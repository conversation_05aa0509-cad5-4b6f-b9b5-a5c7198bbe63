<div align="center"><h1>AREX-UI</h1></div>
<div align="center">A user-friendly visual frontend interface for operating AREX.</div>

## Get Started

1. Installation

  Please refer to [install documentation](http://arextest.com/docs/chapter1/quick-installation).

2. Getting started

  Please visit our documentation website to get started: [AREX Documentions](http://arextest.com/docs/chapter1/get-started)

## FEATURE
- **Code no-invasion based data collection and automation Mock**: mock most components such as Dubbo, Http, Redis, persistence layer framework, configuration center;

- **Support a variety of complex business scenarios of verification**: including multi-threaded concurrency, asynchronous callbacks, write operations and so on;

- **Rapid reproduction of production bugs**: directly use the production recorded data in the local environment.


## Join the AREX Community

- QQ Group - 656108079
- Follow us on [Twitter](https://twitter.com/AREX_Test)
- [Join the Mailing List](https://groups.google.com/g/arex-test)
- [Join <PERSON><PERSON> Slack](https://arexcommunity.slack.com/ssb/redirect)
- [Join <PERSON>EX Discord](https://discord.gg/wy3CZHnV9K)

## LICENSE
```text
Copyright (C) 2022 ArexTest

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU Affero General Public License as
published by the Free Software Foundation, either version 3 of the
License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU Affero General Public License for more details.

You should have received a copy of the GNU Affero General Public License
along with this program. If not, see https://www.gnu.org/licenses/.
```

### 前端本地线上域名调试方案

#### 1，将线上域名流量切换到本地服务端口

[charles rewrite](https://pages.anjukestatic.com/fe/esf/img/a2d7ce9c/921710734215_.pic.jpg)
[charles rewrite](https://pages.anjukestatic.com/fe/esf/img/b9434c00/931710734222_.pic.jpg)

#### 2，将线上后端服务代理到部署的后端沙箱

[后端代理](https://pages.anjukestatic.com/fe/esf/img/c51bba5f/941710734236_.pic.jpg)
