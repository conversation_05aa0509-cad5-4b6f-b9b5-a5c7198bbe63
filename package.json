{"name": "arex", "private": true, "version": "0.6.18", "description": "", "homepage": "https://github.com/arextest/arex", "main": "index.js", "scripts": {"prepare": "husky install", "dev": "pnpm --filter=arex do-dev", "dev:ele": "pnpm --filter=arex dev:ele", "dev:lite": "pnpm --filter=arex-lite do-dev", "build": "pnpm run -r do-build", "server": "cp -r packages/arex/dist packages/arex-server && pnpm --filter=arex-server start", "lint": "prettier --write \"packages/**/*.{ts,tsx}\" && eslint \"packages/**/*.{ts,tsx}\" --fix", "test": "pnpm -r test", "test:run": "pnpm -r test:run", "pre-commit": "pnpm run lint", "rm": "find ./ -type d \\( -name \"dist\" -o -name \"node_modules\" \\) -exec rm -rf {} +", "publish:core": "pnpm publish packages/arex-core --access=public", "publish:request": "pnpm publish packages/arex-request --access=public"}, "keywords": [], "author": "", "license": "ISC", "workspaces": ["./packages/*"], "dependencies": {"@ant-design/pro-table": "^3.18.2", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "antd": "^5.22.1", "copy-to-clipboard": "^3.3.3", "monaco-editor": "^0.39.0", "react-router-dom": "^6.22.2", "shellwords": "^1.0.1"}, "devDependencies": {"@emotion/eslint-plugin": "^11.10.0", "@types/node": "^18.15.12", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^7.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.0", "prettier": "^2.8.8", "react-router-dom": "^6.9.0", "vitest": "^0.33.0"}}