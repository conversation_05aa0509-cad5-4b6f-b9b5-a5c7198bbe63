{"http": {"urlEmpty": "Please input url", "extensionNotInstalled": "Extension not installed", "extensionIncorrect": "Chrome Extension version is incorrect, please install", "enterRequestUrl": "Enter request URL", "importUrl": "Import URL", "showCode": "Show Code", "clearAll": "Clear All", "untitledRequest": "Untitled Request", "copyLink": "Copy Link", "viewMyLinks": "View My Links", "saveAs": "Save As", "params": "Params", "requestBody": "Body", "requestHeaders": "Headers", "responseHeaders": "Response Headers", "result": "Result", "authorization": "Authorization", "pre-requestScript": "Pre-request Script", "test": "Test", "queryParams": "QueryParams", "contentType": "ContentType", "responseBody": "Response Body", "responseNotReady": "Enter the URL and click Send to get a response", "testPassed": "test passed", "testFailed": "test failed", "javaScriptCode": "JavaScript Code", "add_script_block": "Add Script Block", "compare_config": "CompareConfig", "add_tag": "Add Tag"}, "authorization": {"type": "Type", "type_parent": "Inherit auth from parent", "header_description": "This authorization method will be used for every request in this folder. You can override this by specifying one in the request.", "footer_description": "The authorization header will be automatically generated when you send the request."}, "collectionMenu": {"collection": "Collection", "environment": "Environment", "newCreate": "New"}, "applicationsMenu": {"filterFavoriteApps": "Filter <PERSON> Apps", "appFilterPlaceholder": "Search Applications"}, "collection": {"create_new": "Create New", "batch_run": "Batch Run", "batch_compare": "Batch Compare", "new_collection": "New Collection", "new_request": "New Request", "new_case": "New Case", "new_folder": "New Folder", "add_folder": "Add Folder", "add_request": "Add Request", "add_case": "Add Case", "rename": "<PERSON><PERSON>", "duplicate": "Duplicate", "delete": "Delete"}, "replay": {"selectApplication": "Please select an application", "refresh": "refresh", "startSuccess": "Started Successfully", "startFailed": "Start Failed", "startButton": "Start replay", "targetHost": "Target Host", "caseStartTime": "Start Time", "caseEndTime": "End Time", "emptyHost": "Target Host can't be empty", "emptyStartTime": "Start Time can't be empty", "emptyEndTime": "End Time can't be empty", "operation": "operation", "replayReportName": "Report", "state": "State", "all": "All", "passed": "Passed", "failed": "Failed", "invalid": "Invalid", "blocked": "Blocked", "executor": "Executor", "replayStartTime": "Replay Start Time", "report": "Report", "basicInfo": "Basic Information", "replayPassRate": "Replay Pass Rate", "passRate": "Pass Rate", "apiPassRate": "API Pass Rate", "reportName": "Report Name", "recordVersion": "Record version", "replayVersion": "Replay version", "caseRange": "Case range", "totalCases": "Total Cases", "planItemID": "Plan Item ID", "api": "API", "timeConsumed": "Time consumed(s)", "cases": "Cases", "action": "Action", "rerun": "Run Again", "abort": "Abort", "abortTheCase": "Abort the Case", "confirmAbortCase": "Are you sure to abort this case?", "terminate": "Terminate", "terminateTheReplay": "Terminate the Replay", "confirmTerminateReplay": "Are you sure to terminate this replay?", "delete": "Delete", "deleteTheReport": "Delete the Report", "confirmDeleteReport": "Are you sure to delete this report?", "init": "init", "running": "running", "done": "done", "interrupted": "interrupted", "cancelled": "cancelled", "unknownState": "Unknown State", "diffScenes": "DiffScenes", "diffScenesNew": "DiffScenes(New)", "case": "Case", "recordId": "Record ID", "replayId": "Replay ID", "status": "Status", "detail": "Detail", "save": "Save", "recordDetail": "Record Detail", "success": "Success", "caseServiceAPI": "Main Service API", "viewFailedOnly": "View Failed Only", "saveCase": "Save Case", "cancel": "Cancel", "create": "Create", "saveTo": "Save to", "caseName": "Case name", "emptyCaseName": "Please input case name!", "emptyTitle": "Please input the title of collection!", "selectTree": "Please select", "benchmark": "Benchmark", "test": "Test", "pointOfDifference": "Point of difference", "sceneCount": "Scene Count", "caseErrorCount": "Error Count", "issues": "issue(s)", "treeMode": "Tree Mode", "valueOf": "Value of", "isDifferenceExcepted": ",excepted", "actual": ", actual", "unknown": "Unknown", "baseMissing": "Base Missing", "testMissing": "Test Missing", "leftMissing": "Left Missing", "leftMissingDesc": "is missing on the left", "rightMissing": "Right Missing", "rightMissingDesc": "is missing on the right", "differentValue": "Different Value", "ignoreNode": "Ignore Node", "escExit": "Press Esc to exit", "addIgnoreSuccess": "Ignore node successfully", "diffMatch": "Diff Match", "replayEndTime": "ReplayEndTime"}, "appSetting": {"record": "Record", "replay": "Replay", "importYaml": "Import Yaml", "nodesIgnore": "Nodes Ignore", "nodesSort": "Nodes Sort", "agentVersion": "Agent Version", "agentHost": "Agent Host", "basic": "Basic", "duration": "Duration", "period": "Period", "frequency": "Frequency", "advanced": "Advanced", "timeMock": "Time Mock", "dynamicClasses": "Dynamic Classes", "excludeServiceOperationSet": "Operation", "collectCoveragePackages": "Package Prefix", "serializationType": "Serialization Type", "enableDebugIp": "Debug IP", "fullClassName": "Full Class Name", "fullClassNamePlaceholder": "Full class mame", "methodName": "Method Name", "methodNamePlaceholder": "Method name (if not configured, mock all public methods with parameters in this class)", "parameterTypes": "Parameter Types", "parameterTypesPlaceholder": "Fully qualified class name parameters, separated by \"@\" if there are multiple, for example: <EMAIL>", "action": "Action", "delConfirmText": "Are you sure to delete this record?", "caseRange": "Case range", "excludeOperation": "Exclude Operation", "path": "Path", "value": "Value", "updateSuccess": "update success", "global": "Global", "interfaces": "Interfaces", "editArea": "Edit Area (Click interface to start)", "clickToIgnore": "click node to ignore", "dataStructure": "Data Structure", "emptyResponse": "Empty Response", "configResponse": "Config Response", "addKey": "Add Key", "editResponse": "Edit Response", "addSortKey": "Add Sort Key", "noSortNodes": "No Sort Nodes", "keys": "keys", "chooseOneNode": "choose one array node", "choosekey": "choose key to sort", "arrayTree": "A<PERSON>yTree", "sortTree": "SortTree", "emptyKey": "Please enter ignored key", "noIgnoredNodes": "No Ignored Nodes", "emptyFullClassName": "Please enter full class name", "emptyCaseRange": "Please input your case range!", "emptyValue": "Please enter value", "mockTips": "When \"body\" is a json string, it will be automatically converted into a json object, and restored when saving."}, "env": {"searchEnvironment": "Search Environment", "setCurrentEnv": "Set as current environment", "duplicateCurrentEnv": "Duplicate current environment", "createEnvKeyValue": "Create environment keyValue", "delConfirmText": "Deleting this environment might cause any monitors or mock servers using it to stop functioning properly. Are you sure you want to continue?", "variable": "Variable", "key": "Key", "value": "Value"}, "workSpace": {"workSpace": "Workspace", "noPermissionOrInvalid": "接口鉴权失效，重新获取中...", "emptySpaceName": "Please input the name of workspace!", "createSuccess": "create workspace successfully", "importSuccess": "Import success!", "importFailed": "Import Fail!", "add": "addWorkspace", "edit": "Edit Workspace", "import": "Import", "selectFile": "Select File", "importCollection": "Import collection", "collections": "Collections", "overview": "Overview", "labels": "Labels", "workspaceSettings": "Workspace settings", "name": "Name", "emptyName": "Please input your name!", "update": "Update", "noInvitedUser": "no invited user", "admin": "Admin", "editor": "Editor", "viewer": "Viewer", "notAccepted": "Not accepted", "accepted": "Accepted", "del": "Delete workspace", "delMessage": "Once deleted, a workspace is gone forever along with its data.", "delConfirmText": "Are you sure to delete this workspace?", "labelName": "LabelName", "color": "Color", "delLabelConfirmText": "Are you sure to delete this label?", "addLabelButton": "New Label", "labelId": "Id"}}