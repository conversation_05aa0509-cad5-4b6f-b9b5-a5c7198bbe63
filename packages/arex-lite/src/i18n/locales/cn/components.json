{"http": {"urlEmpty": "请输入 URL", "extensionNotInstalled": "插件未安装", "extensionIncorrect": "Chrome Extension 版本不正确，请安装", "enterRequestUrl": "请输入请求 URL", "importUrl": "导入 URL", "showCode": "显示代码", "clearAll": "全部清除", "untitledRequest": "未命名请求", "copyLink": "复制链接", "viewMyLinks": "我的链接", "saveAs": "另存为", "params": "参数", "requestBody": "请求体", "requestHeaders": "请求头", "responseHeaders": "响应头", "result": "结果", "authorization": "授权", "pre-requestScript": "预请求脚本", "test": "测试", "queryParams": "查询参数", "contentType": "内容类型", "responseBody": "响应体", "responseNotReady": "请求未发送，当前无响应", "testPassed": "测试成功", "testFailed": "测试失败", "javaScriptCode": "JavaScript 代码", "add_script_block": "添加脚本块", "compare_config": "比对配置", "add_tag": "添加标签"}, "authorization": {"type": "类型", "type_parent": "继承父类授权", "header_description": "此授权方法将用于此文件夹中的每个请求，你可以在一个具体的请求中重写。", "footer_description": "授权头将在发送请求时自动生成。"}, "collectionMenu": {"collection": "集合", "environment": "环境", "newCreate": "新建"}, "applicationsMenu": {"filterFavoriteApps": "收藏应用列表", "appFilterPlaceholder": "搜索应用"}, "collection": {"create_new": "新建集合", "batch_run": "批量执行", "batch_compare": "批量比对", "new_collection": "新集合", "new_request": "新请求", "new_case": "新用例表", "new_folder": "新文件夹", "add_folder": "添加文件夹", "add_request": "添加请求", "add_case": "添加用例", "rename": "重命名", "duplicate": "拷贝", "delete": "删除"}, "replay": {"selectApplication": "请选择一个应用", "refresh": "刷新", "startSuccess": "启动成功", "startFailed": "启动失败", "startButton": "启动回放", "targetHost": "目标回放环境", "caseStartTime": "Case 开始时间", "caseEndTime": "Case 结束时间", "emptyHost": "目标回放环境不能为空", "emptyStartTime": "Case 开始时间不能为空", "emptyEndTime": "Case 结束时间不能为空", "operation": "operation", "replayReportName": "任务名称", "state": "状态", "all": "全部", "passed": "成功", "failed": "失败", "invalid": "无效", "blocked": "待执行", "executor": "执行人", "replayStartTime": "开始时间", "report": "报告", "basicInfo": "基本信息", "replayPassRate": "回放通过率", "passRate": "Case 通过率", "apiPassRate": "API 通过率", "reportName": "任务名称", "recordVersion": "录制版本", "replayVersion": "回放版本", "caseRange": "任务时间", "totalCases": "Case 数", "planItemID": "子任务 ID", "api": "API", "timeConsumed": "耗时(秒)", "cases": "case 数", "action": "操作", "rerun": "重新执行", "init": "初始化", "abort": "终止", "abortTheCase": "终止任务", "confirmAbortCase": "确定终止该任务?", "terminate": "终止", "terminateTheReplay": "终止回放", "confirmTerminateReplay": "确定终止该回放?", "delete": "删除", "deleteTheReport": "删除报告", "confirmDeleteReport": "确定删除该报告?", "running": "进行中", "done": "完成", "interrupted": "异常终止", "cancelled": "取消", "unknownState": "未知", "diffScenes": "差异对比", "diffScenesNew": "DiffScenes(新)", "case": "全量 Case", "recordId": "录制 ID", "replayId": "回放 ID", "status": "状态", "detail": "详情", "save": "保存", "recordDetail": "录制详情", "success": "成功", "caseServiceAPI": "接口名称", "viewFailedOnly": "只展示失败", "saveCase": "保存", "cancel": "关闭", "create": "保存", "saveTo": "保存至 ", "caseName": "Case 名称", "emptyCaseName": "请输入 Case 名称!", "emptyTitle": "请输入 Collection 名称!", "selectTree": "请选择保存路径", "benchmark": "录制", "test": "回放", "pointOfDifference": "差异点", "sceneCount": "差异数", "caseErrorCount": "错误数", "issues": "差异", "treeMode": "报文详情", "valueOf": "节点 ", "isDifferenceExcepted": "，期望", "actual": ", 实际", "unknown": "未知", "baseMissing": "录制缺失", "testMissing": "测试未调", "leftMissing": "录制为空", "leftMissingDesc": "录制结果为空", "rightMissing": "回放为空", "rightMissingDesc": "回放结果为空", "differentValue": "不同点", "ignoreNode": "忽略节点", "escExit": "按 ESC 退出", "addIgnoreSuccess": "添加忽略成功", "diffMatch": "差异展示", "replayEndTime": "结束时间"}, "appSetting": {"record": "录制", "replay": "回放", "importYaml": "Yaml 配置导入", "nodesIgnore": "忽略节点配置", "nodesSort": "数组节点配置", "agentVersion": "Agent <PERSON>", "agentHost": "Agent 地址", "basic": "基础", "duration": "录制周期", "period": "录制时间", "frequency": "录制频率", "advanced": "高级", "timeMock": "Mo<PERSON> 时间", "dynamicClasses": "动态类", "excludeServiceOperation": "不录制接口集合", "collectCoveragePackages": "<PERSON><PERSON> 包前缀", "serializationType": "序列化方式", "enableDebugIp": "Debug IP 集合", "fullClassName": "全类名", "fullClassNamePlaceholder": "全类名 FullClassName", "methodName": "方法名", "methodNamePlaceholder": "方法名(不配置则 mock 该类中的全部 Public 有参方法)", "parameterTypes": "参数类型", "parameterTypesPlaceholder": "全类名参数,多个用@隔开,例 <EMAIL>", "delConfirmText": "是否删除这条记录？", "caseRange": "Case 数量上限", "excludeOperation": "不录制依赖", "path": "路径", "value": "值", "updateSuccess": "更新成功", "global": "全局", "interfaces": "接口", "editArea": "修改区（点击接口开始）", "clickToIgnore": "点击添加忽略节点", "dataStructure": "数据结构", "emptyResponse": "响应为空", "configResponse": "配置响应", "addKey": "添加节点", "editResponse": "修改响应", "addSortKey": "添加 List 节点", "noSortNodes": "List 节点为空", "keys": "节点", "chooseOneNode": "选择一个数组", "chooseOnekey": "请选择标识节点", "arrayTree": "数组节点", "sortTree": "关键标识节点", "emptyKey": "请输入忽略节点", "noIgnoredNodes": "忽略节点为空", "emptyFullClassName": "请输入全类名", "emptyCaseRange": "请输入 Case 数量上限!", "emptyValue": "请输入值", "mockTips": "\"body\"为json字符串时会自动转换成json对象，保存时还原。"}, "env": {"searchEnvironment": "搜索环境", "setCurrentEnv": "默认为当前环境", "duplicateCurrentEnv": "复制当前环境配置", "createEnvKeyValue": "创建环境变量", "delConfirmText": "删除此环境可能会导致使用它的任何监视器或 MOCK 服务停止工作。你确定要继续吗？", "variable": "变量名称", "key": "键", "value": "值"}, "workSpace": {"workSpace": "工作空间", "noPermissionOrInvalid": "无目标工作空间权限或目标工作空间无效", "emptySpaceName": "请输入名称!", "createSuccess": "创建成功", "importSuccess": "导入成功!", "importFailed": "导入失败!", "add": "新增工作空间", "edit": "修改工作空间", "import": "导入", "selectFile": "选择文件", "importCollection": "导入集合", "collections": "集合", "overview": "概览", "labels": "标签", "workspaceSettings": "设置", "name": "名称", "emptyName": "请输入名称!", "update": "更新", "noInvitedUser": "无受邀用户", "admin": "管理员", "editor": "编辑者", "viewer": "观察者", "notAccepted": "未被接受", "accepted": "已接受", "del": "删除工作空间", "delMessage": "一旦删除，工作区和它的数据就会永远消失.", "delConfirmText": "是否删除该工作空间？", "labelName": "标签名称", "color": "颜色", "delLabelConfirmText": "是否删除该标签", "addLabelButton": "新增标签", "labelId": "序号"}}