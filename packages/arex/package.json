{"name": "arex", "private": true, "version": "0.6.18", "author": "arextest", "main": "dist-electron/main", "files": ["./build/**/*", "./public/electron.js"], "scripts": {"test": "vitest", "test:run": "vitest --run", "do-dev": "vite", "dev:ele": "vite --mode electron", "do-build": "vite build", "build:ele": "vite build --mode electron && electron-builder", "publish": "vite build --mode electron && electron-builder --mac --win -p always"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-table": "^3.18.2", "@arextest/arex-core": "*", "@arextest/arex-request": "*", "@arextest/arex-request-runtime": "^1.0.2", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@formkit/auto-animate": "^0.7.0", "@monaco-editor/react": "^4.6.0", "@types/jszip": "^3.4.1", "@types/react-resizable": "^3.0.8", "ahooks": "^3.7.5", "allotment": "^1.18.1", "antd": "^5.11.0", "antd-img-crop": "^4.12.2", "axios": "^1.3.6", "body-parser": "^1.20.2", "buffer": "^6.0.3", "chart.js": "^4.2.1", "cors": "^2.8.5", "dayjs": "^1.11.7", "express": "^4.18.2", "fzstd": "^0.1.0", "http-proxy-middleware": "^2.0.6", "immer": "^9.0.19", "json-bigint": "^1.0.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "lossless-json": "^4.0.1", "lucide-react": "^0.299.0", "monaco-editor": "^0.39.0", "path-to-regexp": "^6.2.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-countup": "^6.4.2", "react-d3-tree": "^3.5.2", "react-dom": "^18.2.0", "react-json-view": "^1.21.3", "react-markdown": "^10.1.0", "react-resizable": "^3.0.5", "react-router-dom": "^6.9.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "shellwords": "^1.0.1", "umi-request": "^1.0.8", "use-immer": "^0.8.1", "xspy": "^0.0.4", "zustand": "^4.3.7"}, "devDependencies": {"@svgr/core": "^8.0.0", "@svgr/plugin-jsx": "^8.0.1", "@types/express": "^4.17.21", "@types/json-bigint": "^1.0.4", "@types/lodash": "^4.14.194", "@types/react": "^18.2.0", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react-swc": "^3.3.0", "electron": "^28.1.4", "electron-builder": "^24.9.1", "electron-log": "^4.4.8", "electron-updater": "^6.1.4", "typescript": "^5.3.0", "vite": "^4.2.0", "vite-plugin-electron": "^0.12.0", "vite-plugin-electron-renderer": "^0.14.5", "vite-plugin-svgr": "^3.2.0"}}