import { LoadingOutlined } from '@ant-design/icons';
import { Are<PERSON><PERSON><PERSON><PERSON>rovider, ArexMenuManager, ArexPaneManager, Theme } from '@arextest/arex-core';
import { Spin } from 'antd';
import React from 'react';

import AIAssistantContainer from './components/AIAssistant/AIAssistantContainer';
import AIAssistantContextProvider from './components/AIAssistant/context/AIAssistantContext';
import AIFloatingButton from './components/AIAssistant/FloatingButton';
import { useAuthentication, useTrace } from './hooks';
import useDarkMode from './hooks/useDarkMode';
import resources from './i18n';
import Menus from './menus';
import Panes from './panes';
import Routes from './router';
import { REDIRECT_LOGOUT } from './services/LoginService/constants';
import { useUserProfile } from './store';
import GlobalStyle from './style/GlobalStyle';

// set default loading indicator
Spin.setDefaultIndicator(<LoadingOutlined spin />);

// register menus and panes
ArexPaneManager.registerPanes(Panes);
ArexMenuManager.registerMenus(Menus);

const App = () => {
  // useTrace('http://trace.arextest.com:8080/graphql');
  // if(REDIRECT_LOGOUT != window.location.href){
  //   //退出就不使用use hook函数
  //   console.log("REDIRECT_LOGOUT --》" + REDIRECT_LOGOUT);
  //   console.log("当前路由 --》" + window.location.href);
  //   useAuthentication();
  // }
  useAuthentication();
  const { theme: _theme, compact, colorPrimary, language } = useUserProfile();
  const darkMode = useDarkMode();
  const theme = darkMode ? Theme.dark : Theme.light;
  return (
    <ArexCoreProvider
      theme={theme}
      language={language}
      localeResources={resources}
      compact={compact}
      colorPrimary={colorPrimary}
    >
      <AIAssistantContextProvider>
        <GlobalStyle>
          <Routes />
          <AIAssistantContainer />
        </GlobalStyle>
      </AIAssistantContextProvider>
    </ArexCoreProvider>
  );
};

export default App;
