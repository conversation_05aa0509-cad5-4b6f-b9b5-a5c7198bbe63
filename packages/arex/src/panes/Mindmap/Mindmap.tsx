import { DownloadOutlined } from '@ant-design/icons';
import { ArexPaneFC, EmptyWrapper, useArexPaneProps, useTranslation } from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import { Button, Card, Dropdown, Menu } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Tree from 'react-d3-tree';

import { CollectionNodeType, PanesType } from '@/constant';
import { useNavPane } from '@/hooks';
import { FileSystemService } from '@/services';
import { CollectionType } from '@/services/FileSystemService';
import { useWorkspaces } from '@/store';

interface TreeNode {
  name: string;
  children?: TreeNode[];
  attributes?: {
    nodeType: CollectionNodeType;
    method?: string;
    requestType?: string;
    infoId?: string;
    caseSourceType?: number;
    hasChildren?: boolean;
    isExpanded?: boolean;
  };
}

/**
 * Mindmap 组件 - 用于展示和操作脑图结构的可视化组件
 *
 * 主要功能：
 * 1. 从 workspace 数据生成树形结构
 * 2. 支持节点展开/折叠操作
 * 3. 支持点击接口和用例节点跳转到对应详情页
 * 4. 支持自定义节点渲染和样式
 *
 * 特性：
 * - 自动展开所有节点
 * - 支持从指定节点开始展示子树
 * - 不同类型的节点有不同的颜色和图标标识
 * - 支持多语言标题显示
 *
 * @param props 组件属性，包含 workspaceId
 * @returns 返回脑图可视化组件
 */
const Mindmap: ArexPaneFC<{ workspaceId: string }> = (props) => {
  const { data } = useArexPaneProps();
  const workspaceId = (data as any)?.workspaceId;
  const nodeId = (data as any)?.nodeId;
  const nodeName = (data as any)?.nodeName;
  const { t } = useTranslation(['components']);
  const navPane = useNavPane();
  const { activeWorkspaceId } = useWorkspaces();

  // 展开状态管理
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // 初始化展开根节点
  const { data: workspaceData, loading } = useRequest(
    () => FileSystemService.queryWorkspaceById({ id: workspaceId }),
    {
      refreshDeps: [workspaceId],
    },
  );

  // 递归收集所有节点ID
  const getAllNodeIds = (nodes: CollectionType[]): string[] => {
    const ids: string[] = [];
    nodes.forEach((node) => {
      ids.push(node.infoId);
      if (node.children && node.children.length > 0) {
        ids.push(...getAllNodeIds(node.children));
      }
    });
    return ids;
  };

  // 初始化展开所有节点
  useEffect(() => {
    if (workspaceData?.roots && expandedNodes.size === 0) {
      const allNodeIds = getAllNodeIds(workspaceData.roots);
      setExpandedNodes(new Set(allNodeIds));
    }
  }, [workspaceData, expandedNodes.size]);

  // 切换节点展开状态
  const toggleNodeExpansion = useCallback((nodeId: string) => {
    setExpandedNodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 将 CollectionType 转换为 react-d3-tree 需要的格式
  const convertToTreeData = (nodes: CollectionType[]): TreeNode[] => {
    return nodes.map((node) => {
      const hasChildren = node.children && node.children.length > 0;
      const isExpanded = expandedNodes.has(node.infoId);

      return {
        name: decodeURIComponent(node.nodeName || ''),
        attributes: {
          nodeType: node.nodeType,
          method: node.method || undefined,
          requestType: node.requestType || undefined,
          infoId: node.infoId,
          caseSourceType: node.caseSourceType,
          hasChildren,
          isExpanded,
        },
        // 只有在展开状态下才显示子节点
        children: hasChildren && isExpanded ? convertToTreeData(node.children!) : undefined,
      };
    });
  };

  // 递归查找指定ID的节点
  const findNodeById = (nodes: CollectionType[], targetId: string): CollectionType | null => {
    for (const node of nodes) {
      if (node.infoId === targetId) {
        return node;
      }
      if (node.children) {
        const found = findNodeById(node.children, targetId);
        if (found) return found;
      }
    }
    return null;
  };

  const treeData = useMemo(() => {
    if (!workspaceData?.roots) return [];

    // 如果指定了nodeId，查找对应的节点并展示其子树
    if (nodeId) {
      const targetNode = findNodeById(workspaceData.roots, nodeId);
      if (targetNode) {
        // 如果找到目标节点，展示以该节点为根的子树
        return convertToTreeData([targetNode]);
      }
    }

    // 否则展示整个根节点树
    return convertToTreeData(workspaceData.roots);
  }, [workspaceData, nodeId, expandedNodes]);

  // 处理节点点击事件
  const handleNodeClick = (nodeDatum: any) => {
    const { nodeType, infoId, caseSourceType } = nodeDatum.attributes || {};

    // 只处理interface和case类型的节点
    if (nodeType === CollectionNodeType.interface || nodeType === CollectionNodeType.case) {
      const icon =
        nodeType === CollectionNodeType.interface
          ? nodeDatum.attributes?.method || undefined
          : nodeType === CollectionNodeType.case
          ? caseSourceType === 1
            ? 'arex'
            : 'case'
          : undefined;

      navPane({
        type: PanesType.REQUEST,
        id: `${activeWorkspaceId}-${nodeType}-${infoId}`,
        name: nodeDatum.name,
        icon,
      });
    }
  };

  // 自定义节点渲染
  const renderCustomNodeElement = ({ nodeDatum }: any) => {
    const getNodeColor = () => {
      switch (nodeDatum.attributes?.nodeType) {
        case CollectionNodeType.folder:
          return '#1890ff';
        case CollectionNodeType.interface:
          return '#52c41a';
        case CollectionNodeType.case:
          return '#faad14';
        default:
          return '#d9d9d9';
      }
    };

    const getNodeIcon = () => {
      switch (nodeDatum.attributes?.nodeType) {
        case CollectionNodeType.folder:
          return '📁';
        case CollectionNodeType.interface:
          return '🌐';
        case CollectionNodeType.case:
          return '📄';
        default:
          return '⚪';
      }
    };

    const isClickable =
      nodeDatum.attributes?.nodeType === CollectionNodeType.interface ||
      nodeDatum.attributes?.nodeType === CollectionNodeType.case;

    const hasChildren = nodeDatum.attributes?.hasChildren;
    const isExpanded = nodeDatum.attributes?.isExpanded;

    const handleExpandClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (hasChildren && nodeDatum.attributes?.infoId) {
        toggleNodeExpansion(nodeDatum.attributes.infoId);
      }
    };

    return (
      <g>
        {/* 主节点 */}
        <g
          onClick={() => handleNodeClick(nodeDatum)}
          style={{ cursor: isClickable ? 'pointer' : 'default' }}
        >
          <circle
            r={15}
            fill={getNodeColor()}
            stroke={isClickable ? '#1890ff' : 'none'}
            strokeWidth={isClickable ? '2' : '0'}
          />
          <text fill='white' strokeWidth='0' x='0' y='5' textAnchor='middle' fontSize='12'>
            {getNodeIcon()}
          </text>
        </g>

        {/* 展开/收起按钮 */}
        {hasChildren && (
          <g onClick={handleExpandClick} style={{ cursor: 'pointer' }}>
            <circle cx={20} cy={-10} r={8} fill='#f0f0f0' stroke='#d9d9d9' strokeWidth='1' />
            <text x={20} y={-6} textAnchor='middle' fontSize='10' fill='#666' fontWeight='bold'>
              {isExpanded ? '−' : '+'}
            </text>
          </g>
        )}

        {/* 节点标签 */}
        <text
          fill='black'
          strokeWidth='0'
          x='0'
          y='35'
          textAnchor='middle'
          fontSize='12'
          fontWeight='bold'
        >
          {nodeDatum.name}
        </text>
        {nodeDatum.attributes?.method && (
          <text fill='#666' strokeWidth='0' x='0' y='50' textAnchor='middle' fontSize='10'>
            {nodeDatum.attributes.method}
          </text>
        )}
        {/* {isClickable && (
          <text fill='#1890ff' strokeWidth='0' x='0' y='65' textAnchor='middle' fontSize='9'>
            点击打开
          </text>
        )} */}
      </g>
    );
  };

  if (loading) {
    return (
      <EmptyWrapper loading={loading}>
        <div style={{ height: '400px' }} />
      </EmptyWrapper>
    );
  }

  if (!treeData.length) {
    return (
      <EmptyWrapper empty>
        <div style={{ height: '400px' }} />
      </EmptyWrapper>
    );
  }

  // 将TreeNode数据转换为OPML格式
  const convertTreeDataToOPML = (nodes: TreeNode[]): string => {
    const convertNodeToOutline = (node: TreeNode, depth: number = 0): string => {
      const indent = '\t'.repeat(depth + 2);
      const nodeName = node.name;

      // 构建_url属性，包含导航链接
      let urlContent = '';
      let typeAttr = '';
      if (
        node.attributes?.nodeType === CollectionNodeType.interface ||
        node.attributes?.nodeType === CollectionNodeType.case
      ) {
        const { nodeType, infoId, caseSourceType } = node.attributes;
        const icon =
          nodeType === CollectionNodeType.interface
            ? node.attributes?.method || undefined
            : nodeType === CollectionNodeType.case
            ? caseSourceType === 1
              ? 'arex'
              : 'case'
            : undefined;

        // 生成完整的URL链接
        const navInfo = `${activeWorkspaceId}-${nodeType}-${infoId}`;
        const protocol = window.location.protocol;
        const host = window.location.host;
        urlContent = `${protocol}//${host}/collection/request/${navInfo}`;
        typeAttr = ' type="link"';
      }

      const urlAttr = urlContent ? ` _url="${urlContent}"` : '';
      const combinedAttrs = urlAttr + typeAttr;

      if (node.children && node.children.length > 0) {
        const childrenOutlines = node.children
          .map((child) => convertNodeToOutline(child, depth + 1))
          .join('\n');
        return `${indent}<outline text="${nodeName}"${combinedAttrs}>\n${childrenOutlines}\n${indent}</outline>`;
      } else {
        return `${indent}<outline text="${nodeName}"${combinedAttrs}></outline>`;
      }
    };

    const currentDate = new Date().toString();
    const outlines = nodes.map((node) => convertNodeToOutline(node)).join('\n');

    return `<?xml version="1.0" encoding="UTF-8"?>\n<opml version="1.0">\n\t<head>\n\t\t<dateCreated>${currentDate}</dateCreated>\n\t\t<dateModified>${currentDate}</dateModified>\n\t</head>\n\t<body>\n${outlines}\n\t</body>\n</opml>`;
  };

  // 将TreeNode数据转换为XMind格式
  const convertTreeDataToXMind = (nodes: TreeNode[]): string => {
    const convertNodeToTopic = (node: TreeNode, isRoot: boolean = false): string => {
      const nodeId = `topic_${Math.random().toString(36).substr(2, 9)}`;
      const nodeName = node.name;

      // 构建topic开始标签，包含链接信息
      let topicStartTag = `<topic id="${nodeId}" timestamp="${Date.now()}"`;

      // 添加链接信息到topic元素属性中
      if (node.attributes?.nodeType === CollectionNodeType.interface || node.attributes?.nodeType === CollectionNodeType.case) {
        const { nodeType, infoId } = node.attributes;
        const navInfo = `${activeWorkspaceId}-${nodeType}-${infoId}`;
        const protocol = window.location.protocol;
        const host = window.location.host;
        const url = `${protocol}//${host}/collection/request/${navInfo}`;

        // 使用XMind标准的XLink格式
        topicStartTag += ` xlink:href="${url}"`;
      }

      topicStartTag += '>';
      let topicContent = topicStartTag;
      topicContent += `<title>${nodeName}</title>`;

      if (node.children && node.children.length > 0) {
        topicContent += '<children><topics type="attached">';
        node.children.forEach(child => {
          topicContent += convertNodeToTopic(child, false);
        });
        topicContent += '</topics></children>';
      }

      topicContent += '</topic>';
      return topicContent;
    };

    // 如果只有一个根节点，直接使用它作为主题；如果有多个根节点，需要创建一个容器
    let mainContent;
    if (nodes.length === 1) {
      mainContent = convertNodeToTopic(nodes[0], true);
    } else {
      const rootTopics = nodes.map(node => convertNodeToTopic(node, false)).join('');
      mainContent = `<topic id="root" timestamp="${Date.now()}">
      <title>Mindmap</title>
      <children>
        <topics type="attached">
          ${rootTopics}
        </topics>
      </children>
    </topic>`;
    }

    return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xmap-content xmlns="urn:xmind:xmap:xmlns:content:2.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:xmind:xmap:xmlns:content:2.0 http://www.xmind.net/schemas/2008/content.xsd" version="2.0">
  <sheet id="sheet1" timestamp="${Date.now()}">
    <title>Mindmap</title>
    ${mainContent}
  </sheet>
</xmap-content>`;
  };

  // 处理OPML导出
  const handleExportOPML = () => {
    if (!treeData.length) return;

    const opmlContent = convertTreeDataToOPML(treeData);
    const blob = new Blob([opmlContent], { type: 'application/xml' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `mindmap-${Date.now()}.opml`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 处理XMind导出
  const handleExportXMind = async () => {
    if (!treeData.length) return;

    try {
      // 动态导入JSZip
      const JSZip = (await import('jszip')).default;

      const zip = new JSZip();

      // 创建XMind内容文件
      const xmindContent = convertTreeDataToXMind(treeData);
      zip.file('content.xml', xmindContent);

      // 创建META-INF/manifest.xml
      const manifestContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0">
  <file-entry full-path="content.xml" media-type="text/xml"/>
  <file-entry full-path="META-INF/" media-type=""/>
  <file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
</manifest>`;
      zip.folder('META-INF')?.file('manifest.xml', manifestContent);

      // 生成ZIP文件
      const content = await zip.generateAsync({ type: 'blob' });

      const url = URL.createObjectURL(content);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mindmap-${Date.now()}.xmind`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('导出XMind文件失败:', error);
    }
  };

  const getTitle = () => {
    const baseTitle = t('collection.mindmap_preview', { ns: 'components' }) || '脑图预览';
    // 对 nodeName 进行URL解码，处理空格等特殊字符
    const decodedNodeName = nodeName ? decodeURIComponent(nodeName) : '';
    return decodedNodeName ? `${baseTitle} - ${decodedNodeName}` : baseTitle;
  };

  const getTitleExtra = () => {
    const exportMenu = (
      <Menu>
        <Menu.Item key='xmind' onClick={handleExportXMind}>
          导出XMind格式
        </Menu.Item>
        <Menu.Item key='opml' onClick={handleExportOPML}>
          导出OPML格式
        </Menu.Item>
      </Menu>
    );

    return (
      <Dropdown overlay={exportMenu} placement='bottomRight'>
        <Button type='primary' icon={<DownloadOutlined />} size='small'>
          导出
        </Button>
      </Dropdown>
    );
  };

  return (
    <Card title={getTitle()} extra={getTitleExtra()} style={{ height: '100%' }}>
      <div style={{ height: '600px', width: '100%' }}>
        <Tree
          data={treeData}
          orientation='horizontal'
          translate={{ x: 100, y: 300 }}
          renderCustomNodeElement={renderCustomNodeElement}
          separation={{ siblings: 1, nonSiblings: 1 }}
          nodeSize={{ x: 120, y: 150 }}
          zoom={0.8}
          enableLegacyTransitions
        />
      </div>
    </Card>
  );
};

export default Mindmap;
