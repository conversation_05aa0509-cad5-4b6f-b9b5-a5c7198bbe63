import { PlusOutlined } from '@ant-design/icons';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { useRequest } from 'ahooks';
import { Button, message } from 'antd';
import React, { FC, useEffect, useMemo, useState } from 'react';

import FilterRuleSetting from '@/panes/AppSetting/FilterRule/components/FilterRuleSetting';
import { ApplicationService, FilterConfigurationService } from '@/services';
import { filterRuleDetailBody } from '@/services/FilterConfigurationService';
import { queryFilterConfigurations } from '@/services/FilterConfigurationService/getFilterRuleEnum';
import { FilterRuleListBodyData } from '@/services/FilterConfigurationService/getFilterRuleList';

import { defaultFilterConfig } from './index.config';

type FilterRuleProps = {
  appId: string;
  env: string;
};

const FilterRule: FC<FilterRuleProps> = (props) => {
  const [settingWrapper] = useAutoAnimate();
  const [interfaceOptions, updateInterfaceOptions] = useState<any[]>([]);
  const [filterRuleEnumOptions, updateFilterRuleEnumOptions] = useState<any[]>([]);
  const [filterRuleListData, setFilterRuleListData] = useState<FilterRuleListBodyData[]>([]);

  const { run: getRuleList } = useRequest(FilterConfigurationService.getFilterRuleList, {
    onSuccess(res: any) {
      const filterRuleListRes = res;
      setFilterRuleListData(filterRuleListRes);
    },
  });

  useEffect(() => {
    getRuleList({ appId: props.appId });
  }, [props.appId]);

  const { run: addAndUpdateFilterRule } = useRequest(
    FilterConfigurationService.addAndUpdateFilterRule,
    {
      manual: true,
      onSuccess(res: any) {
        if (res) {
          getRuleList({ appId: props.appId });
          message.success('操作成功');
        } else {
          message.success('操作失败');
        }
      },
    },
  );

  const handleAddRuleItem = () => {
    const newRuleList: FilterRuleListBodyData[] = (filterRuleListData || []).concat(
      defaultFilterConfig,
    );
    setFilterRuleListData(newRuleList);
  };

  const handleUpdate = (params: any) => {
    addAndUpdateFilterRule(params);
  };

  const handleCopyAddRule = (rule: any) => {
    const params = {
      filterName: rule.filterName,
      appId: rule.appId,
      serviceId: rule.serviceId,
      operationName: rule.operationName,
      operationId: rule.operationId,
      operator: rule.operator,
      operationType: rule.operationType,
      operationTypes: rule.operationTypes,
      requestFilterRules: rule.requestFilterRules,
      responseFilterRules: rule.responseFilterRules,
      expression: rule.expression,
    };
    addAndUpdateFilterRule(params);
  };

  const { run: copyFilterConfiguration } = useRequest(
    FilterConfigurationService.copyFilterConfiguration,
    {
      defaultParams: [{ filterId: '' }],
      manual: true,
      onSuccess: function (res: filterRuleDetailBody) {
        const copyRes = res;
        if (copyRes) {
          handleCopyAddRule(copyRes);
        } else {
          message.success('复制失败');
        }
      },
    },
  );

  const { run: deleteFilterConfiguration } = useRequest(
    FilterConfigurationService.deleteFilterConfiguration,
    {
      manual: true,
      onSuccess(res: any) {
        const deleteRes = res;
        if (deleteRes) {
          getRuleList({ appId: props.appId });
          message.success('操作成功');
        } else {
          message.success('操作失败');
        }
      },
    },
  );

  const { run: queryInterfacesList } = useRequest(
    () =>
      ApplicationService.queryInterfacesList<'Interface'>({
        appId: props.appId as string,
        env: props.env as string,
      }),
    {
      ready: !!props.appId,
      onSuccess(res) {
        const list = Object.entries(
          res.reduce<Record<string, { label: string; value?: string | null }[]>>(
            (options, item) => {
              (item.operationTypes || [])?.forEach((operation) => {
                const newItem = {
                  label: item.operationName,
                  value: item.id,
                  ...item,
                };
                options[operation]
                  ? options[operation].push(newItem)
                  : (options[operation] = [newItem]);
              });
              return options;
            },
            {},
          ),
        );
        const options = list.map(([label, options]) => ({
          label,
          options,
        }));
        updateInterfaceOptions(options);
      },
    },
  );

  const queryFilterRuleList = () => {
    queryFilterConfigurations().then((res: any) => {
      const arr = [];
      for (const key in res || {}) {
        arr.push({
          label: key,
          value: res[key],
        });
      }
      updateFilterRuleEnumOptions(arr);
    });
  };

  useEffect(() => {
    queryInterfacesList();
    queryFilterRuleList();
  }, []);

  return (
    <>
      <div ref={settingWrapper}>
        <FilterRuleSetting
          interfaceOptions={interfaceOptions}
          filterRuleEnumOptions={filterRuleEnumOptions}
          filterRuleListItem={filterRuleListData}
          onUpdate={handleUpdate}
          onCopy={(operationIdsItem: string) =>
            copyFilterConfiguration({ filterId: operationIdsItem })
          }
          onDelete={(operationIdsItem: string, index: number) => {
            console.log(operationIdsItem, index);
            if (operationIdsItem) {
              deleteFilterConfiguration({ operationIds: operationIdsItem });
            } else {
              (filterRuleListData || []).splice(index, 1);
              setFilterRuleListData([...filterRuleListData]);
            }
          }}
        />
      </div>
      <Button
        block
        type='dashed'
        icon={<PlusOutlined />}
        onClick={handleAddRuleItem}
        style={{ height: 'auto', padding: '16px', marginTop: '8px' }}
      >
        <span style={{ padding: '0 8px' }}>添加过滤规则</span>
      </Button>
    </>
  );
};

export default FilterRule;
