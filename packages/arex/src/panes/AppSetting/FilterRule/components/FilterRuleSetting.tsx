import { Button, <PERSON>lapse, Flex, Space } from 'antd';
import React, { FC } from 'react';

import { FilterRuleListBodyData } from '@/services/FilterConfigurationService';

import FilterDetail from './FilterDetail';

interface FilterRuleSettingProps {
  onCopy: (item: string) => void;
  onDelete: (item: string, index: number) => void;
  interfaceOptions: any[];
  filterRuleListItem?: FilterRuleListBodyData[];
  filterRuleEnumOptions: any[];
  onUpdate?: (params: any) => void;
}

const FilterRuleSetting: FC<FilterRuleSettingProps> = (props) => {
  const { onCopy } = props;

  const genExtra = (item: string, index: number) => (
    <Flex gap='small'>
      <Button type='primary' size='small' onClick={() => onCopy(item)}>
        复制
      </Button>
      <Button type='primary' size='small' onClick={() => props.onDelete(item, index)}>
        删除
      </Button>
    </Flex>
  );

  const handleUpdate = (params: any) => {
    props.onUpdate && props.onUpdate(params);
  };

  const getInterfaceRule = (filterSummaryInfos: any[], interfaceInfo: any, ruleIndex: number) => {
    const items = filterSummaryInfos.map((item) => {
      return {
        key: item.id,
        label: (
          <>
            {item.filterName}: {interfaceInfo.operationName}
          </>
        ),
        extra: genExtra(item.id, ruleIndex),
        children: (
          <FilterDetail
            id={item.id}
            filterRuleEnumOptions={props.filterRuleEnumOptions}
            interfaceOptions={props.interfaceOptions || []}
            onUpdate={handleUpdate}
          />
        ),
      };
    });

    return <Collapse size='small' collapsible='icon' items={items} />;
  };

  const getInterfaceCard = (filterRuleListItems: any[]) => {
    return filterRuleListItems.map((item, index: number) => {
      return <div key={index}>{getInterfaceRule(item.filterSummaryInfos, item, index)}</div>;
    });
  };
  return (
    <div style={{ marginBottom: '8px' }}>
      <Space direction='vertical' style={{ width: '100%' }}>
        {getInterfaceCard(props.filterRuleListItem || [])}
      </Space>
    </div>
  );
};
export default FilterRuleSetting;
