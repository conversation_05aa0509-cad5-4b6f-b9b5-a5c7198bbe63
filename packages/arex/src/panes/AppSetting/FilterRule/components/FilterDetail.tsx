import { DeleteOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { SmallTextButton } from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Input, message, Segmented, Select, Space, Table } from 'antd';
import React, { useEffect, useState } from 'react';

import { FilterConfigurationService } from '@/services';
import { FilterRuleData, filterRuleDetailBody } from '@/services/FilterConfigurationService';

import { AliasWordList } from '../index.config';
import FilterRuleModal from './FilterRuleModal';

interface IProps {
  id: string;
  interfaceOptions: any[];
  filterRuleEnumOptions: any[];
  onUpdate?: (params: any) => void;
}

const FilterDetail = (props: IProps) => {
  const [detail, updateDetail] = useState<any>({});
  const [expression, updateExpression] = useState<string>('');
  const [segValue, updateSegValue] = useState<'res' | 'req'>('req');
  const [resTableData, updateResTableData] = useState<FilterRuleData[]>([]);
  const [reqTableData, updateReqTableData] = useState<FilterRuleData[]>([]);
  const [allTableData, updateAllTableData] = useState<FilterRuleData[]>([]);
  const [ruleName, updateRuleName] = useState<string>('');
  const [interfaceId, updateInterfaceId] = useState<string | undefined>(undefined);
  const [currentInterface, updateCurrenInterface] = useState<any>({});
  const [showModal, updateShowModal] = useState<boolean>(false);
  const filterOptions = [
    {
      label: '请求过滤',
      value: 'req',
    },
    {
      label: '响应过滤',
      value: 'res',
    },
  ];

  const { run: getFilterRuleDetail } = useRequest(FilterConfigurationService.getFilterRuleDetail, {
    manual: true,
    onSuccess: (res: filterRuleDetailBody[]) => {
      const filterRuleDetailRes = res;
      if (filterRuleDetailRes) {
        const detail = filterRuleDetailRes[0];
        updateExpression(detail.expression);
        updateRuleName(detail.filterName);
        updateDetail(detail);
        // console.log(detail);
        updateInterfaceId(detail.operationId || undefined);
        updateCurrenInterface(detail);
        updateReqTableData(detail.requestFilterRules);
        updateResTableData(detail.responseFilterRules);
      }
    },
  });

  useEffect(() => {
    if (props.id) {
      getFilterRuleDetail({ filterId: props.id });
    }
  }, [props.id]);

  useEffect(() => {
    updateAllTableData([...reqTableData, ...resTableData]);
  }, [reqTableData, resTableData]);

  const handleSave = () => {
    if (!ruleName) {
      return message.error('请输入规则名称');
    }
    const opId = currentInterface.operationId || currentInterface.id;
    if (!opId) {
      return message.error('请选择接口');
    }
    const params: any = {
      filterName: ruleName,
      appId: currentInterface.appId,
      serviceId: currentInterface.serviceId,
      operationName: currentInterface.operationName,
      operationId: opId,
      operator: currentInterface.operator,
      operationType: currentInterface.operationType,
      operationTypes: currentInterface.operationTypes || [],
      requestFilterRules: reqTableData,
      responseFilterRules: resTableData,
      expression: expression || detail.expression || '',
    };
    if (detail.id) {
      params.id = detail.id;
    }
    console.log(params);
    props.onUpdate && props.onUpdate(params);
  };

  const handleChangeRuleName = (e: any) => {
    const val = e.target.value;
    updateRuleName(val);
  };

  const handleChangeInterface = (val: string) => {
    updateInterfaceId(val);
    updateCurrenInterface({ interfaceId, ...updateCurrenInterface });
    let curOptions: any = {};
    props.interfaceOptions.map((item) => {
      (item.options || []).map((cItem: any) => {
        if (cItem.value === val) {
          curOptions = cItem;
        }
        return cItem;
      });
      return item;
    });
    updateCurrenInterface(curOptions);
  };

  const handleSaveRule = (rule: FilterRuleData) => {
    const allOldData = allTableData || [];
    const oldAllAliasList = allOldData.map((item) => item.alias);

    for (const aliasItem of AliasWordList) {
      if (!oldAllAliasList.includes(aliasItem)) {
        rule.alias = aliasItem;
        break;
      }
    }
    const isResponseType = rule.coloringType == 4;

    if (isResponseType) {
      updateResTableData([...resTableData, rule]);
    } else {
      updateReqTableData([...reqTableData, rule]);
    }

    updateShowModal(false);
  };

  const handleDeleteRule = (item: FilterRuleData) => {
    reqTableData.forEach((reqItem, index) => {
      if (reqItem === item) {
        reqTableData.splice(index, 1);
      }
    });
    resTableData.forEach((resItem, index) => {
      if (resItem === item) {
        resTableData.splice(index, 1);
      }
    });

    updateReqTableData([...reqTableData]);
    updateResTableData([...resTableData]);
  };

  const cloumns = [
    {
      title: 'filters',
      dataIndex: 'filters',
      render(filters: string | string[]) {
        return Array.isArray(filters) ? filters.join('/') : filters;
      },
    },
    {
      title: 'coloringType',
      dataIndex: 'coloringType',
      render(val: number) {
        return val == 4 ? 'Response' : 'Request';
      },
    },
    {
      title: 'filterRuleEnum',
      dataIndex: 'filterRuleEnum',
      render(val: number) {
        const currentEnumOption = props.filterRuleEnumOptions.find((item) => item.value === val);
        return currentEnumOption.label || '';
      },
    },
    { title: 'compareValue', dataIndex: 'compareValue' },
    { title: 'alias', dataIndex: 'alias' },
    {
      title: '操作',
      dataIndex: '',
      render(item: FilterRuleData) {
        return <SmallTextButton icon={<DeleteOutlined />} onClick={() => handleDeleteRule(item)} />;
      },
    },
  ];

  const checkExpression = () => {
    const testString = expression.replace(/[a-zA-Z]{1,}/g, '1');
    try {
      eval(testString);
      message.success('表达式正确');
    } catch (error) {
      message.error('表达式错误');
    }
  };

  return (
    <>
      <div style={{ display: 'flex' }}>
        <Form.Item label='规则名称'>
          <Input placeholder='请输入规则名称' value={ruleName} onChange={handleChangeRuleName} />
        </Form.Item>
        <Form.Item label='接口' style={{ marginLeft: 16 }}>
          <Select
            optionFilterProp='label'
            placeholder='请选择接口'
            popupMatchSelectWidth={false}
            showSearch
            options={props.interfaceOptions || []}
            value={interfaceId}
            onChange={handleChangeInterface}
            style={{ width: '160px' }}
          />
        </Form.Item>
      </div>
      <Space direction='vertical' style={{ width: '100%' }} size={'middle'}>
        <Segmented
          value={segValue}
          style={{ backgroundColor: '#f0f0f0' }}
          options={filterOptions}
          onChange={(value: any) => {
            updateSegValue(value);
          }}
        />
        <Card
          title={
            <Space size='small'>
              规则列表{' '}
              <SmallTextButton icon={<PlusOutlined />} onClick={() => updateShowModal(true)} />
            </Space>
          }
          extra={<QuestionCircleOutlined />}
        >
          <Table
            size='small'
            bordered
            columns={cloumns}
            dataSource={allTableData}
            pagination={false}
            rowKey={(r) => JSON.stringify(r)}
          />
        </Card>
        <Card
          title='表达式'
          extra={
            <span style={{ cursor: 'pointer' }} onClick={checkExpression}>
              检查
            </span>
          }
        >
          <Input.TextArea
            placeholder='请输入表达式'
            rows={3}
            onChange={(e) => updateExpression(e.target.value)}
            value={expression}
          />
        </Card>
        <Button type='primary' onClick={() => handleSave()}>
          保存
        </Button>
      </Space>
      <FilterRuleModal
        show={showModal}
        filterRuleEnumOptions={props.filterRuleEnumOptions}
        colorParams={{
          appId: currentInterface.appId,
          operationId: currentInterface.operationId || interfaceId,
          colorType: segValue,
        }}
        onSave={handleSaveRule}
        onClose={() => {
          updateShowModal(false);
        }}
      />
    </>
  );
};

export default FilterDetail;
