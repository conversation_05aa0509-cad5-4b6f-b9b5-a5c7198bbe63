import { useRequest } from 'ahooks';
import { Form, Input, Modal, Select } from 'antd';
import React, { useEffect, useState } from 'react';

import { ComparisonService } from '@/services';

interface Props {
  show: boolean;
  colorParams: any;
  filterRuleEnumOptions: any[];
  onSave: (formVal: any) => void;
  onClose: () => void;
}

const FilterRuleModal = (props: Props) => {
  const [colorOptions, updateColorOptions] = useState<any[]>([]);
  const [form] = Form.useForm();
  // 删除未使用的 colorTypeMap
  const [colorType, setColorType] = useState<string>('');

  const onFilterChange = (value: any) => {
    // 从 colorOptions 中找到对应的选项，设置 colorType
    const selectedOption = colorOptions.find(option => option.value === value);
    if (selectedOption) {
      setColorType(selectedOption.coloringType);
    }
  };

  const handleSave = () => {
    form.validateFields().then((formVal) => {
      formVal.filters = (formVal.filters || '').split('/');
      formVal.alias = '';
      formVal.coloringType = colorType;

      props.onSave && props.onSave(formVal);
      form.resetFields();
    });
  };

  const { run: queryDyeIgnoreNode } = useRequest(
    () => {
      return ComparisonService.queryDyeIgnoreNode(props.colorParams);
    },
    {
      onSuccess: (res) => {
        const list = res.map((item) => {
          return {
            label: item.colorings.join('/'),
            value: item.colorings.join('/'),
            coloringType: item.colorType,
          };
        });
        updateColorOptions(list);
      },
    },
  );

  useEffect(() => {
    if (props.show) {
      queryDyeIgnoreNode();
    }
  }, [props.show]);

  return (
    <Modal open={props.show} title='规则' onOk={handleSave} onCancel={props.onClose}>
      <Form labelCol={{ style: { width: 80 } }} form={form}>
        <Form.Item label='染色节点' name='filters'>
          <Select options={colorOptions} placeholder='请选择' onChange={onFilterChange} />
        </Form.Item>
        <Form.Item label='表达式' name='filterRuleEnum'>
          <Select options={props.filterRuleEnumOptions || []} placeholder='请选择' />
        </Form.Item>
        <Form.Item label='值' name='compareValue'>
          <Input placeholder='请输入' />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default FilterRuleModal;
