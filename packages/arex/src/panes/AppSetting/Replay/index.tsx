import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { HelpTooltip, SmallTextButton, useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { useRequest } from 'ahooks';
import {
  App,
  Button,
  ConfigProvider,
  Form,
  InputNumber,
  Modal,
  Segmented,
  Select,
  Space,
  Table,
  Tabs,
  theme,
} from 'antd';
import React, { FC, useEffect, useState } from 'react';
import { useImmer } from 'use-immer';

import UndertoneWrapper from '@/panes/AppSetting/UndertoneWrapper';
import { ConfigService } from '@/services';
import { ApplicationService } from '@/services';
import { TableRow } from '@/services/ConfigService/updateReplaySetting';

import { SettingRecordProps } from '../Record';
import SettingForm from '../SettingForm';
import { ExcludeOperation, MockRuleModal } from './FormItem';
import { SelectedMethods } from './FormItem/MockRuleModal';

type SettingFormType = {
  offsetDays: number;
  excludeOperationMap: TableRow[];
  sendMaxQps: number;
};

interface TableEntry {
  key: string;
  values: string[] | string;
}

const defaultValues: SettingFormType = {
  offsetDays: 0,
  excludeOperationMap: [],
  sendMaxQps: 0,
};

const SettingReplay: React.FC<SettingRecordProps> = ({ appId }) => {
  const [env, setEnv] = useState('Product');
  const { token } = theme.useToken();
  const { message } = App.useApp();
  const { t } = useTranslation(['components', 'common']);
  const [initialValues, setInitialValues] = useImmer<SettingFormType>(defaultValues);
  const [form] = Form.useForm();
  const [showModal, updateShowModal] = useState<boolean>(false);
  const [tableData, setTableData] = useState<TableRow[]>([]);
  const [tcpcopytableData, setTcpcopyTableData] = useState<
    {
      operationName: string;
      operationType: string;
      id: string;
    }[]
  >([]);
  const [showTcpCopyModal, setShowTcpCopyModal] = useState(false);

  // 添加接口列表数据请求
  const { data: interfaceList } = useRequest(
    () =>
      ApplicationService.queryInterfacesList({
        appId,
        env,
      }),
    {
      ready: !!appId && !!env,
      refreshDeps: [env],
    },
  );

  const handleSaveTcpCopyRule = async (values: { interface: string[] }) => {
    try {
      // 获取选中接口的ID并转换为字符串，同时过滤掉 null 和 undefined
      const selectedIds = interfaceList
        ?.filter((item) => values.interface.includes(item.operationName))
        .map((item) => item.id)
        .filter((id): id is string => id != null)
        .map((id) => id.toString());

      // 调用保存接口
      await ConfigService.updateMockType({
        operationIds: selectedIds || [],
        mockType: true,
      });

      // 保存成功后刷新列表，不再手动更新状态
      refreshTcpCopyList();
      message.success('TCP Copy配置添加成功！');
      setShowTcpCopyModal(false);
    } catch (error) {
      message.error('TCP Copy配置添加失败！');
    }
  };

  // TCP Copy 模式数据请求
  const { data: mockTypeList, run: refreshTcpCopyList } = useRequest(
    () => ConfigService.queryMockTypeList(appId),
    {
      ready: !!appId,
      refreshDeps: [env],
      onSuccess: (data) => {
        if (data) {
          const formattedData = data.map((item) => ({
            key: item.operationName,
            operationType: item.operationType,
            id: item.id,
          }));
          setTcpcopyTableData(formattedData);
        }
      },
    },
  );

  const cloumns = [
    {
      title: '插件类型',
      dataIndex: 'key',
      key: 'key',
      align: 'left',
    },
    {
      title: '方法名',
      dataIndex: 'values',
      key: 'values',
      align: 'left',
      render: (values: string[]) => (
        <ul>
          {values.map((value) => (
            <li key={value} style={{ whiteSpace: 'pre-wrap' }}>
              {value}
            </li>
          ))}
        </ul>
      ),
    },
    {
      title: '操作',
      dataIndex: '',
      align: 'center',
      render(item: TableRow) {
        return (
          <SmallTextButton icon={<DeleteOutlined />} onClick={() => handleDeletePluginRule(item)} />
        );
      },
    },
  ];

  const tcpcopycloumns = [
    {
      title: '接口',
      dataIndex: 'key',
      key: 'key',
      align: 'left',
      render: (text: string) => (
        <span style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>{text}</span>
      ),
    },
    {
      title: '操作',
      dataIndex: '',
      align: 'center',
      render(record: any) {
        return (
          <SmallTextButton
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteTcpCopyRule(record)}
          />
        );
      },
    },
  ];

  const handleSavePluginRule = (selected: SelectedMethods) => {
    const newEntry: TableEntry = {
      key: selected.plugin,
      values: selected.method,
    };

    const newRow: TableRow[] = {
      key: newEntry.key,
      values: Array.isArray(newEntry.values) ? newEntry.values : [newEntry.values],
    };
    tableData.push(newRow);
    setTableData([...tableData]);
    message.success('插件配置添加成功！');
    updateShowModal(false);
  };

  const handleDeletePluginRule = (item: TableRow[]) => {
    tableData.forEach((resItem, index) => {
      if (resItem.key === item.key) {
        tableData.splice(index, 1);
      }
    });
    setTableData([...tableData]);
  };

  const handleDeleteTcpCopyRule = async (record: any) => {
    try {
      // 调用删除接口
      await ConfigService.deleteMockType(record.id);

      // 删除成功后刷新列表
      refreshTcpCopyList();
      message.success('TCP Copy配置删除成功！');
    } catch (error) {
      message.error('TCP Copy配置删除失败！');
    }
  };

  const { loading } = useRequest(ConfigService.queryReplaySetting, {
    defaultParams: [{ appId: appId, env: env }],
    onSuccess(res: any) {
      const data = res[0] || {};
      setInitialValues({
        offsetDays: data.offsetDays,
        // @ts-ignore
        excludeOperationMap: data.excludeOperationMap
          ? Object.entries(data.excludeOperationMap).map(([key, value]) => ({
              key,
              value,
            }))
          : [],
        sendMaxQps: data.sendMaxQps,
      });
      form.setFieldValue('offsetDays', data.offsetDays);
      form.setFieldValue('sendMaxQps', data.sendMaxQps);
      function convertToTableData(data1: { string: Set<string> }): TableRow[] {
        return Object.entries(data1).map(([key, set]) => ({
          key,
          values: Array.from(set),
        }));
      }
      const tableData: TableRow[] = convertToTableData(data.excludeOperationMap || []);
      setTableData(tableData);
    },
  });

  const { run: updateReplaySetting } = useRequest(ConfigService.updateReplaySetting, {
    manual: true,
    onSuccess(res) {
      res && message.success(t('message.updateSuccess', { ns: 'common' }));
    },
  });

  const { run: change } = useRequest(ConfigService.queryReplaySetting, {
    manual: true,
    onSuccess(res: any) {
      const data = res[0] || {};
      setInitialValues({
        offsetDays: data.offsetDays,
        // @ts-ignore
        excludeOperationMap: data.excludeOperationMap
          ? Object.entries(data.excludeOperationMap).map(([key, value]) => ({
              key,
              value,
            }))
          : [],
        sendMaxQps: data.sendMaxQps,
      });
      form.setFieldValue('offsetDays', data.offsetDays);
      form.setFieldValue('sendMaxQps', data.sendMaxQps);
      function convertToTableData(data1: { string: Set<string> }): TableRow[] {
        return Object.entries(data1).map(([key, set]) => ({
          key,
          values: Array.from(set),
        }));
      }
      const tableData: TableRow[] = convertToTableData(data.excludeOperationMap || []);
      setTableData(tableData);
    },
  });

  // useEffect(() => {
  //   form.resetFields();
  // }, [initialValues]);

  const onFinish = (values: SettingFormType) => {
    const params = {
      appId: appId,
      env: env,
      offsetDays: values.offsetDays,
      excludeOperationMap: tableData.reduce<{ [key: string]: string[] }>((map, cur) => {
        // @ts-ignore
        map[cur.key] = cur.values;
        return map;
      }, {}),
      sendMaxQps: values.sendMaxQps,
    };
    updateReplaySetting(params);
  };
  const renderForm = () => {
    return (
      <SettingForm loading={loading} initialValues={initialValues} form={form} onFinish={onFinish}>
        <UndertoneWrapper>
          <Form.Item
            label={
              <HelpTooltip title={t('appSetting.QPSTips')}>{t('appSetting.maxQPS')}</HelpTooltip>
            }
            name='sendMaxQps'
            rules={[{ required: true, message: t('appSetting.emptyQPS') as string }]}
          >
            <InputNumber min={1} max={2000} precision={0} />
          </Form.Item>

          <Form.Item
            label={
              <HelpTooltip title={t('appSetting.caseRangeTooltip')}>
                {t('appSetting.caseRange')}
              </HelpTooltip>
            }
            name='offsetDays'
            rules={[{ required: true, message: t('appSetting.emptyCaseRange') as string }]}
          >
            <InputNumber min={1} />
          </Form.Item>

          <Form.Item
            label={
              <HelpTooltip title={t('appSetting.mockRuletip')}>
                {t('appSetting.exclusion')}
              </HelpTooltip>
            }
          ></Form.Item>

          <Table
            dataSource={tableData}
            size='small'
            bordered
            columns={cloumns}
            pagination={false}
            rowKey={(r) => JSON.stringify(r)}
          />
          <Button
            block
            type='text'
            size='small'
            icon={<PlusOutlined />}
            onClick={() => updateShowModal(true)}
          >
            {t('add', { ns: 'common' })}
          </Button>
          <Form.Item
            label={
              <HelpTooltip title={t('appSetting.tcpcopymodetip')}>
                {t('appSetting.tcpcopymode')}
              </HelpTooltip>
            }
          ></Form.Item>
          <Table
            dataSource={tcpcopytableData}
            size='small'
            bordered
            columns={tcpcopycloumns}
            pagination={false}
            rowKey={(r) => JSON.stringify(r)}
          />
          <Button
            block
            type='text'
            size='small'
            icon={<PlusOutlined />}
            onClick={() => setShowTcpCopyModal(true)}
          >
            {t('add', { ns: 'common' })}
          </Button>
        </UndertoneWrapper>
        <Modal
          title={t('TCP Copy模式')}
          open={showTcpCopyModal}
          onCancel={() => setShowTcpCopyModal(false)}
          footer={null}
        >
          <Form onFinish={handleSaveTcpCopyRule}>
            <Form.Item
              label='接口'
              name='interface'
              rules={[{ required: true, message: '请选择接口名称' }]}
            >
              <Select
                mode='multiple'
                placeholder='请选择接口'
                options={interfaceList?.map((item: any) => ({
                  label: item.operationName,
                  value: item.operationName,
                }))}
              />
            </Form.Item>
            <Form.Item style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setShowTcpCopyModal(false)}>取消</Button>
                <Button type='primary' htmlType='submit'>
                  确定
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        <Form.Item
          wrapperCol={{ offset: 8, span: 16 }}
          style={{ textAlign: 'right', marginTop: '16px' }}
        >
          <Button type='primary' htmlType='submit'>
            {t('save', { ns: 'common' })}
          </Button>
        </Form.Item>
        <MockRuleModal
          key={showModal ? 'open' : 'closed'}
          visible={showModal}
          onSave={handleSavePluginRule}
          onClose={() => {
            updateShowModal(false);
          }}
          appId={appId}
          env={env}
        />
      </SettingForm>
    );
  };

  const optionsItems = [
    { key: 'Product', label: 'Product', value: 'Product', children: renderForm() },
    { key: 'SandBox', label: 'SandBox', value: 'SandBox', children: renderForm() },
    { key: 'Stable', label: 'Stable', value: 'Stable', children: renderForm() },
    { key: 'Test', label: 'Test', value: 'Test', children: renderForm() },
  ];

  return (
    <ConfigProvider
      theme={{
        components: {
          Segmented: {
            /* 这里是你的组件 token */
            itemSelectedBg: token.colorPrimary,
            algorithm: true,
          },
        },
      }}
    >
      <Segmented
        block
        value={env}
        options={optionsItems}
        onChange={(env) => {
          setEnv(env as string);
          change({ appId: appId, env: env.toString() });
        }}
        style={{ margin: '0 8px 8px', backgroundColor: 'rgb(245, 245, 245)' }}
      />

      <Tabs
        activeKey={env}
        items={optionsItems}
        css={css`
          .ant-tabs-nav {
            display: none;
          }
        `}
      />
    </ConfigProvider>
  );
};

export default SettingReplay;
