import { Form, Input, Modal, Select, Checkbox, message } from 'antd';
import React, { useEffect, useState, useMemo } from 'react';
import { useRequest } from 'ahooks';
import axios from 'axios';

const { Option } = Select;

const envOptions = [
  { label: 'Product', value: 'Product' },
  { label: 'SandBox', value: 'SandBox' },
  { label: 'Stable', value: 'Stable' },
  { label: 'Test', value: 'Test' },
];

export interface SelectedMethods {
  clusterName: string;
  envNames: string[] | string;
}

interface Props {
  visible: boolean;
  onSave: (selected: SelectedMethods) => void;
  onClose: () => void;
  destroyOnClose: boolean;
  //   appId:string;
  //   env:string;
}

type AutoCompLabel = {
  key: string;
  value: string;
  clusterName?: string;
};

const ClusterIntegrationModal = (props: Props) => {
  const [form] = Form.useForm<FormData>();
  const [clusterOptions, setClusterOptions] = useState<AutoCompLabel[]>([]);
  const [clusterName, setClusterName] = useState<string | undefined>('');
  const [envNames, setEnvNames] = useState<string[]>([]);

  useEffect(() => {
    setEnvNames([]);
    setClusterName('');
  }, []);

  useRequest(() => axios.get('/iapi/iapi/scence/getAllServiceInfo'), {
    onSuccess: (res) => {
      if (res.data && Array.isArray(res.data.data)) {
        setClusterOptions(res.data.data);
      } else {
        console.error('Invalid data format:', res.data);
      }
    },
    onError: (error) => {
      console.error('Request error:', error);
    },
  });

  const handleClusterChange = (value: string) => {
    setClusterName(value);
  };

  const handleSave = () => {
    if (clusterName && envNames?.length > 0) {
      const result: SelectedMethods = {
        clusterName,
        envNames: envNames as string[],
      };
      props.onSave(result);
      setEnvNames([]);
      setClusterName('');
    } else {
      message.warning('请选择集群和执行环境');
    }
  };

  return (
    <Modal
      open={props.visible}
      title='新建接入集群'
      onOk={handleSave}
      onCancel={props.onClose}
      destroyOnClose={props.destroyOnClose}
    >
      <Form labelCol={{ style: { width: 80 } }} form={form}>
        <Form.Item label='集群名' name='ClusterName'>
          <Select
            mode='tags'
            placeholder='请选择'
            value={clusterName}
            onChange={handleClusterChange}
            showSearch
          >
            {clusterOptions.map((option) => (
              <Option key={option.key} value={option.clusterName}>
                {option.clusterName}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label='执行环境' name='executionEnvironment'>
          <Checkbox.Group
            options={envOptions}
            onChange={(e) => setEnvNames(e)}
            value={
              Array.isArray(envNames) && envNames.length > 0 ? (envNames as string[]) : undefined
            }
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ClusterIntegrationModal;
