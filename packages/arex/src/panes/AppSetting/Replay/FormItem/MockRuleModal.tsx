import { Form, Input, Modal, Select } from 'antd';
import React, { useEffect, useState ,useMemo} from 'react';

const { Option } = Select;

const methodEnums:MethodEnum[] =[
  {
  Database: [
      "executeQuery",
      "executeUpdate"
  ],
  HttpClient: "",
  Redis: [
      "PING",
      "DEL",
      "SET",
      "GET",
      "HSET"
  ],
  ScfClient: "",
  WlistClient: "",
  WmbProducer: [
        "commit",
        "abort",
        "send",
        "sendQueue"
    ],
  WosClient: [
        "uploadFile",
        "uploadSingleFile",
        "uploadSliceFile"
    ],
  WtableClient: "",
  CLUSTER_EXCLUDE_MOCK_STATE: [
        "true",
        "false"
    ]
}
]

interface MethodEnum {
  [key: string]: string[] | "";
}

export interface SelectedMethods {
    plugin: string;
    method: string[] | string;
}

interface Props {
  visible:boolean;
  onSave: (selected:SelectedMethods) => void;
  onClose: () => void;
  appId:string;
  env:string;
}

const MockRuleModal = (props: Props) => {
  const [form] = Form.useForm<FormData>();
  const [inputValue, setInputValue] = useState<string>("");
  const [plugin, setPlugin] = useState<string>("");
  const [method, setMethod] = useState<string[] | string>([]);
  const [preSelectedMethod, setPreSelectedMethod] = useState<string[]>([]); // 存储预先选中的次级选项

  useEffect(() => {
      setPlugin("");
      setMethod([]);
      setInputValue("");
}, []);

  
  const handlePluginChange = (value: string) => {
    setPlugin(value);
    if (value === "ScfClient" || value === "HttpClient") {
      setMethod("");
      setInputValue("");
      setPreSelectedMethod([]);
    } else {
      setMethod([]);
      setInputValue("");
      setPreSelectedMethod([]);
    }
  };

  const handleMethodChange = (value: string[] | string) => {
    if ((plugin === "ScfClient")|| (plugin ==="HttpClient")) {
      setInputValue(value as string);
      setMethod(value);
    } else {
      setMethod(value as string[]);
    }
  };
  

  const pluginOptions = Object.keys(methodEnums[0]) ;

  const getMethodOptions = () => {
    if (!plugin) return [];
    const methodData = methodEnums[0][plugin];
    // const methodData = plugin === "HttpClient" ? interfaceOptions :methodEnums[0][plugin];
    if (Array.isArray(methodData)) {
      return methodData;
    }
    return [];
  };

  const handleSave = () => {
    if (plugin) {
      const result: SelectedMethods = {
        plugin,
        // method: plugin === "ScfClient" ? inputValue.trim() : plugin === "HttpClient" ? (interfaceId as string[]):(method as string[]),
        method: ((plugin === "ScfClient") || (plugin ==="HttpClient") )? inputValue.trim() :(method as string[]),
      };
      props.onSave(result);
      setMethod("");
      setInputValue("");
      setPreSelectedMethod([]);
    } else {
      alert("请先选择一个插件类型.");
    }
  };

  // 检查当前主选项是否有次级选项
  const hasMethodOptions = () => {
    const options = getMethodOptions();
    return options.length > 0;
  };

    // 自定义选项渲染，以便高亮显示预先选中的选项
  const renderOption = (option: string) => {
      const isPreSelected = preSelectedMethod.includes(option);
      return (
        <span style={{ fontWeight: isPreSelected ? "bold" : "normal" }}>
          {option}
        </span>
      );
  };

  // const [interfaceOptions, updateInterfaceOptions] = useState<any[]>([]);
  // const [interfaceId, updateInterfaceId] = useState<string[] | string>([]);
  // const [currentInterface, updateCurrenInterface] = useState<any>({});

  // const  queryInterfacesList = useRequest(
  //   () =>
  //     ApplicationService.queryInterfacesList<'Interface'>({
  //       appId: props.appId as string,
  //       env: props.env as string,
  //     }),
  //   {
  //     ready: !!props.appId,
  //     onSuccess(res) {
  //       const list = Object.entries(
  //         res.reduce<Record<string, { label: string; value?: string | null }[]>>(
  //           (options, item) => {
  //             (item.operationTypes || [])?.forEach((operation) => {
  //               const newItem = {
  //                 label: item.operationName,
  //                 value: item.id,
  //                 ...item,
  //               };
  //               options[operation]
  //                 ? options[operation].push(newItem)
  //                 : (options[operation] = [newItem]);
  //             });
  //             return options;
  //           },
  //           {},
  //         ),
  //       );
  //       const options = list.map(([label, options]) => ({
  //         label,
  //         options,
  //       }));
  //       updateInterfaceOptions(options);
  //     },
  //   },
  // );

  
// 查找指定 id 对应的 operationName
//   const foundOperationName = (val:string[] | string)=>{

//    const foundOperationNames = Array.from(val, id => 
//      interfaceOptions.flatMap(item => item.options).find(option => option.id === id)?.operationName || null
//    );
//      updateInterfaceId(foundOperationNames);
// }

  // const handleChangeInterface = (val:string[] | string) => {
  //   foundOperationName(val);
  //   updateCurrenInterface({ interfaceId, ...updateCurrenInterface });
  //   let curOptions: any = {};
  //   interfaceOptions.map((item) => {
  //     (item.options || []).map((cItem: any) => {
  //       if (cItem.value === val) {
  //         curOptions = cItem;
  //       }
  //       return cItem;
  //     });
  //     return item;
  //   });
  //   updateCurrenInterface(curOptions);
  // };

  return (
    <Modal open={props.visible} title='规则' onOk={handleSave} onCancel={props.onClose}>
      <Form labelCol={{ style: { width: 80 } }} form={form}
      >
        <Form.Item label='插件类型' name='plugin' >
          <Select
        placeholder='请选择'
        value={plugin || undefined}
        onChange={handlePluginChange}
        >
        {pluginOptions.map(option => (
          <Option key={option} value={option}>
            {option}
          </Option>
        ))}
          </Select>  
        </Form.Item>       

       {plugin && (
          <Form.Item label="方法名" required>
            {(plugin === "ScfClient" )|| (plugin === "HttpClient") ? (
              <Input
                placeholder="请输入方法"
                value={inputValue}
                onChange={(e) => handleMethodChange(e.target.value)}
              />
            ) 
            // : plugin === 'HttpClient' ? (
            //   <Select
            //   mode="multiple"
            //   placeholder={"请选择方法"}
            //   options={interfaceOptions || []}
            //   style={{ width: '260px' }}
            //   // value={interfaceId}
            //   onChange={handleChangeInterface}
            // >
            //  {getMethodOptions().map((option) => (
            //     <Option key={option} value={option} label={option}>
            //       {renderOption(option)}
            //     </Option>
            //   ))}
            // </Select>
            // )
            :(
              <Select
              mode="multiple"
              placeholder={hasMethodOptions() ? "请选择方法" : "暂时没有选项"}
              value={
                Array.isArray(method) && method.length > 0
                  ? (method as string[])
                  : undefined
              }
              onChange={handleMethodChange}
              allowClear
              optionLabelProp="label"
              disabled={!hasMethodOptions()} // 禁用选择器如果没有选项
              notFoundContent={!hasMethodOptions() ? "暂时没有选项" : undefined} // 设置默认文案
              showSearch
            >
              {getMethodOptions().map((option) => (
                <Option key={option} value={option} label={option}>
                  {renderOption(option)}
                </Option>
              ))}
            </Select>
            )}
          </Form.Item>
        )} 
      </Form>
    </Modal>

  );
};

export default MockRuleModal;
