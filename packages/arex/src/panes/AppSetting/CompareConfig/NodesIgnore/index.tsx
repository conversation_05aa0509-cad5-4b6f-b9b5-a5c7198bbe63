import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  SaveOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import {
  css,
  Label,
  PaneDrawer,
  SmallTextButton,
  SpaceBetweenWrapper,
  useTranslation,
} from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import {
  App,
  AutoComplete,
  Button,
  ButtonProps,
  Card,
  Collapse,
  Input,
  InputRef,
  List,
  Space,
  Tag,
  Typography,
} from 'antd';
import { TreeProps } from 'antd/es';
import { DataNode } from 'antd/lib/tree';
import dayjs from 'dayjs';
import { stringify } from 'querystring';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useImmer } from 'use-immer';

import { EditAreaPlaceholder } from '@/components';
import { CONFIG_TARGET } from '@/panes/AppSetting/CompareConfig';
import CompareConfigTitle from '@/panes/AppSetting/CompareConfig/CompareConfigTitle';
import { ComparisonService } from '@/services';
import { OperationId } from '@/services/ApplicationService';
import {
  DependencyParams,
  ExpirationType,
  IgnoreNodeBase,
  QueryIgnoreNode,
} from '@/services/ComparisonService';

import IgnoreTree from './IgnoreTree';
import { getIgnoreNodes } from './utils';

const ActiveKey = 'sort';

type CheckedNodesData = {
  operationId?: OperationId<'Global'>;
  exclusionsList: string[];
  compareTypeList: string[];
};

export type NodesIgnoreProps = {
  defaultNodeStatusMap: any;
  appId?: string;
  operationId?: string;
  dependency?: DependencyParams;
  readOnly?: boolean;
  syncing?: boolean;
  loadingContract?: boolean;
  configTarget: CONFIG_TARGET;
  contractParsed: { [p: string]: any };
  onAdd?: () => void;
  onSync?: () => void;
  onClose?: () => void;
};

const NodesIgnore: FC<NodesIgnoreProps> = (props) => {
  const { message } = App.useApp();
  const [t] = useTranslation(['components', 'common']);

  const searchRef = useRef<InputRef>(null);
  const editInputRef = useRef<any>(null);

  const [activeKey, setActiveKey] = useState<string | string[]>([ActiveKey]);
  const [search, setSearch] = useState<string | false>(false);
  const [editMode, setEditMode] = useState(false);

  const [openIgnoreModal, setOpenIgnoreModal] = useState(false);

  const [checkedNodesData, setCheckedNodesData] = useImmer<CheckedNodesData>({
    exclusionsList: [],
    compareTypeList: [],
  });

  const [ignoredKey, setIgnoredKey] = useState<string>();
  const [compareTypeKey, setCompareTypeKey] = useState<string>();
  //

  // 假设的状态类型
  enum NodeStatus {
    NORMAL,
    STRIKETHROUGH,
    UNDERLINE,
  }

  //const [nodeStatusMap, setNodeStatusMap] = useState(new Map());
  const [nodeStatusMap, setNodeStatusMap] = useState(
    props.defaultNodeStatusMap ? new Map(Object.entries(props.defaultNodeStatusMap)) : new Map(),
  );
  const [defaultNodeStatusMap, setDefaultNodeStatusMap] = useState(new Map());

  // 自定义节点标题渲染函数
  //const customTitleRender = (node: any) => {
  const handleNodeStatusChange = (key: any, status: any) => {
    setNodeStatusMap((prev) => {
      if (prev.has(key)) {
        prev.delete(key);
      }
      return new Map(prev.set(key, status));
    });
  };

  /**
   * 获取 IgnoreNode
   */
  const {
    data: ignoreNodeList = [],
    loading: loadingIgnoreNode,
    run: queryIgnoreNode,
    mutate: setIgnoreNodeList,
  } = useRequest(
    () =>
      ComparisonService.queryIgnoreNode({
        appId: props.appId!,
        operationId: props.configTarget === CONFIG_TARGET.GLOBAL ? undefined : props.operationId,
        ...(props.configTarget === CONFIG_TARGET.DEPENDENCY ? props.dependency : {}),
      }),
    {
      ready: !!(
        props.appId &&
        (props.configTarget === CONFIG_TARGET.GLOBAL || // GLOBAL ready
          (props.configTarget === CONFIG_TARGET.INTERFACE && props.operationId) || // INTERFACE ready
          (props.configTarget === CONFIG_TARGET.DEPENDENCY && props.dependency))
      ),
      refreshDeps: [props.operationId, props.dependency, props.configTarget],
      onSuccess: convertIgnoreNode,
    },
  );

  useEffect(() => {
    setIgnoreNodeList([]);
  }, [props.configTarget]);
  const [allData, setAllData] = useState<any>();
  const newStatusMap = new Map();
  function convertIgnoreNode(data: QueryIgnoreNode[]) {
    console.log('convertIgnoreNode', data);
    setAllData(data);

    data.map((item: any) => {
      const { compareTypesPath, path } = item;
      const relPath = compareTypesPath || path;
      const relStatus = compareTypesPath ? NodeStatus.UNDERLINE : NodeStatus.STRIKETHROUGH;
      newStatusMap.set(relPath, relStatus);
    });
    setDefaultNodeStatusMap(newStatusMap);
    setNodeStatusMap(newStatusMap);
    setCheckedNodesData((state) => {
      state.operationId = props.operationId;
      state.exclusionsList = data.map((item) => item.path || '');
      state.compareTypeList = data.map((item) => item.path || '');
    });
  }

  // const ignoreNodesFiltered = useMemo(
  //   () =>
  //     typeof search === 'string' && search
  //       ? ignoreNodeList.filter((node) =>
  //           node.exclusions.join('/').toLowerCase().includes(search.toLowerCase()),
  //         )
  //       : ignoreNodeList,
  //   [ignoreNodeList, search],
  // );

  const ignoreNodesFiltered = useMemo(
    () =>
      typeof search === 'string' && search
        ? ignoreNodeList.filter((node) => {
            // 将exclusions和compareTypes都转换为小写并连接成字符串，然后检查是否包含搜索关键词
            const exclusionsStr = node.exclusions.join('/').toLowerCase();
            const compareTypesStr = node.compareTypes.join('/').toLowerCase();
            return (
              exclusionsStr.includes(search.toLowerCase()) ||
              compareTypesStr.includes(search.toLowerCase())
            );
          })
        : ignoreNodeList,
    [ignoreNodeList, search],
  );

  console.log('ignoreNodeList', ignoreNodeList);

  function getPath(nodeList: DataNode[], pathList: string[], basePath = '') {
    nodeList.forEach((node) => {
      const path = basePath ? basePath + '/' + node.title : (node.title as string);
      pathList.push(path);
      node.children && getPath(node.children, pathList, path);
    });
  }

  const nodePath = useMemo(() => {
    const pathList: string[] = [];
    getPath(getIgnoreNodes(props.contractParsed, ''), pathList);
    return pathList.map((value) => ({ value }));
  }, [props.contractParsed]);

  /**
   * 删除 IgnoreNode
   */
  const { run: handleDeleteIgnoreNode } = useRequest(ComparisonService.deleteIgnoreNode, {
    manual: true,
    onSuccess(success) {
      if (success) {
        queryIgnoreNode();
        message.success(t('message.delSuccess', { ns: 'common' }));
      } else {
        message.error(t('message.delFailed', { ns: 'common' }));
      }
    },
  });

  /**
   * 批量新增 IgnoreNode
   */
  const { run: batchInsertIgnoreNode } = useRequest(ComparisonService.batchInsertIgnoreNode, {
    manual: true,
    onSuccess(success) {
      if (success) {
        queryIgnoreNode();
        message.success(t('message.updateSuccess', { ns: 'common' }));
      } else {
        message.error(t('message.updateFailed', { ns: 'common' }));
      }
    },
  });

  /**
   * 批量删除 IgnoreNode
   */
  const { run: batchDeleteIgnoreNode } = useRequest(ComparisonService.batchDeleteIgnoreNode, {
    manual: true,
    onSuccess(success) {
      if (success) {
        queryIgnoreNode();
      } else {
        message.error(t('message.delFailed', { ns: 'common' }));
      }
    },
  });

  /**
   * 新增 Global IgnoreNode 数据
   */
  const { run: insertIgnoreNode } = useRequest(ComparisonService.insertIgnoreNode, {
    manual: true,
    onSuccess(success) {
      if (success) {
        message.success(t('message.updateSuccess', { ns: 'common' }));
        handleGlobalEditExit();
        queryIgnoreNode();
      } else {
        message.error(t('message.updateFailed', { ns: 'common' }));
      }
    },
  });

  const handleGlobalEditExit = () => {
    setEditMode(false);
    setIgnoredKey(undefined);
    setCompareTypeKey(undefined);
  };

  const handleGlobalEditSave = () => {
    if (!ignoredKey) {
      message.warning(t('appSetting.emptyKey'));
      return;
    }

    // if (!compareTypeKey) {
    //   message.warning(t('compareType key'));
    //   return;
    // }

    const params: IgnoreNodeBase = {
      operationId: undefined,
      appId: props.appId,
      exclusions: ignoredKey.split('/').filter(Boolean),
      //compareTypes: compareTypeKey.split('/').filter(Boolean),
      compareTypes: compareTypeKey ? compareTypeKey.split('/').filter(Boolean) : [],
    };

    insertIgnoreNode(params);
  };

  const handleIgnoreTreeSelect: TreeProps['onSelect'] = (_, info) => {
    const selected = info.selectedNodes.map((node) => node.key.toString());
    console.log('selected is ', selected);

    setCheckedNodesData((state) => {
      state.operationId = props.operationId;
      state.exclusionsList = selected;
      state.compareTypeList = selected;
    });
  };

  const handleSearch: ButtonProps['onClick'] = (e) => {
    activeKey?.[0] === ActiveKey && e.stopPropagation();
    setSearch('');
    setTimeout(() => searchRef.current?.focus());
  };

  const handleIgnoreAdd: ButtonProps['onClick'] = (e) => {
    activeKey?.[0] === ActiveKey && e.stopPropagation();
    props.onAdd?.();

    if (props.configTarget === CONFIG_TARGET.GLOBAL) {
      setTimeout(() => editInputRef.current?.focus());
      setEditMode(true);
    } else {
      setOpenIgnoreModal(true);
      // TODO empty construct tip
      // if (Object.keys(props.responseParsed).length) setOpenIgnoreModal(true);
      // else message.info('empty response, please sync response first');
    }
  };

  async function handleIgnoreSave() {
    console.log('nodeStatusMap', nodeStatusMap);
    const { operationId = null, exclusionsList = [], compareTypeList = [] } = checkedNodesData;
    const exclusionsListPrev = ignoreNodeList.map((item) => item.path as string);
    console.log('exclusionsListPrev', exclusionsListPrev);
    console.log('allData', allData);

    const exclusions: string[] = [],
      compareTypes: string[] = [],
      remove: string[] = [];

    nodeStatusMap.forEach((itemVal, itemKey) => {
      if (itemVal === 1) {
        // 忽略
        exclusions.push(itemKey);
      } else if (itemVal === 2) {
        // 比对类型
        compareTypes.push(itemKey);
      }
      const id = allData.find((item: any) => (item.compareTypesPath || item.path) === itemKey)?.id;
      id && remove.push(id);
      // else if(itemVal === 0) {  // 删除
      //remove.push(itemKey)
      // remove.push(ignoreNodeList.find((item) => item.path === itemKey)!.id);
      // }
    });

    console.log('compareTypes', compareTypes);
    console.log('exclusions', exclusions);
    console.log('remove', remove);

    // 计算新旧集合的差集，分别进行增量更新和批量删除
    // Array.from(new Set([...exclusionsListPrev, ...exclusionsList])).forEach((path) => {
    //   if (exclusionsListPrev.includes(path) && exclusionsList.includes(path)) return;
    //   else if (exclusionsListPrev.includes(path))
    //     remove.push(ignoreNodeList.find((item) => item.path === path)!.id);
    //   else add.push(path);
    // });
    const add = [...exclusions, ...compareTypes];
    // 增量更新 老的cun
    // add.length &&
    //   batchInsertIgnoreNode(
    //     add.map((path) => ({
    //       appId: props.appId,
    //       operationId: props.operationId,
    //       exclusions,
    //       compareTypes,
    //       //exclusions: path.split('/').filter(Boolean),
    //       //compareTypes: path.split('/').filter(Boolean),
    //       ...(props.configTarget === CONFIG_TARGET.DEPENDENCY
    //         ? props.dependency
    //         : ({} as DependencyParams)),
    //     })),
    //   );

    // 分开存 path的路径没有分割开存
    // const insertData = [
    //   ...exclusions.map((path) => {
    //     const cleanedPath = path.endsWith('/') ? path.slice(0, -1) : path; // Remove trailing slash if present
    //     return {
    //       appId: props.appId,
    //       operationId: props.operationId,
    //       exclusions: [cleanedPath],
    //       compareTypes: [],
    //       ...(props.configTarget === CONFIG_TARGET.DEPENDENCY
    //         ? props.dependency
    //         : ({} as DependencyParams)),
    //     };
    //   }),

    //   ...compareTypes.map((path) => {
    //     const cleanedPath = path.endsWith('/') ? path.slice(0, -1) : path; // Remove trailing slash if present for compareTypes as well
    //     return {
    //       appId: props.appId,
    //       operationId: props.operationId,
    //       exclusions: [],
    //       compareTypes: [cleanedPath], // Each compareType path without trailing slash is an individual entry
    //       ...(props.configTarget === CONFIG_TARGET.DEPENDENCY
    //         ? props.dependency
    //         : ({} as DependencyParams)),
    //     };
    //   }),
    // ];

    //分开存   path的路径也分割开存
    const insertData = [
      ...exclusions.map((path) => {
        const cleanedPath = path.endsWith('/') ? path.slice(0, -1) : path;
        const pathParts = cleanedPath.split('/'); // 将路径按斜杠分割成多个部分
        return {
          appId: props.appId,
          operationId: props.operationId,
          exclusions: pathParts, // 存储分割后的路径部分
          compareTypes: [],
          ...(props.configTarget === CONFIG_TARGET.DEPENDENCY
            ? props.dependency
            : ({} as DependencyParams)),
        };
      }),

      ...compareTypes.map((path) => {
        const cleanedPath = path.endsWith('/') ? path.slice(0, -1) : path;
        const pathParts = cleanedPath.split('/'); // 同样地，将路径按斜杠分割成多个部分
        return {
          appId: props.appId,
          operationId: props.operationId,
          exclusions: [],
          compareTypes: pathParts, // 存储分割后的路径部分
          ...(props.configTarget === CONFIG_TARGET.DEPENDENCY
            ? props.dependency
            : ({} as DependencyParams)),
        };
      }),
    ];

    const deleteData = remove.map((id) => ({ id }));

    try {
      // 先执行删除操作
      if (deleteData.length) {
        await batchDeleteIgnoreNode(deleteData);
      }
      // 然后执行插入操作

      if (insertData.length) {
        setTimeout(async () => {
          await batchInsertIgnoreNode(insertData);
        }, 100); // 调整时间间隔根据需要，这里是200毫秒
      }
    } catch (error) {
      // 错误处理逻辑，例如弹出错误消息等
      console.error('Error during ignore save operations:', error);
    }
    // Perform the batch insert operation
    //  insertData.length && batchInsertIgnoreNode(insertData);
    // 批量删除
    //remove.length && batchDeleteIgnoreNode(remove.map((id) => ({ id })));
    setOpenIgnoreModal(false);
  }

  return (
    <>
      <Collapse
        size='small'
        activeKey={activeKey}
        items={[
          {
            key: ActiveKey,
            label: (
              <CompareConfigTitle
                title={t('appSetting.nodesIgnore', { ns: 'components' })}
                readOnly={props.readOnly}
                onSearch={handleSearch}
                onAdd={handleIgnoreAdd}
              />
            ),
            children: (
              <Card bordered={false} size='small' bodyStyle={{ padding: 0 }}>
                <List
                  size='small'
                  dataSource={ignoreNodesFiltered}
                  loading={loadingIgnoreNode}
                  header={
                    search !== false && (
                      <SpaceBetweenWrapper style={{ padding: '0 16px' }}>
                        <Input.Search
                          size='small'
                          ref={searchRef}
                          placeholder='Search for ignored key'
                          onChange={(e) => setSearch(e.target.value)}
                          style={{ marginRight: '8px' }}
                        />
                        <Button
                          size='small'
                          type='text'
                          icon={<CloseOutlined />}
                          onClick={() => setSearch(false)}
                        />
                      </SpaceBetweenWrapper>
                    )
                  }
                  footer={
                    props.configTarget === CONFIG_TARGET.GLOBAL &&
                    editMode && (
                      <List.Item style={{ padding: '0 16px' }}>
                        <SpaceBetweenWrapper width={'100%'}>
                          <AutoComplete
                            size='small'
                            placeholder='Input key to ignore'
                            ref={editInputRef}
                            options={nodePath}
                            filterOption={(inputValue, option) =>
                              option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                            }
                            value={ignoredKey}
                            onChange={setIgnoredKey}
                            style={{ width: '100%' }}
                          />
                          <span style={{ display: 'flex', marginLeft: '8px' }}>
                            <SmallTextButton
                              icon={<CloseOutlined />}
                              onClick={handleGlobalEditExit}
                            />
                            <SmallTextButton
                              icon={<CheckOutlined />}
                              onClick={handleGlobalEditSave}
                            />
                          </span>
                        </SpaceBetweenWrapper>
                      </List.Item>
                    )
                  }
                  renderItem={(node) => (
                    <List.Item>
                      <SpaceBetweenWrapper width={'100%'}>
                        {/* Displaying exclusions with default color */}
                        <div
                          className='content-wrapper'
                          style={{ display: 'flex', flexDirection: 'column' }}
                        >
                          {' '}
                          {/* 新增一个包裹div来控制内部样式 */}
                          {/* Displaying exclusions with label in the same line */}
                          {node.exclusions && node.exclusions.length > 0 && (
                            <div style={{ display: 'flex', alignItems: 'baseline' }}>
                              <span style={{ marginRight: '8px', color: 'gray' }}>忽略:</span>
                              <Typography.Text ellipsis>
                                {node.exclusions.join('/')}
                              </Typography.Text>
                            </div>
                          )}
                          {/* Displaying compareTypes with label in the same line */}
                          {(node.compareTypes || []).length > 0 && ( // 确保有数据才显示比对类型
                            <div style={{ display: 'flex', alignItems: 'baseline' }}>
                              <span style={{ marginRight: '8px', color: 'gray' }}>类型比对:</span>
                              <Typography.Text
                                ellipsis
                                style={{ color: 'green', display: 'inline' }}
                              >
                                {node.compareTypes.join('/')}
                              </Typography.Text>
                            </div>
                          )}
                        </div>
                        {node.expirationType === ExpirationType.temporary && (
                          <Tag>
                            {node.expirationDate > Date.now() ? (
                              <>
                                <Label>{t('appSetting.expireOn')}</Label>
                                {dayjs(node.expirationDate).format('YYYY/MM/DD HH:mm')}
                              </>
                            ) : (
                              t('appSetting.expired')
                            )}
                          </Tag>
                        )}

                        {!props.readOnly && (
                          <SmallTextButton
                            icon={<DeleteOutlined />}
                            onClick={() => handleDeleteIgnoreNode({ id: node.id })}
                          />
                        )}
                      </SpaceBetweenWrapper>
                    </List.Item>
                  )}
                  locale={{ emptyText: t('appSetting.noIgnoredNodes') }}
                />
              </Card>
            ),
          },
        ]}
        onChange={setActiveKey}
        css={css`
          flex: 1;
          margin: 0 16px 16px 0;
          height: fit-content;
          .ant-collapse-content-box {
            padding: 0 !important;
          }
        `}
      />
      <PaneDrawer
        width='60%'
        title={
          <SpaceBetweenWrapper>
            <Space size='middle'>
              <Typography.Title level={5} style={{ marginBottom: 0 }}>
                Nodes Ignore
              </Typography.Title>

              <Button
                size='small'
                disabled={props.syncing}
                icon={<SyncOutlined spin={props.syncing} />}
                onClick={props.onSync}
              >
                {t('appSetting.sync', { ns: 'components' })}
              </Button>
            </Space>

            <Button size='small' type='primary' icon={<SaveOutlined />} onClick={handleIgnoreSave}>
              {t('save', { ns: 'common' })}
            </Button>
          </SpaceBetweenWrapper>
        }
        open={openIgnoreModal}
        onClose={() => {
          props.onClose?.();
          setOpenIgnoreModal(false);
          convertIgnoreNode(ignoreNodeList);
        }}
      >
        <EditAreaPlaceholder
          ready={!!props.contractParsed}
          dashedBorder
          title={t('appSetting.editArea')}
        >
          <IgnoreTree
            // TODO auto expand failed
            defaultExpandAll
            loading={props.loadingContract}
            treeData={props.contractParsed}
            //selectedKeys={checkedNodesData.exclusionsList}
            selectedKeys={[...checkedNodesData.exclusionsList, ...checkedNodesData.compareTypeList]}
            onSelect={handleIgnoreTreeSelect}
            nodeStatusMap={nodeStatusMap}
            defaultNodeStatusMap={newStatusMap}
            onNodeStatusChange={handleNodeStatusChange}
          />
        </EditAreaPlaceholder>
      </PaneDrawer>
    </>
  );
};

export default NodesIgnore;
