import { EmptyWrapper, styled, useTranslation } from '@arextest/arex-core';
import { Card, Tree } from 'antd';
import { TreeProps } from 'antd/es';
import React, { FC, useMemo, useState } from 'react';

import { getIgnoreNodes } from './utils';

type IgnoreTreeProps = Omit<TreeProps, 'treeData'> & {
  loading?: boolean;
  treeData: object;
  onNodeStatusChange?: (key: any, status: any) => void;
  nodeStatusMap?: any;
  defaultNodeStatusMap?: Map<any, NodeStatus>; // 调整类型定义
};

// 假设的状态类型
enum NodeStatus {
  NORMAL,
  STRIKETHROUGH,
  UNDERLINE,
}

const IgnoreTreeWrapper = styled.div`
  .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: #fff;
  }
`;

const IgnoreTree: FC<IgnoreTreeProps> = (props) => {
  const { t } = useTranslation(['components', 'common']);
  const treeData = useMemo(() => getIgnoreNodes(props.treeData, ''), [props.treeData]);
  // const [nodeStatusMap, setNodeStatusMap] = useState(new Map());
  const { nodeStatusMap } = props;

  // 初始化或更新nodeStatusMap，基于defaultNodeStatusMap
  /* const initialNodeStatusMap = useMemo(() => {
    const map = new Map();
    if (props.defaultNodeStatusMap) {
      props.defaultNodeStatusMap.exclusions.forEach(key => {
        map.set(key, NodeStatus.STRIKETHROUGH);
      });
      props.defaultNodeStatusMap.compareTypes.forEach(key => {
        map.set(key, NodeStatus.UNDERLINE);
      });
    }
    return map;
  }, [props.defaultNodeStatusMap]); */

  const initialNodeStatusMap = useMemo(() => {
    return props.defaultNodeStatusMap || new Map(); // 直接使用传入的 Map，如果没有则返回一个空 Map
  }, [props.defaultNodeStatusMap]);

  // 确保使用初始状态或传入的nodeStatusMap

  //const [nodeStatusMap, setNodeStatusMap] = useState(initialNodeStatusMap || new Map());

  // 自定义节点标题渲染函数
  const customTitleRender = (node: any) => {
    const handleClick = () => {
      // 更新节点状态，实现循环
      const nextStatus =
        nodeStatusMap.get(node.key) === NodeStatus.STRIKETHROUGH
          ? NodeStatus.UNDERLINE
          : nodeStatusMap.get(node.key) === NodeStatus.UNDERLINE
          ? NodeStatus.NORMAL
          : NodeStatus.STRIKETHROUGH;
      props.onNodeStatusChange && props.onNodeStatusChange(node.key, nextStatus);
      // setNodeStatusMap((prev) => new Map(prev.set(node.key, nextStatus)));
    };

    let style = {};
    switch (nodeStatusMap.get(node.key)) {
      case NodeStatus.STRIKETHROUGH:
        style = { textDecoration: 'line-through', backgroundColor: '#78c078' };

        break;
      case NodeStatus.UNDERLINE:
        style = { textDecoration: 'underline', backgroundColor: 'rgb(173, 163, 163)' };
        break;
      default:
        break;
    }

    return (
      <span onClick={handleClick} style={style}>
        {node.title}
      </span>
    );
  };

  //console.log("treeData",treeData)
  //console.log("nodeStatusMap",nodeStatusMap)

  return (
    <IgnoreTreeWrapper>
      <Card size='small' title={t('appSetting.clickToIgnore')}>
        <EmptyWrapper
          loading={props.loading}
          empty={!Object.keys(props.treeData).length}
          description={t('appSetting.emptyContractTip')}
        >
          <Tree
            multiple
            defaultExpandAll
            height={800}
            {...props}
            treeData={treeData}
            titleRender={customTitleRender}
          />
        </EmptyWrapper>
      </Card>
    </IgnoreTreeWrapper>
  );
};

export default IgnoreTree;
