// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getArrayNode > no array object 1`] = `[]`;

exports[`getArrayNode > object with nest array and sortNodeList 1`] = `
[
  {
    "children": [],
    "disabled": true,
    "icon": undefined,
    "key": "responsestatustype/",
    "title": "responsestatustype",
  },
  {
    "children": [],
    "disabled": false,
    "icon": <Badge
      color="green"
    />,
    "key": "body/",
    "title": "body",
  },
  {
    "children": [],
    "disabled": false,
    "icon": <Badge
      color="green"
    />,
    "key": "nestArray/",
    "title": "nestArray",
  },
]
`;

exports[`getArrayNode > object with simple array and sortNodeList 1`] = `
[
  {
    "children": [],
    "disabled": true,
    "icon": undefined,
    "key": "responsestatustype/",
    "title": "responsestatustype",
  },
  {
    "children": [],
    "disabled": false,
    "icon": <Badge
      color="green"
    />,
    "key": "body/",
    "title": "body",
  },
]
`;
