import { HelpTooltip, useTranslation } from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import {
  Button,
  Checkbox,
  Collapse,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Row,
  Select,
  Switch,
  Typography,
} from 'antd';
import React, { FC, useEffect, useState } from 'react';

import { InterfaceSelect } from '@/components';
import { CICDConfigurationService, FilterConfigurationService } from '@/services';
import { CiCdConfigurationBodyData } from '@/services/CICDConfigurationService';

type CiCdProps = {
  appId: string;
  env: string;
};
const CiCd: FC<CiCdProps> = (props) => {
  const { t } = useTranslation(['components']);
  const environmentOptions = [
    { label: 'SandBox', value: 1 },
    { label: 'Stable', value: 2 },
    { label: 'Test', value: 3 },
  ];
  const replayRangeOptions = [
    { label: t('cicd.last24Hours'), value: 1 },
    { label: t('cicd.today'), value: 2 },
    { label: t('cicd.other'), value: 3 },
  ];
  const triggerTimeOptions = [
    { label: t('cicd.noTrigger'), value: 0 },
    { label: t('cicd.arrange'), value: 1 },
    { label: t('cicd.sendForTest'), value: 2 },
  ];
  const yunTriggerOptions = [
    { label: t('cicd.arrange'), value: 1 },
    { label: t('cicd.upgrade'), value: 2 },
  ];
  const yunTriggerTimeOptions = [
    { label: t('cicd.noTrigger'), value: 0 },
    { label: t('cicd.arrange'), value: 1 },
  ];
  const [filterRuleOptions, updateFilterRuleOptions] = useState<any[]>([]);
  const [form] = Form.useForm();
  const [hidden, setHidden] = useState(true);
  const [ciCdObj, setCiCdObj] = useState<CiCdConfigurationBodyData>();

  const { run: getCiCdObj } = useRequest(CICDConfigurationService.getCiCdConfiguration, {
    onSuccess(res: any) {
      setCiCdObj(res);
    },
  });

  useEffect(() => {
    if (props.appId) {
      getCiCdObj({ appId: props.appId });
      FilterConfigurationService.getFilterRuleList({ appId: props.appId }).then((res: any) => {
        const list: any[] = [];
        (res || []).map((item: { filterSummaryInfos: any[] }) => {
          item.filterSummaryInfos.map((cItem: { filterName: any; id: any }) => {
            list.push({
              label: cItem.filterName,
              value: cItem.id,
            });
            return cItem;
          });
          return item;
        });
        updateFilterRuleOptions(list);
      });
    }
  }, [getCiCdObj, props.appId]);

  useEffect(() => {
    if (ciCdObj) {
      console.log(ciCdObj?.executionEnvironment);
      form.setFieldsValue({
        appId: props.appId,
        cicdSwitch: ciCdObj?.cicdSwitch,
        executionEnvironment: ciCdObj?.executionEnvironment,
        threshold: ciCdObj?.threshold,
        replayRange: ciCdObj?.replayRange,
        replayRangeHours: ciCdObj?.replayRangeHours,
        replayName: ciCdObj?.replayName,
        triggerTime: ciCdObj?.triggerTime,
        yunTrigger: ciCdObj?.yunTrigger,
        replayPath: ciCdObj?.replayPath,
        replayCasesNum: ciCdObj?.replayCasesNum,
        filterRules: ciCdObj?.filterRules,
      });
      if (ciCdObj?.replayRange == 3 && ciCdObj?.replayRangeHours) {
        setHidden(false);
      }
    }
  }, [form, ciCdObj]);

  const { run: saveCiCdConfiguration } = useRequest(
    CICDConfigurationService.saveCiCdConfiguration,
    {
      manual: true,
      onSuccess(res: any) {
        if (res) {
          message.success('操作成功');
        } else {
          message.success('操作失败');
        }
      },
    },
  );

  const handleSave = () => {
    const params = form.getFieldsValue();
    params.appId = props.appId;
    console.log('params', ciCdObj, params);
    saveCiCdConfiguration({ ...ciCdObj, ...params });
  };

  return (
    <Form
      initialValues={{
        cicdSwitch: false,
        executionEnvironment: [1],
        threshold: 80,
        replayRange: 1,
        triggerTime: 1,
        yunTriggerTime: 0,
      }}
      form={form}
    >
      <Collapse
        bordered={false}
        defaultActiveKey={['basicSetting']}
        items={[
          {
            key: 'basicSetting',
            label: (
              <Typography.Text>
                {t('basicSetting', {
                  ns: 'common',
                })}
              </Typography.Text>
            ),
            children: (
              <>
                <Form.Item label={t('cicd.cicdSwitch')} name='cicdSwitch' valuePropName='checked'>
                  <Switch checkedChildren='开启' unCheckedChildren='关闭' />
                </Form.Item>
                <Form.Item label={t('cicd.executionEnvironment')} name='executionEnvironment'>
                  <Checkbox.Group options={environmentOptions} onChange={(e) => console.log(e)} />
                </Form.Item>
                <Form.Item label={t('cicd.threshold')} name='threshold'>
                  <InputNumber<number>
                    min={0}
                    max={100}
                    formatter={(value) => `${value}%`}
                    parser={(value) => value?.replace('%', '') as unknown as number}
                  />
                </Form.Item>
                <Row>
                  <Form.Item label={t('cicd.replayRange')} name='replayRange'>
                    <Radio.Group
                      options={replayRangeOptions}
                      onChange={(e) => setHidden(e.target.value !== 3)}
                    />
                  </Form.Item>
                  {hidden ? (
                    ''
                  ) : (
                    <Form.Item name='replayRangeHours'>
                      <InputNumber
                        addonBefore={t('cicd.recent')}
                        addonAfter={t('cicd.hours')}
                        style={{ width: 180 }}
                      />
                    </Form.Item>
                  )}
                </Row>
              </>
            ),
          },
        ]}
      />
      <Collapse
        bordered={false}
        defaultActiveKey={['advancedSettings']}
        items={[
          {
            key: 'advancedSettings',
            label: (
              <Typography.Text>
                {t('advancedSettings', {
                  ns: 'common',
                })}
              </Typography.Text>
            ),
            children: (
              <div style={{ width: 'max-content' }}>
                <Form.Item label={t('replay.planName')} name='replayName'>
                  <Input allowClear placeholder={t('replay.planNamePlaceholder') as string} />
                </Form.Item>
                <Form.Item label={t('cicd.triggerTime')} name='triggerTime'>
                  <Radio.Group options={triggerTimeOptions} />
                </Form.Item>
                <Form.Item label={t('cicd.yunTrigger')} name='yunTrigger'>
                  <Checkbox.Group options={yunTriggerOptions} onChange={(e) => console.log(e)} />
                </Form.Item>
                <Form.Item
                  label={
                    <HelpTooltip title={t('replay.pathsTooltip')}>{t('replay.paths')}</HelpTooltip>
                  }
                  name='replayPath'
                >
                  <InterfaceSelect appId={props.appId} placeholder={t('replay.pathsPlaceholder')} />
                </Form.Item>
                <Form.Item label={t('replay.caseCountLimit')} name='replayCasesNum'>
                  <InputNumber
                    precision={0}
                    min={0}
                    addonAfter={t('replay.caseCountUnit')}
                    placeholder={t('replay.caseCountLimitPlaceholder') as string}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Form.Item label={t('replay.filterRules')} name='filterRules'>
                  <Select
                    mode='multiple'
                    allowClear
                    style={{ width: '100%' }}
                    placeholder='Please select'
                    options={filterRuleOptions}
                  />
                </Form.Item>
              </div>
            ),
          },
        ]}
      />
      <Form.Item>
        <Button type='primary' style={{ float: 'right', marginTop: '10px' }} onClick={handleSave}>
          {t('cicd.save')}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CiCd;
