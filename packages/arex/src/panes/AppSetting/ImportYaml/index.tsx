import { useArexCoreConfig, useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { Editor } from '@monaco-editor/react';
import { useRequest } from 'ahooks';
import { App, Button, ConfigProvider, Form, Segmented, Tabs, theme } from 'antd';
import React, { FC, useState } from 'react';

import UndertoneWrapper from '@/panes/AppSetting/UndertoneWrapper';
import { ConfigService } from '@/services';
import { useUserProfile } from '@/store';

type ImportYamlProps = {
  env: string;
  appId: string;
};

const ImportYaml: FC<ImportYamlProps> = (props) => {
  const { message } = App.useApp();
  const { t } = useTranslation(['common', 'components']);
  const [value, setValue] = useState('');
  const [env, setEnv] = useState('Product');
  const { token } = theme.useToken();

  useRequest(ConfigService.queryConfigTemplate, {
    defaultParams: [{ appId: props.appId, env: 'Product' }],
    refreshDeps: [props.appId, props.env],
    onSuccess(res) {
      setEnv(env);
      setValue(res.configTemplate);
    },
  });

  const { run: updateConfigTemplate } = useRequest(
    () =>
      ConfigService.updateConfigTemplate({
        appId: props.appId,
        env: env,
        configTemplate: value,
      }),
    {
      manual: true,
      onSuccess(success) {
        success && message.success(t('message.updateSuccess'));
      },
    },
  );

  const { run: change } = useRequest(ConfigService.queryConfigTemplate, {
    manual: true,
    onSuccess(res) {
      setValue(res.configTemplate);
    },
  });

  const renderForm = () => {
    return (
      <>
        <UndertoneWrapper>
          <Editor
            theme={'vs-dark'}
            // theme={theme1 === 'dark' ? 'vs-dark' : 'light'}
            value={value}
            language={'yaml'}
            height={'400px'}
            onChange={(value) => {
              setValue(value || '');
            }}
          />
        </UndertoneWrapper>

        <Button
          type={'primary'}
          onClick={updateConfigTemplate}
          style={{ float: 'right', marginTop: '16px' }}
        >
          {t('save')}
        </Button>
      </>
    );
  };

  const optionsItems = [
    { key: 'Product', label: 'Product', value: 'Product', children: renderForm() },
    { key: 'SandBox', label: 'SandBox', value: 'SandBox', children: renderForm() },
    { key: 'Stable', label: 'Stable', value: 'Stable', children: renderForm() },
    { key: 'Test', label: 'Test', value: 'Test', children: renderForm() },
  ];

  return (
    <ConfigProvider
      theme={{
        components: {
          Segmented: {
            /* 这里是你的组件 token */
            itemSelectedBg: token.colorPrimary,
            algorithm: true,
          },
        },
      }}
    >
      <Segmented
        block
        value={env}
        options={optionsItems}
        onChange={(env) => {
          setEnv(env as string);
          change({ appId: props.appId, env: env.toString() });
        }}
        style={{ margin: '0 8px 8px', backgroundColor: 'rgb(245, 245, 245)' }}
      />

      <Tabs
        activeKey={env}
        items={optionsItems}
        css={css`
          .ant-tabs-nav {
            display: none;
          }
        `}
      />
    </ConfigProvider>
  );
};

export default ImportYaml;
