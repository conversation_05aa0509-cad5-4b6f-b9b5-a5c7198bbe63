import { DeleteOutlined } from '@ant-design/icons';
import { HelpTooltip, useTranslation } from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import { App, Badge, Button, Form, Input, InputNumber, Popconfirm, Switch, Table, Tag, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { FC, useEffect, useState } from 'react';

import { ConfigService } from '@/services';
import { AgentData } from '@/services/ConfigService';
import { addUserAction, UserActionEnum } from '@/services/ConfigService/agentInstances/sendUserActions';

export interface RunningStatusProps {
  env: string;
  appId: string;
}

interface AgentGroup {
  group: string;
  limit: number;
}

const AgentSetting: FC<RunningStatusProps> = (props) => {
  const [processedAgentData, setProcessedAgentData] = useState<any>([]);
  const { message } = App.useApp();
  const { t } = useTranslation(['components', 'common']);
  const {
    data: agentData,
    loading: loadingAgentList,
    refresh,
    run,
  } = useRequest(ConfigService.getAgentList, {
    defaultParams: [props.appId, props.env],
  });

  useEffect(() => {
    // 处理从后端获取的数据
    const processedAgentData = agentData?.groupRecordMachineCountLimit
      ? Object.entries(agentData.groupRecordMachineCountLimit).map(([group, limit]) => ({
        group,
        limit,
      }))
      : [];
    setProcessedAgentData(processedAgentData);
  }, [agentData]);

  const [newLimit, setNewLimit] = useState<number>(1);
  const [editingRecord, setEditingRecord] = useState<AgentGroup | null>(null);

  const getAgentLimit = () => {
    if (props.env === 'Product' || props.env === 'Stable') {
      return 5;
    }
    return 10;
  }


  const handleLimitChange = (record: AgentGroup, value: number) => {
    setNewLimit(value ? value : 0);
    setEditingRecord(record);
    setProcessedAgentData((val: any) => {
      return val.map((item: any) => {
        if (item.group === record.group) {
          return {
            ...item,
            limit: value,
          };
        }
        return item;
      });
    });
  };

  const { run: handleLimitConfirm } = useRequest(ConfigService.updateLimit, {
    manual: true,
    onSuccess(success) {
      if (success) {
        message.success(t('message.updateSuccess', { ns: 'common' }));
        // refresh();
      } else {
        message.error(t('message.updateFailed', { ns: 'common' }));
      }
    },
  });

  const agentColumns: ColumnsType<AgentGroup> = [
    {
      title: '集群分组',
      dataIndex: 'group',
      align: 'center',
      render: (text) => <Typography.Text copyable>{text}</Typography.Text>,
    },
    {
      title: '机器数量',
      dataIndex: 'limit',
      align: 'center',
      render: (text, record) => (
        <InputNumber
          precision={0}
          min={0}
          max={getAgentLimit()}
          value={text}
          style={{ width: '10%' }}
          onChange={(value) => handleLimitChange(record, value)}
          onPressEnter={(e) => handleLimitConfirm(record, props.appId, props.env, newLimit)}
          onBlur={(e) => handleLimitConfirm(record, props.appId, props.env, newLimit)}
        />
      ),
    },
  ];

  const { run: updateAgentSwitch } = useRequest(ConfigService.agentSwitch, {
    manual: true,
    onSuccess(success) {
      if (success) {
        message.success(t('message.updateSuccess', { ns: 'common' }));
        // refresh();
        // 调用 addUserAction
        const eventId = isAgentSettingEnabled ? UserActionEnum.TURN_AGENT_ON : UserActionEnum.TURN_AGENT_OFF;
        addUserAction(eventId, { env: props.env, appId: props.appId });
      } else {
        message.error(t('message.updateFailed', { ns: 'common' }));
      }
    },
  });

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [isAgentSettingEnabled, setIsAgentSettingEnabled] = useState(false); // 初始化开关状态
  const handleSwitchChange = (checked) => {
    setIsAgentSettingEnabled(checked); // 更新开关状态
    updateAgentSwitch(props.env, props.appId, checked);
  };

  useEffect(() => {
    setIsAgentSettingEnabled(!!agentData?.envLoadState);
  }, [agentData]);

  return (
    <>
      <Form.Item label='环境开关'>
        <Switch checked={isAgentSettingEnabled} onChange={handleSwitchChange} />
      </Form.Item>
      {isAgentSettingEnabled &&
        <Table
          bordered
          size='small'
          pagination={false}
          loading={loadingAgentList}
          dataSource={processedAgentData || []}
          columns={agentColumns}
        />}
    </>
  );
};

export default AgentSetting;
