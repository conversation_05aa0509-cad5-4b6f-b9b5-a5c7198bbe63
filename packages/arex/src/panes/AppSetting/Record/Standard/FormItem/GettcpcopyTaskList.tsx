import { useRequest } from 'ahooks';
import {
  Button,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Switch,
  Table,
} from 'antd';
import dayjs from 'dayjs';
import React, { FC, forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { ConfigService } from '@/services';
import { TaskData } from '@/services/ConfigService/agentInstances/tcpcopytaskList';

// 使用TaskData作为TaskItem类型
type TaskItem = TaskData;

const formatTime = (timestamp: number) => dayjs(timestamp).format('YYYY-MM-DD HH:mm');
export interface TcpcopyTaskListProps {
  appId: string;
  env: string;
}

// 修改组件定义，使用forwardRef
const GettcpcopyTaskList = forwardRef<{ refresh: () => void }, TcpcopyTaskListProps>(
  (props, ref) => {
    const [tcpcopytasklistdata, setTcpcopytasklistData] = useState<TaskItem[]>([]);
    const [currentTask, setCurrentTask] = useState<TaskItem | null>(null);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [statusLoading, setStatusLoading] = useState<Record<string, boolean>>({});

    const [pagination, setPagination] = useState({
      current: 1,
      pageSize: 5,
    });

    const { data: tasklistData, run: refresh } = useRequest(
      () => ConfigService.getTcpcopytasklist(props.appId, props.env),
      {
        ready: !!props.appId && !!props.env,
        onError: (err) => console.error('请求错误:', err),
      },
    );

    // 暴露refresh方法给父组件
    useImperativeHandle(ref, () => ({
      refresh,
    }));

    // console.log('tasklistData', tasklistData);

    useEffect(() => {
      // 处理从后端获取的数据
      // const processedAgentData = tasklistData
      //   ? Object.entries(tasklistData).map(([group, limit]) => ({
      //       group,
      //       limit,
      //     }))
      //   : [];
      setTcpcopytasklistData(tasklistData);
    }, [tasklistData]);

    // 打开编辑模态框
    const handleEdit = (record: TaskItem) => {
      setCurrentTask(record);
      // console.log('取到的record', record);

      form.setFieldsValue({
        ...record,
        timeUnit: record.timeUnit.toLowerCase(), // 转换时间单位格式
      });
      setEditModalVisible(true);
    };

    // 提交编辑
    const handleEditSubmit = async () => {
      // console.log('原始环境值:', currentTask!.envEnum);

      try {
        const values = await form.validateFields();

        // 添加日志，检查环境值
        // console.log('提交编辑任务参数:', {
        //   ...values,
        //   id: currentTask?.id,
        //   creator: currentTask?.creator,
        //   clusterName: currentTask?.clusterName,
        //   env: currentTask?.envEnum || props.env, // 使用props.env作为备选
        //   ip: currentTask?.ip,
        // });

        const success = await ConfigService.updateTcpcopyTask({
          ...values,
          id: currentTask!.id,
          creator: currentTask!.creator, // 保留原始创建人
          clusterName: currentTask!.clusterName,
          env: props.env as 'Product' | 'Test' | 'SandBox' | 'Stable', // 使用props中的env替代currentTask中的envEnum
          ip: currentTask!.ip, // 保留原始IP
        });

        if (success) {
          message.success('任务更新成功');
          setEditModalVisible(false);
          refresh(); // 刷新列表
        }
      } catch (error) {
        // console.error('更新任务失败:', error);
        message.error(error.message || '更新失败');
      }
    };

    //删除
    const handleDelete = async (taskId: string) => {
      try {
        // 修改为正确的参数格式
        const success = await ConfigService.deleteTcpcopyTask({
          taskId: taskId, // 确保taskId不为空
          env: props.env as 'Product' | 'Test' | 'SandBox' | 'Stable',
        });

        if (success) {
          message.success('任务删除成功');
          refresh(); // 刷新列表
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error(error.message || '删除失败');
      }
    };

    //启动、停止
    const handleStatusChange = async (taskId: string, checked: boolean) => {
      setStatusLoading((prev) => ({ ...prev, [taskId]: true }));

      try {
        let success: boolean;

        if (checked) {
          // 启动任务
          success = await ConfigService.startTcpcopyTask({
            taskId,
            env: props.env as 'Product' | 'Test' | 'SandBox' | 'Stable',
          });
        } else {
          // 停止任务
          success = await ConfigService.stopTcpcopyTask({
            taskId,
            env: props.env as 'Product' | 'Test' | 'SandBox' | 'Stable',
          });
        }

        if (success) {
          message.success(`任务已${checked ? '启动' : '停止'}`);
          refresh(); // 刷新列表
        }
      } catch (error) {
        message.error(error.message || '状态切换失败');
        // 回滚Switch状态
        const updatedData = tcpcopytasklistdata.map((item) =>
          item.id === taskId ? { ...item, enableRun: !checked } : item,
        );
        setTcpcopytasklistData(updatedData);
      } finally {
        setStatusLoading((prev) => ({ ...prev, [taskId]: false }));
      }
    };

    const taskListcolumns = [
      { title: '任务名称', dataIndex: 'taskName', key: 'taskName' },
      { title: '录制IP', dataIndex: 'ip', key: 'ip' },
      {
        title: '录制时长',
        render: (record: TaskItem) => `${record.recordTime} ${record.timeUnit}`,
      },
      { title: 'QPS', dataIndex: 'recordQps', key: 'recordQps' },
      {
        title: '开始时间',
        render: (record: TaskItem) => formatTime(record.startTimestamp),
      },
      {
        title: '结束时间',
        render: (record: TaskItem) => formatTime(record.endTimestamp),
      },
      { title: '创建人', dataIndex: 'creator', key: 'creator' },
      {
        title: '创建时间',
        render: (record: TaskItem) => formatTime(record.createTimestamp),
      },
      {
        title: '操作',
        render: (record: TaskItem) => (
          <Space>
            <Switch
              checked={record.enableRun}
              onChange={(checked) => handleStatusChange(record.id, checked)}
              loading={statusLoading[record.id]}
            />
            <Button size='small' onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Popconfirm title='确认删除？' onConfirm={() => handleDelete(record.id)}>
              <Button danger size='small'>
                删除
              </Button>
            </Popconfirm>
          </Space>
        ),
      },
    ];

    // 处理分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
      setPagination({
        current: page,
        pageSize: pageSize || pagination.pageSize,
      });
    };

    // 处理每页条数变化
    const handlePageSizeChange = (current: number, size: number) => {
      setPagination({
        current: current,
        pageSize: size,
      });
      refresh(); // 刷新数据
    };

    return (
      <>
        <Table
          columns={taskListcolumns}
          dataSource={tcpcopytasklistdata || []}
          scroll={{ x: 'max-content' }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: handlePaginationChange,
            onShowSizeChange: handlePageSizeChange,
            pageSizeOptions: ['5', '10', '20', '50', '100'], // 添加这一行，自定义分页选项
          }}
          rowKey='id'
          size='middle'
        />
        <Modal
          title='编辑TCP录制任务'
          open={editModalVisible}
          onOk={handleEditSubmit}
          onCancel={() => setEditModalVisible(false)}
          width={700}
          destroyOnClose
          // 添加以下属性，阻止事件冒泡
          modalRender={(modal) => <div onClick={(e) => e.stopPropagation()}>{modal}</div>}
        >
          <Form
            form={form}
            layout='horizontal'
            labelCol={{ span: 6 }}
            // 添加onClick事件阻止冒泡
            onClick={(e) => e.stopPropagation()}
          >
            <Form.Item
              name='taskName'
              label='任务名称'
              rules={[{ required: true, message: '请输入任务名称' }]}
            >
              <Input readOnly disabled />
            </Form.Item>

            <Form.Item
              name='recordTime'
              label='录制时长'
              rules={[{ required: true, message: '请输入时长' }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item name='timeUnit' label='时间单位' rules={[{ required: true }]}>
              <Select>
                <Select.Option value='minutes'>分钟</Select.Option>
                <Select.Option value='hours'>小时</Select.Option>
                <Select.Option value='days'>天</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name='recordQps'
              label='录制QPS'
              rules={[
                { required: true, message: '请输入QPS值' },
                { type: 'number', min: 1 },
              ]}
            >
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label='IP地址'>
              <Input value={currentTask?.ip} readOnly disabled />
            </Form.Item>

            <Form.Item label='创建人'>
              <Input value={currentTask?.creator} readOnly disabled />
            </Form.Item>
          </Form>
        </Modal>
      </>
    );
  },
);

export default GettcpcopyTaskList;
