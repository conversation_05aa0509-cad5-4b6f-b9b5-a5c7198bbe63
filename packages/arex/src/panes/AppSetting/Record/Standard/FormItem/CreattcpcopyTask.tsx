import { getLocalStorage, useTranslation } from '@arextest/arex-core';
import { Button, Form, Input, message, Modal, Select } from 'antd';
import React, { FC, useCallback, useState } from 'react';

import { EMAIL_KEY } from '@/constant';
import { ConfigService } from '@/services';

export type CreateTcpcopyTaskProps = {
  appId: string;
  env: string;
  onSuccess?: () => void; // 成功回调
  setActiveKeys?: (updater: (prev: string[]) => string[]) => void; // 添加这个属性
  refreshTaskList?: () => void; // 添加刷新列表的回调函数
};

const CreateTcpcopyTask: FC<CreateTcpcopyTaskProps> = ({
  appId,
  env,
  onSuccess,
  setActiveKeys,
  refreshTaskList,
}) => {
  const { t } = useTranslation(['components', 'common']);
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const creator = getLocalStorage<string>(EMAIL_KEY);

  // 如果没有传入setActiveKeys，则创建一个空函数避免报错
  const updateActiveKeys = setActiveKeys || (() => {});

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const clusterName = appId;
      await ConfigService.createTcpcopyTask({ clusterName, creator, env, ...values });
      onSuccess?.(); // 成功时触发回调
      refreshTaskList?.(); // 刷新任务列表
      setVisible(false);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '新建失败';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  const selectAfter = (
    <Form.Item name='timeUnit' noStyle initialValue='hours'>
      <Select defaultValue='小时' style={{ width: 100 }}>
        <Select.Option value='minutes'>分钟</Select.Option>
        <Select.Option value='hours'>小时</Select.Option>
        <Select.Option value='days'>天</Select.Option>
      </Select>
    </Form.Item>
  );

  return (
    <>
      <Button
        type='primary'
        onClick={(e) => {
          e.stopPropagation(); // 阻止事件冒泡
          setVisible(true);
        }}
      >
        {t('新建')}
      </Button>

      <Modal
        title={t('新建TCP录制任务')}
        open={visible}
        onOk={handleSubmit}
        onCancel={handleClose}
        confirmLoading={loading}
        destroyOnClose
        // 添加以下属性，阻止事件冒泡
        modalRender={(modal) => <div onClick={(e) => e.stopPropagation()}>{modal}</div>}
      >
        <Form
          form={form}
          layout='vertical'
          // 添加onClick事件阻止冒泡
          onClick={(e) => e.stopPropagation()}
        >
          <Form.Item
            label={t('任务名称')}
            name='taskName'
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder='请输入任务名称' />
          </Form.Item>

          <Form.Item
            label={t('录制IP')}
            name='ip'
            rules={[{ pattern: /^((\d{1,3}\.){3}\d{1,3})$/, message: '无效的IP' }]}
          >
            <Input placeholder='请输入正确的IP地址' />
          </Form.Item>

          <Form.Item label={t('录制时长')} name='recordTime'>
            <Input addonAfter={selectAfter} />
          </Form.Item>

          <Form.Item label={t('录制频率')} name='recordQps'>
            <Input addonAfter='QPS' />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CreateTcpcopyTask;
