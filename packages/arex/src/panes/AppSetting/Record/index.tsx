import { HelpTooltip, useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { useRequest } from 'ahooks';
import {
  App,
  Button,
  Checkbox,
  Collapse,
  ConfigProvider,
  Form,
  Input,
  InputNumber,
  Segmented,
  Select,
  Switch,
  Tabs,
  theme,
  TimePicker,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { FC, useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';

import { ApplicationService, ConfigService } from '@/services';
import { QueryRecordSettingRes, SerializeSkipInfo } from '@/services/ConfigService';

import SettingForm from '../SettingForm';
import {
  DurationInput,
  DynamicClassesEditableTable,
  IntegerStepSlider,
  SerializeSkip,
} from './Standard/FormItem';
import AgentSetting from './Standard/FormItem/AgentSetting';
import CreatTcpcopyTask from './Standard/FormItem/CreattcpcopyTask';
import GettcpcopyTaskList from './Standard/FormItem/GettcpcopyTaskList';
import RunningStatus from './Standard/FormItem/RunningStatus';
import { decodeWeekCode, encodeWeekCode } from './Standard/utils';

export type SettingRecordProps = {
  env: string;
  appId: string;
};

type SettingFormType = {
  env: string;
  allowDayOfWeeks: number[];
  sampleRate: number;
  period: Dayjs[];
  timeMock: boolean;
  excludeServiceOperationSet: string[];
  recordMachineCountLimit?: number;
  includeServiceOperationSet: string[] | undefined;
  serializeSkipInfoList?: SerializeSkipInfo[];
  collectCoveragePackages?: string[];
  serializationType?: string; // 修改为字符串类型
  enableDebugIpSet?: string[];
};

// const [refreshKey, setRefreshKey] = useState(0);
const format = 'HH:mm';
const defaultValues: Omit<
  QueryRecordSettingRes,
  'appId' | 'modifiedTime' | 'allowDayOfWeeks' | 'allowTimeOfDayFrom' | 'allowTimeOfDayTo' | 'env'
> & {
  env: string;
  allowDayOfWeeks: number[];
  period: Dayjs[];
  timeMock: boolean;
  includeServiceOperationSet: string[];
  serializeSkipInfoList: SerializeSkipInfo[];
  collectCoveragePackages?: string[];
  serializationType?: string; // 修改为字符串类型
  enableDebugIpSet?: string[];
} = {
  env: 'Product',
  allowDayOfWeeks: [],
  sampleRate: 1,
  period: [dayjs('00:01', format), dayjs('23:59', format)],
  timeMock: false,
  excludeServiceOperationSet: [],
  recordMachineCountLimit: 1,
  includeServiceOperationSet: [],
  serializeSkipInfoList: [],
  collectCoveragePackages: [],
  serializationType: '1', // 修改为字符串
  enableDebugIpSet: [],
};

const SettingRecord: FC<SettingRecordProps> = (props) => {
  const { message } = App.useApp();
  const { t } = useTranslation(['components', 'common']);
  const [initialValues, setInitialValues] = useImmer<SettingFormType>(defaultValues);
  const [loading, setLoading] = useState(false);
  const [env, setEnv] = useState('Product');
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  const [activeKeys, setActiveKeys] = useState<string[]>([
    'agentSetting',
    'runningStatus',
    'basic',
    'tcpcopyTaskList',
  ]); // 初始展开的面板

  /**
   * 请求 InterfacesList
   */
  const { data: operationList = [] } = useRequest(
    () =>
      ApplicationService.queryInterfacesList<'Global'>({
        appId: props.appId as string,
        env: 'Product',
      }),
    {
      ready: !!props.appId,
    },
  );

  useRequest(ConfigService.queryRecordSetting, {
    defaultParams: [{ appId: props.appId, env: 'Product' }],
    onBefore() {
      setLoading(true);
    },
    onSuccess(res: any) {
      if (res == null) {
        setLoading(false);
        return;
      }
      setInitialValues((draft) => {
        draft.period = [
          dayjs(res.allowTimeOfDayFrom || '00:00', format),
          dayjs(res.allowTimeOfDayTo || '23:59', format),
        ];
        draft.env = res.env;
        draft.sampleRate = res.sampleRate;
        draft.allowDayOfWeeks = [];
        draft.timeMock = res.timeMock;
        draft.excludeServiceOperationSet = res.excludeServiceOperationSet?.filter(Boolean) || [];
        draft.recordMachineCountLimit =
          res?.recordMachineCountLimit == undefined ? 1 : res?.recordMachineCountLimit;
        draft.includeServiceOperationSet =
          res.extendField?.includeServiceOperations?.split(',').filter(Boolean) || [];
        draft.serializeSkipInfoList = res.serializeSkipInfoList || [];
        draft.collectCoveragePackages = res.collectCoveragePackages || [];
        draft.serializationType = res.serializationType?.toString() || '1'; // 确保为字符串
        draft.enableDebugIpSet = res.enableDebugIpSet || [];
      });
      setInitialValues((draft) => {
        draft.allowDayOfWeeks = decodeWeekCode(res.allowDayOfWeeks);
      });
      setLoading(false);
    },
  });

  const { run: update } = useRequest(ConfigService.updateRecordSetting, {
    manual: true,
    onSuccess(res) {
      res && message.success(t('message.updateSuccess', { ns: 'common' }));
    },
  });

  const { run: change } = useRequest(ConfigService.queryRecordSetting, {
    manual: true,
    onSuccess(res) {
      if (!res) {
        return;
      }
      setInitialValues((draft) => {
        draft.period = [
          dayjs(res.allowTimeOfDayFrom || '00:00', format),
          dayjs(res.allowTimeOfDayTo || '23:59', format),
        ];
        draft.env = res.env;
        draft.sampleRate = res.sampleRate;
        draft.allowDayOfWeeks = [];
        draft.timeMock = res.timeMock;
        draft.excludeServiceOperationSet = res.excludeServiceOperationSet?.filter(Boolean) || [];
        draft.recordMachineCountLimit =
          res?.recordMachineCountLimit == undefined ? 1 : res?.recordMachineCountLimit;
        draft.includeServiceOperationSet =
          res.extendField?.includeServiceOperations?.split(',').filter(Boolean) || [];
        draft.serializeSkipInfoList = res.serializeSkipInfoList || [];
        draft.collectCoveragePackages = res.collectCoveragePackages || [];
        draft.serializationType = res.serializationType?.toString() || '1'; // 确保为字符串
        draft.enableDebugIpSet = res.enableDebugIpSet || [];
      });
      setInitialValues((draft) => {
        draft.allowDayOfWeeks = decodeWeekCode(res.allowDayOfWeeks);
      });
    },
  });

  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(initialValues);
  }, [initialValues, form]);

  const onFinish = (values: SettingFormType) => {
    const allowDayOfWeeks = encodeWeekCode(values.allowDayOfWeeks);
    const [allowTimeOfDayFrom, allowTimeOfDayTo] = values.period.map((m: unknown) =>
      m.format(format),
    );

    const params = {
      env: env,
      allowDayOfWeeks,
      allowTimeOfDayFrom,
      allowTimeOfDayTo,
      appId: props.appId,
      sampleRate: values.sampleRate,
      timeMock: values.timeMock,
      excludeServiceOperationSet: values.excludeServiceOperationSet?.filter(Boolean),
      recordMachineCountLimit: values.recordMachineCountLimit,
      extendField: {
        includeServiceOperations: values.includeServiceOperationSet?.join(','),
      },
      serializeSkipInfoList: values.serializeSkipInfoList,
      collectCoveragePackages: values.collectCoveragePackages,
      serializationType: parseInt(values.serializationType || '1', 10), // 转换为数字
      enableDebugIpSet: values.enableDebugIpSet,
    };
    update(params);
  };

  const tcpcopyTaskListRefreshRef = useRef<{ refresh: () => void }>(null);

  // 添加一个确保tcpcopyTaskList始终展开的函数
  // 修复updateActiveKeys函数，确保它能正确处理传入的参数类型
  // 修改updateActiveKeys函数，允许手动折叠tcpcopyTaskList
  const updateActiveKeys = (keys: string[] | string) => {
    // 确保keys是数组
    const newKeys = Array.isArray(keys) ? keys : [keys].filter(Boolean);

    // 不再强制tcpcopyTaskList始终展开，而是尊重用户的折叠/展开操作
    setActiveKeys(newKeys);
  };

  const renderForm = () => {
    return (
      <SettingForm loading={loading} initialValues={initialValues} form={form} onFinish={onFinish}>
        <Collapse
          activeKey={activeKeys} // 控制当前展开的面板
          onChange={updateActiveKeys} // 使用新的函数确保tcpcopyTaskList始终展开
          bordered={false}
          defaultActiveKey={['agentSetting', 'runningStatus', 'basic', 'tcpcopyTaskList']}
          items={[
            {
              key: 'agentSetting',
              label: t('appSetting.agentSetting'),
              children: (
                <>
                  <AgentSetting appId={props.appId} env={env} />
                </>
              ),
            },
            {
              key: 'runningStatus',
              label: t('appSetting.runningStatus'),
              children: (
                <>
                  <RunningStatus appId={props.appId} env={env} />
                </>
              ),
            },
            {
              key: 'basic',
              label: t('appSetting.basic'),
              children: (
                <>
                  <Form.Item label={t('appSetting.duration')} name='allowDayOfWeeks'>
                    <DurationInput />
                  </Form.Item>
                  <Form.Item label={t('appSetting.period')} name='period'>
                    <TimePicker.RangePicker format={format} />
                  </Form.Item>
                  <Form.Item label={t('appSetting.frequency')} name='sampleRate'>
                    <IntegerStepSlider />
                  </Form.Item>
                  <Form.Item label={t('appSetting.serializeSkip')} name='serializeSkipInfoList'>
                    <SerializeSkip />
                  </Form.Item>
                </>
              ),
            },
            {
              key: 'advanced',
              label: t('appSetting.advanced'),
              forceRender: true,
              children: (
                <>
                  <Form.Item
                    label={t('appSetting.timeMock')}
                    name='timeMock'
                    valuePropName='checked'
                  >
                    <Checkbox />
                  </Form.Item>
                  <Form.Item
                    label={
                      <HelpTooltip title={t('appSetting.dynamicClassesTooltip')}>
                        {t('appSetting.dynamicClasses')}
                      </HelpTooltip>
                    }
                  >
                    <DynamicClassesEditableTable appId={props.appId} env={env} />
                  </Form.Item>
                  <Form.Item
                    label={
                      <HelpTooltip title={t('appSetting.inclusionTooltip')}>
                        {t('appSetting.inclusion')}
                      </HelpTooltip>
                    }
                    name='includeServiceOperationSet'
                  >
                    <Select
                      allowClear
                      mode='tags'
                      options={[...new Set(operationList.map((item) => item.operationName))]
                        .filter(Boolean)
                        .map((name) => ({
                          label: name,
                          value: name,
                        }))}
                    />
                  </Form.Item>
                  <Form.Item
                    label={
                      <HelpTooltip title={t('appSetting.exclusionTooltip')}>
                        {t('appSetting.exclusion')}
                      </HelpTooltip>
                    }
                    name='excludeServiceOperationSet'
                  >
                    <Select
                      allowClear
                      mode='tags'
                      options={[...new Set(operationList.map((item) => item.operationName))]
                        .filter(Boolean)
                        .map((name) => ({
                          label: name,
                          value: name,
                        }))}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t('appSetting.collectCoveragePackages')}
                    name='collectCoveragePackages'
                  >
                    <Select allowClear mode='tags' />
                  </Form.Item>
                  <Form.Item label={t('appSetting.serializationType')} name='serializationType'>
                    <Select
                      allowClear
                      options={[
                        { label: 'DEFAULT', value: '0' },
                        { label: 'JACKSON', value: '1' },
                        { label: 'GSON', value: '2' },
                      ]}
                      value={initialValues.serializationType} // 确保值正确
                    />
                  </Form.Item>
                  <Form.Item label={t('appSetting.enableDebugIp')} name='enableDebugIpSet'>
                    <Select allowClear mode='tags' />
                  </Form.Item>
                </>
              ),
            },

            {
              key: 'tcpcopyTaskList',
              label: t('appSetting.tcpcopyTaskList'),
              children: (
                <>
                  <GettcpcopyTaskList
                    appId={props.appId}
                    env={env}
                    ref={tcpcopyTaskListRefreshRef}
                  />
                </>
              ),
              extra: (
                <CreatTcpcopyTask
                  appId={props.appId}
                  env={env}
                  setActiveKeys={updateActiveKeys} // 使用新的函数
                  refreshTaskList={() => tcpcopyTaskListRefreshRef.current?.refresh()}
                  onSuccess={() => {
                    message.success(t('createSuccess'));
                    form.resetFields();
                  }}
                />
              ),
            },
          ]}
          css={css`
            .ant-collapse-header-text {
              font-weight: 600;
            }
          `}
        />
        <Form.Item style={{ float: 'right', margin: '16px 0' }}>
          <Button type='primary' htmlType='submit'>
            {t('save', { ns: 'common' })}
          </Button>
        </Form.Item>
      </SettingForm>
    );
  };

  const optionsItems = [
    { key: 'Product', label: 'Product', value: 'Product', children: renderForm() },
    { key: 'SandBox', label: 'SandBox', value: 'SandBox', children: renderForm() },
    { key: 'Stable', label: 'Stable', value: 'Stable', children: renderForm() },
    { key: 'Test', label: 'Test', value: 'Test', children: renderForm() },
  ];

  return (
    <ConfigProvider
      theme={{
        components: {
          Segmented: {
            itemSelectedBg: token.colorPrimary,
            algorithm: true,
          },
        },
      }}
    >
      <Segmented
        block
        value={env}
        options={optionsItems}
        onChange={(env) => {
          setEnv(env as string);
          change({ appId: props.appId, env: env.toString() });
        }}
        style={{ margin: '0 8px 8px', backgroundColor: 'rgb(245,245,245)' }}
      />
      <Tabs
        activeKey={env}
        items={optionsItems}
        css={css`
          .ant-tabs-nav {
            display: none;
          }
        `}
      />
    </ConfigProvider>
  );
};

export default SettingRecord;
