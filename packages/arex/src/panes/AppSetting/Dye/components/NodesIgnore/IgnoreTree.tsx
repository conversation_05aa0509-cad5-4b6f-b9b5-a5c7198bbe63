/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2024-06-13 19:16:55
 * @modify date 2024-06-13 19:16:55
 * @desc [节点树]
 */
import { SaveOutlined } from '@ant-design/icons';
import { EmptyWrapper, useTranslation } from '@arextest/arex-core';
import { Button, Card, Tree } from 'antd';
import { TreeProps } from 'antd/es';
import React, { FC, useMemo } from 'react';

import { getIgnoreNodes } from './utils';

type IgnoreTreeProps = Omit<TreeProps, 'treeData'> & {
  loading?: boolean;
  treeData: object;
  onAdd?: () => void;
};

const IgnoreTree: FC<IgnoreTreeProps> = (props) => {
  const { t } = useTranslation(['components', 'common']);
  let treeData = useMemo(() => getIgnoreNodes(props.treeData, ''), [props.treeData]);

  // 非叶子结点不可选
  const formatTreeData = (data: any[]): any[] => {
    return data.map((item) => {
      item.disabled = !!item.children?.length;
      if (item.children) {
        // 递归
        item.children = formatTreeData(item.children);
      }
      return item;
    });
  };
  treeData = formatTreeData(treeData);
  console.log(treeData);

  return (
    <Card
      size='small'
      title={t('appSetting.clickToIgnore')}
      extra={
        <Button size='small' type='primary' icon={<SaveOutlined />} onClick={props.onAdd}>
          {t('save', { ns: 'common' })}
        </Button>
      }
    >
      <EmptyWrapper
        loading={props.loading}
        empty={!Object.keys(props.treeData).length}
        description={t('appSetting.emptyContractTip')}
      >
        <Tree multiple defaultExpandAll height={400} {...props} treeData={treeData} />
      </EmptyWrapper>
    </Card>
  );
};

export default IgnoreTree;
