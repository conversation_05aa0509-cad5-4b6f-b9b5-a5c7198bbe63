import { SearchOutlined } from '@ant-design/icons';
import { TooltipButton, useTranslation } from '@arextest/arex-core';
import { ButtonProps, Space, Typography } from 'antd';
import React, { FC } from 'react';

export type DyeConfigTitleProps = {
  title: string;
  onSearch?: ButtonProps['onClick'];
};

const DyeConfigTitle: FC<DyeConfigTitleProps> = (props) => {
  const { t } = useTranslation();

  return (
    <Space>
      <Typography.Text strong>{props.title}</Typography.Text>
      <div className='compare-config-title-actions'>
        <TooltipButton
          key='search'
          icon={<SearchOutlined />}
          title={t('search')}
          onClick={props.onSearch}
        />
      </div>
    </Space>
  );
};

export default DyeConfigTitle;
