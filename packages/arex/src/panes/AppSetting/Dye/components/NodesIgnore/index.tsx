import { CloseOutlined, DeleteOutlined } from '@ant-design/icons';
import { css, SmallTextButton, SpaceBetweenWrapper, useTranslation } from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import {
  App,
  Button,
  ButtonProps,
  Card,
  Collapse,
  Input,
  InputRef,
  List,
  Space,
  Typography,
} from 'antd';
import { TreeProps } from 'antd/es';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useImmer } from 'use-immer';

import { CONFIG_TARGET } from '@/panes/AppSetting/CompareConfig';
import { ComparisonService } from '@/services';
import { OperationId } from '@/services/ApplicationService';
import { QueryDyeIgnoreNode } from '@/services/ComparisonService';

import DyeConfigTitle from './DyeConfigTitle';
import IgnoreTree from './IgnoreTree';

const ActiveKey = 'sort';

type CheckedNodesData = {
  operationId?: OperationId<'Global'>;
  exclusionsList: string[];
  compareTypeList: string[];
};

export type NodesIgnoreProps = {
  appId?: string;
  operationId?: string;
  syncing?: boolean;
  loadingContract?: boolean;
  // 2 请求，1，响应
  configTypeValue?: string;
  configTarget: CONFIG_TARGET;
  contractParsed: { [p: string]: any };
  currentInterface: any;
  onAdd?: () => void;
  onSync?: () => void;
};

const NodesIgnore: FC<NodesIgnoreProps> = (props) => {
  const { message } = App.useApp();
  const [t] = useTranslation(['components', 'common']);

  const searchRef = useRef<InputRef>(null);

  const [activeKey, setActiveKey] = useState<string | string[]>([ActiveKey]);
  const [search, setSearch] = useState<string | false>(false);

  const [checkedNodesData, setCheckedNodesData] = useImmer<CheckedNodesData>({
    exclusionsList: [],
    compareTypeList: [],
  });

  const {
    data: ignoreNodeList = [],
    loading: loadingIgnoreNode,
    run: queryIgnoreNode,
    mutate: setIgnoreNodeList,
  } = useRequest(
    () => {
      return ComparisonService.queryDyeIgnoreNode({
        appId: props.appId!,
        operationId: props.operationId,
        colorType: props.configTypeValue === '2' ? 'req' : 'res',
      });
    },
    {
      ready: !!(
        props.appId &&
        (props.configTarget === CONFIG_TARGET.GLOBAL || // GLOBAL ready
          (props.configTarget === CONFIG_TARGET.INTERFACE && props.operationId))
      ),
      refreshDeps: [props.operationId, props.configTarget, props.configTypeValue],
      onSuccess: convertIgnoreNode,
    },
  );

  useEffect(() => {
    setIgnoreNodeList([]);
  }, [props.configTarget]);

  function convertIgnoreNode(data: QueryDyeIgnoreNode[]) {
    setCheckedNodesData((state) => {
      state.operationId = props.operationId;
      state.exclusionsList = data.map((item) => item.path || '');
    });
  }

  const ignoreNodesFiltered = useMemo(
    () =>
      typeof search === 'string' && search
        ? ignoreNodeList.filter((node) =>
            node.colorings.join('/').toLowerCase().includes(search.toLowerCase()),
          )
        : ignoreNodeList,
    [ignoreNodeList, search],
  );

  /**
   * 删除 IgnoreNode
   */
  const { run: doDeleteIgnoreNode } = useRequest(ComparisonService.deleteDyeIgnoreNode, {
    manual: true,
    onSuccess(success) {
      if (success) {
        queryIgnoreNode();
        message.success(t('message.delSuccess', { ns: 'common' }));
      } else {
        message.error(t('message.delFailed', { ns: 'common' }));
      }
    },
  });

  // 批量新增 IgnoreNode
  const batchInsertIgnoreNode = (list: any[]) => {
    const ajaxArr = list.map((item) => {
      return ComparisonService.insertDyeIgnoreNode(item);
    });
    // 批量新增
    Promise.allSettled(ajaxArr)
      .then(() => {
        message.success(t('message.updateSuccess', { ns: 'common' }));
      })
      .catch(() => {
        message.error(t('message.updateFailed', { ns: 'common' }));
      })
      .finally(() => {
        queryIgnoreNode();
      });
  };

  // 批量删除 IgnoreNode
  const batchDeleteIgnoreNode = (ids: string[]) => {
    console.log('批量删除', ids);
    const ajaxArr = ids.map((id: string) => {
      return ComparisonService.deleteDyeIgnoreNode(id);
    });
    // 批量删除
    Promise.allSettled(ajaxArr)
      .then(() => {
        // message.success(t('message.delSuccess', { ns: 'common' }));
      })
      .catch(() => {
        // message.error(t('message.delFailed', { ns: 'common' }));
      })
      .finally(() => {
        queryIgnoreNode();
      });
  };

  const handleIgnoreTreeSelect: TreeProps['onSelect'] = (_, info) => {
    const selected = info.selectedNodes.map((node) => node.key.toString());
    console.log(1111, 'selected', selected, info.selectedNodes);

    setCheckedNodesData((state) => {
      state.operationId = props.operationId;
      state.exclusionsList = selected;
    });
  };

  const handleSearch: ButtonProps['onClick'] = (e) => {
    activeKey?.[0] === ActiveKey && e.stopPropagation();
    setSearch('');
    setTimeout(() => searchRef.current?.focus());
  };

  async function handleIgnoreSave() {
    const { exclusionsList } = checkedNodesData;
    const exclusionsListPrev = ignoreNodeList.map((item) => item.path as string);

    const add: string[] = [],
      remove: string[] = [];

    // 计算新旧集合的差集，分别进行增量更新和批量删除
    Array.from(new Set([...exclusionsListPrev, ...exclusionsList])).forEach((path) => {
      if (exclusionsListPrev.includes(path) && exclusionsList.includes(path)) return;
      else if (exclusionsListPrev.includes(path))
        remove.push(ignoreNodeList.find((item) => item.path === path)!.id);
      else add.push(path);
    });
    const currentInterface = props.currentInterface || {};
    const params: any[] = add.map((path) => ({
      appId: props.appId,
      operationId: props.operationId,
      operationName: currentInterface.operationName,
      operationType: currentInterface.operationType,
      serviceId: currentInterface.serviceId,
      coloringConfiguration: {
        coloringFieldType: props.configTypeValue === '2' ? 'reqBodyJson' : 'resBodyJson',
        colorings: path.split('/').filter(Boolean),
      },
    }));
    batchInsertIgnoreNode(params);

    // 批量删除
    remove.length && batchDeleteIgnoreNode(remove);
  }

  return (
    <Space style={{ width: '100%' }} direction='vertical' size='middle'>
      <IgnoreTree
        defaultExpandAll
        loading={props.loadingContract}
        treeData={props.contractParsed}
        selectedKeys={[...checkedNodesData.exclusionsList]}
        onSelect={handleIgnoreTreeSelect}
        onAdd={handleIgnoreSave}
      />
      <Collapse
        size='small'
        activeKey={activeKey}
        items={[
          {
            key: ActiveKey,
            label: <DyeConfigTitle title='染色节点' onSearch={handleSearch} />,
            children: (
              <Card bordered={false} size='small'>
                <List
                  size='small'
                  dataSource={ignoreNodesFiltered}
                  loading={loadingIgnoreNode}
                  header={
                    search !== false && (
                      <SpaceBetweenWrapper style={{ padding: '0 16px' }}>
                        <Input.Search
                          size='small'
                          ref={searchRef}
                          placeholder='Search for ignored key'
                          onChange={(e) => setSearch(e.target.value)}
                          style={{ marginRight: '8px' }}
                        />
                        <Button
                          size='small'
                          type='text'
                          icon={<CloseOutlined />}
                          onClick={() => setSearch(false)}
                        />
                      </SpaceBetweenWrapper>
                    )
                  }
                  renderItem={(node) => (
                    <List.Item>
                      <SpaceBetweenWrapper width={'100%'}>
                        {/* Displaying exclusions with default color */}
                        <div
                          className='content-wrapper'
                          style={{ display: 'flex', flexDirection: 'column' }}
                        >
                          {node.colorings && node.colorings.length > 0 && (
                            <div style={{ display: 'flex', alignItems: 'baseline' }}>
                              <span style={{ marginRight: '8px', color: 'gray' }}>选中:</span>
                              <Typography.Text ellipsis>{node.colorings.join('/')}</Typography.Text>
                            </div>
                          )}
                        </div>
                        <SmallTextButton
                          icon={<DeleteOutlined />}
                          onClick={() => doDeleteIgnoreNode(node.id)}
                        />
                      </SpaceBetweenWrapper>
                    </List.Item>
                  )}
                  locale={{ emptyText: t('appSetting.noIgnoredNodes') }}
                />
              </Card>
            ),
          },
        ]}
        onChange={setActiveKey}
        css={css`
          flex: 1;
          margin: 0 16px 16px 0;
          height: fit-content;
          .ant-collapse-content-box {
            padding: 0 !important;
          }
        `}
      />
    </Space>
  );
};

export default NodesIgnore;
