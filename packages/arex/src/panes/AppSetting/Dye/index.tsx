/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2024-06-06 15:27:16
 * @modify date 2024-06-06 15:27:16
 * @desc [染色组件]
 */
import {
  Label,
  Segmented,
  tryParseJsonString,
  tryPrettierJsonString,
  useTranslation,
} from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import { message, Select, Space } from 'antd';
import React, { FC, useMemo, useState } from 'react';

import { ApplicationService, ReportService } from '@/services';

import NodesIgnore from './components/NodesIgnore';
import SyncContract from './SyncContract';

type FilterRuleProps = {
  appId: string;
  env: string;
  operationId?: string | false; // 限定接口，用于展示特定接口的对比配置, false 时不展示 operationType
};
export enum CONFIG_TARGET {
  GLOBAL,
  INTERFACE,
  DEPENDENCY,
}

const FilterRule: FC<FilterRuleProps> = (props) => {
  const [configTypeValue, setConfigTypeValue] = useState<'1' | '2'>('2');
  const [rawContract, setRawContract] = useState<string>();
  const [activeOperationId, setActiveOperationId] = useState<string | undefined>(
    props.operationId || undefined,
  );
  const [currentInterface, updateCurrentInterface] = useState<any>();
  const configTargetValue = CONFIG_TARGET.INTERFACE;

  const { t } = useTranslation(['components']);

  const { data: operationList = [] } = useRequest(
    () =>
      ApplicationService.queryInterfacesList<'Interface'>({
        appId: props.appId as string,
        env: props.env as string,
      }),
    {
      ready: !!props.appId,
      onSuccess(res) {
        !props.operationId && setActiveOperationId(res?.[0]?.id);
      },
    },
  );

  const interfaceOptions = useMemo(() => {
    const list = Object.entries(
      operationList.reduce<Record<string, { label: string; value?: string | null }[]>>(
        (options, item) => {
          (item.operationTypes || [])?.forEach((operation) => {
            const newItem = {
              label: item.operationName,
              value: item.id,
              ...item,
            };
            options[operation]
              ? options[operation].push(newItem)
              : (options[operation] = [newItem]);
          });
          return options;
        },
        {},
      ),
    );
    return list.map(([label, options]) => ({
      label,
      options,
    }));
  }, [operationList]);

  const configTypeOptions = [
    {
      label: '请求染色',
      value: '2',
    },
    {
      label: '响应染色',
      value: '1',
    },
  ];

  const { run: handleSyncResponse, loading: syncing } = useRequest(
    () =>
      ReportService.syncResponseContract({
        operationId: activeOperationId as string,
      }),
    {
      manual: true,
      ready: !!activeOperationId,
    },
  );

  const { run: handleSyncRequest } = useRequest(
    () =>
      ReportService.syncRequestContract({
        operationId: activeOperationId as string,
      }),
    {
      manual: true,
      ready: !!activeOperationId,
    },
  );

  const handleSync = () => {
    handleSyncResponse();
    handleSyncRequest();
  };

  const {
    data: contract,
    mutate: setContract,
    loading: loadingContract,
    run: queryContract,
  } = useRequest(
    (params?: { contractSource?: string }) =>
      ReportService.queryContract({
        appId: props.appId!,
        operationId: activeOperationId,
        contractSource: configTypeValue,
        ...(params || {}),
      }),
    {
      ready: !!props.appId && !syncing && !!activeOperationId,
      refreshDeps: [syncing, activeOperationId], // TODO 目前可能有一些多余的无效请求，待优化
      onBefore() {
        setContract();
        setRawContract(undefined);
      },
      onSuccess(res) {
        setRawContract(tryPrettierJsonString(res?.contract || ''));
      },
    },
  );
  const contractParsed = useMemo<{ [key: string]: any }>(() => {
    const res = contract?.contract;
    if (res) {
      const json: any = tryParseJsonString(res) || {};
      return json;
    } else {
      return {};
    }
  }, [contract]);

  const handleConfigTypeChange = (val: any) => {
    setConfigTypeValue(val);
    queryContract({ contractSource: `${val}` });
  };

  const { run: updateContract } = useRequest(ReportService.overwriteContract, {
    manual: true,
    onSuccess(success) {
      if (success) {
        message.success(t('message.updateSuccess', { ns: 'common' }));
      } else {
        message.error(t('message.updateFailed', { ns: 'common' }));
      }
    },
  });

  const handleSaveContract = (value?: string) => {
    const params = {
      appId: props.appId,
      operationId: activeOperationId,
      operationResponse: value || '',
      contractSource: configTypeValue,
    };

    updateContract(params);
  };

  // 选择接口
  const handleChangeInterface = (val: string, options: any) => {
    setActiveOperationId(val);
    updateCurrentInterface(options);
  };

  return (
    <Space direction='vertical' size='middle' style={{ width: '100%' }}>
      <Space>
        <Label type='secondary'>{t('appSetting.interface')}</Label>
        <Select
          optionFilterProp='label'
          placeholder='choose interface'
          popupMatchSelectWidth={false}
          // START 指定 operationId 时，Select 只读
          showSearch={!props.operationId}
          bordered={!props.operationId}
          suffixIcon={!props.operationId ? undefined : null}
          open={props.operationId ? false : undefined}
          // END 指定 operationId 时，Select 只读
          options={interfaceOptions}
          value={activeOperationId}
          onChange={handleChangeInterface}
          style={{ width: '260px' }}
        />
        <SyncContract
          syncing={syncing}
          value={rawContract}
          buttonsDisabled={{ leftButton: syncing }}
          onSync={handleSync}
          onEdit={queryContract}
          onSave={handleSaveContract}
        />
      </Space>
      <Segmented
        value={configTypeValue}
        options={configTypeOptions}
        onChange={handleConfigTypeChange}
      />
      <div>
        <NodesIgnore
          key='nodes-ignore'
          appId={props.appId}
          operationId={activeOperationId}
          syncing={syncing}
          loadingContract={loadingContract}
          configTarget={configTargetValue}
          contractParsed={contractParsed}
          onAdd={queryContract}
          onSync={handleSync}
          configTypeValue={configTypeValue}
          currentInterface={currentInterface || interfaceOptions?.[0]?.options[0]}
        />
      </div>
    </Space>
  );
};

export default FilterRule;
