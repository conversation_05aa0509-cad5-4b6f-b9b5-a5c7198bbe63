import { DeleteOutlined, EditOutlined, FilterOutlined, SearchOutlined } from '@ant-design/icons';
import ProTable, { ProColumns } from '@ant-design/pro-table';
import { Label, PaneDrawer, SmallTextButton, useTranslation } from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import {
  App,
  Button,
  DatePicker,
  Form,
  Input,
  InputRef,
  Modal,
  PaginationProps,
  Select,
  Table,
  theme,
  Tooltip,
} from 'antd';
import moment from 'dayjs';
import dayjs from 'dayjs';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { ComparisonService, ReportService, StorageService } from '@/services';
import { queryGroupedIpList, queryIpList } from '@/services/ConfigService';
import { queryFilterConfigurations } from '@/services/FilterConfigurationService/getFilterRuleEnum';
import { AggOperation } from '@/services/ReportService';
import { DeleteRecordType } from '@/services/StorageService';

import RecordedCaseListDetail from './RecordedCaseDetail';

const { RangePicker } = DatePicker;

export type RecordedCaseRef = {
  open: () => void;
};

export type RecordedCaseProps = {
  appId: string;
  appName?: string;
  env?: string;
  ips?: Set<string>;
  responseType?: string;
  recordId?: string;
  recordVersion?: string;
  beginTime?: string;
  endTime?: string;
  onChange?: () => void;
};

type Agent = {
  ip: string;
  agentStatus: string;
  scfServicePort: number;
  httpServicePort: number;
  currentRate: string;
  decelerateCode: number;
  genericsEnable: boolean;
  agentActiveState: number;
  cloudGroupName: string;
  cloudImageFourVersion: string;
};

interface AgentInfo {
  [key: string]: Agent[];
}

interface FormValues {
  filters?: Set<string>;
  filterRuleEnum?: number;
  compareValue?: string;
  colorType?: number;
}

const RecordedCase = forwardRef<RecordedCaseRef, RecordedCaseProps>((props, ref) => {
  const { t } = useTranslation(['components']);
  const { token } = theme.useToken();
  const { message } = App.useApp();

  const [open, setOpen] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  const searchInput = useRef<InputRef>(null);
  const [host, setHost] = useState<any[]>([]);
  const [agentsInfo, setAgentsInfo] = useState<AgentInfo>({});
  const [dataSource, setDataSource] = useState<AggOperation[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryConditions, setQueryConditions] = useState<RecordedCaseProps>([]);
  const formRef = useRef<any>();
  const [showModal, setShowModal] = useState<boolean>(false);
  const [filterRuleEnumOptions, updateFilterRuleEnumOptions] = useState<any[]>([]);
  const [colorOptions, updateColorOptions] = useState<any[]>([]);
  const [selectedColorType, updataSelectedColorType] = useState<number>();
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState<PaginationProps>({
    current: 1,
    pageSize: 10, // 提供默认的每页条目数
  });
  const [formValues, setFormValues] = useState<FormValues>({});

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
  }));

  useEffect(() => {
    const groupedIpList = async () => {
      const res: AgentInfo = await queryGroupedIpList({
        appId: props.appId,
      });
      setAgentsInfo(res);
      setHost(transformedData(res));
    };
    if (props.appId) {
      groupedIpList();
    }
    queryFilterRuleList();
    setShowModal(false);
    setDefaultCreationTime();
  }, []);

  useEffect(() => {}, [agentsInfo, host]);

  const setDefaultCreationTime = () => {
    const endTime = dayjs(); // 当前时间
    const startTime = dayjs().subtract(1, 'day'); // 昨天同一时间

    formRef.current?.setFieldsValue({
      creationTime: [startTime, endTime],
    });
  };

  const transformedData = (agentsInfo: AgentInfo) => {
    return Object.keys(agentsInfo).flatMap((key) => ({
      label: <span>{key}</span>,
      title: key,
      options: agentsInfo[key].map((agentInfo: any) => {
        return {
          label: <span>{agentInfo.ip}</span>,
          value: `${agentInfo.ip}`,
        };
      }),
    }));
  };

  const { run: deleteRecord } = useRequest(StorageService.deleteRecord, {
    manual: true,
    onSuccess(success) {
      if (success) {
        message.success(t('message.delSuccess', { ns: 'common' }));
        refresh();
        props.onChange?.();
      } else {
        message.error(t('message.delFailed', { ns: 'common' }));
      }
    },
  });

  const handleSureRule = async () => {
    // try {
    // 提交表单并获取新值
    const values = await form.validateFields();
    // 将filters字段由String转成Set<string>，不进行分割
    const filtersSet = [values.filters];
    // 更新filters字段为Set类型
    values.filters = filtersSet;
    setFormValues({ ...values, colorType: selectedColorType }); // 将selectedColorType合并到values中,保存表单值
    // 关闭模态框
    setShowModal(false);
    form.resetFields(); // 关闭模态框时重置表单数据
  };

  const updateShowModal = () => {
    setShowModal(false);
    form.resetFields(); // 关闭模态框时重置表单数据
  };

  const handleColorChange = (value, option) => {
    updataSelectedColorType(option.colorType);
  };

  const { run: queryDyeIgnoreNodes } = useRequest(
    (id) => {
      return ComparisonService.queryDyeIgnoreNode({
        appId: props.appId,
        operationId: id,
        colorType: null,
      });
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.length > 0) {
          const list = res.map((item) => {
            return {
              label: item.colorings.join('/'),
              value: item.colorings.join('/'),
              colorType: item.colorType,
            };
          });
          updateColorOptions(list);
          updataSelectedColorType(list[0].colorType), setShowModal(true);
        } else {
          message.error('请先去配置接口染色规则～');
        }
      },
    },
  );

  const queryFilterRuleList = () => {
    queryFilterConfigurations().then((res: any) => {
      const arr = [];
      for (const key in res || {}) {
        arr.push({
          label: key,
          value: res[key],
        });
      }
      updateFilterRuleEnumOptions(arr);
    });
  };

  const columns: ProColumns<AggOperation>[] = [
    // 隐藏在表格中的搜索字段
    {
      title: '环境',
      dataIndex: 'environment',
      hideInTable: true,
      valueType: 'select',
      valueEnum: {
        // 根据实际环境选项进行调整
        SandBox: t('sandbox'),
        Stable: t('stable'),
        Product: t('online'),
        Test: t('offline'),
      },
      fieldProps: {
        placeholder: t('请选择环境'),
        allowClear: true,
      },
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      hideInTable: true,
      valueType: 'text',
      renderFormItem: () => (
        <Select
          mode='tags'
          allowClear
          placeholder={t('请选择IP')}
          style={{ width: '100%' }}
          options={host}
        />
      ),
    },
    {
      title: '云平台版本号',
      dataIndex: 'cloudImageFourVersion',
      hideInTable: true,
      valueType: 'text',
      fieldProps: {
        placeholder: t('请输入云平台四位版本号'),
        allowClear: true,
      },
    },
    {
      title: '录制 ID',
      dataIndex: 'recordId',
      hideInTable: true,
      valueType: 'text',
      fieldProps: {
        placeholder: t('请输入录制 ID'),
        allowClear: true,
      },
    },
    {
      title: '状态',
      dataIndex: 'responseType',
      hideInTable: true,
      valueType: 'select',
      valueEnum: {
        // 根据实际环境选项进行调整
        NORMAL: t('Normal'),
        TIMEOUT_EXCEPTION: t('Exception_TimeOut'),
        OTHER_EXCEPTION: t('Exception_Other'),
      },
      fieldProps: {
        placeholder: t('请选择状态'),
        allowClear: true,
      },
    },
    {
      title: '创建时间',
      dataIndex: 'creationTime',
      hideInTable: true,
      valueType: 'dateRange',
      renderFormItem: () => (
        <RangePicker
          style={{ width: '100%' }}
          allowClear={false}
          defaultValue={[dayjs().subtract(1, 'day'), dayjs()]}
          disabledDate={(current) => current && current > moment().endOf('day')}
          renderExtraFooter={() => (
            <>
              <Label type='secondary'>{t('dateRangePreset.quickPick', { ns: 'common' })}</Label>
              <Button
                size={'small'}
                onClick={() => {
                  formRef.current?.setFieldsValue({
                    creationTime: [dayjs().subtract(1, 'day'), dayjs()],
                  });
                }}
              >
                {t('dateRangePreset.1d', { ns: 'common' })}
              </Button>
              <Button
                size={'small'}
                onClick={() => {
                  formRef.current?.setFieldsValue({
                    creationTime: [dayjs().startOf('day'), dayjs()],
                  });
                }}
              >
                {t('dateRangePreset.today', { ns: 'common' })}
              </Button>
            </>
          )}
        />
      ),
    },
    {
      title: t('replay.operationName'),
      dataIndex: 'operationName',
      key: 'operationName',
      hideInSearch: true, // 隐藏在搜索表单中
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
        <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
          <Input.Search
            allowClear
            enterButton
            size='small'
            ref={searchInput}
            placeholder={`${t('search', { ns: 'common' })} ${t('replay.api')}`}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onSearch={(value, event) => {
              // @ts-ignore
              if (event.target?.localName === 'input') return;
              confirm();
            }}
            onPressEnter={() => confirm()}
          />
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined style={{ color: filtered ? token.colorPrimaryActive : undefined }} />
      ),
      onFilter: (value, record) =>
        record.operationName
          .toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()),
      onFilterDropdownOpenChange: (visible) => {
        visible && setTimeout(() => searchInput.current?.select(), 100);
      },
    },
    {
      title: t('replay.recordedCaseCount'),
      dataIndex: 'recordedCaseCount',
      key: 'recordedCaseCount',
      hideInSearch: true, // 隐藏在搜索表单中
      sorter: (a, b) => a.recordedCaseCount - b.recordedCaseCount,
      defaultSortOrder: 'descend',
    },
    {
      title: t('action', { ns: 'common' }),
      align: 'center',
      hideInSearch: true, // 隐藏在搜索表单中
      render: (value, record) => (
        <>
          <SmallTextButton
            size='small'
            icon={<DeleteOutlined />}
            title={t('delete', { ns: 'common' })}
            onClick={() => {
              deleteRecord({
                appId: props.appId,
                operationName: record.operationName,
                type: DeleteRecordType.ByAppIdAndOperationName,
              });
            }}
          />
          {expandedRowKeys.includes(record.id) ? (
            <SmallTextButton
              size='small'
              icon={<EditOutlined />}
              title={t('筛选', { ns: 'common' })}
              onClick={() => queryDyeIgnoreNodes(record.id)}
            />
          ) : (
            <Tooltip title='请先展开接口录制信息列表～'>
              <SmallTextButton
                size='small'
                icon={<EditOutlined />}
                title={t('筛选', { ns: 'common' })}
                onClick={() => queryDyeIgnoreNodes(record.id)}
                disabled
              />
            </Tooltip>
          )}
          <Modal
            open={showModal}
            title='规则'
            onOk={handleSureRule}
            onCancel={() => {
              updateShowModal();
            }}
          >
            <Form labelCol={{ style: { width: 80 } }} form={form}>
              <Form.Item label='染色节点' name='filters'>
                <Select options={colorOptions} placeholder='请选择' onChange={handleColorChange} />
              </Form.Item>
              <Form.Item label='表达式' name='filterRuleEnum'>
                <Select options={filterRuleEnumOptions || []} placeholder='请选择' />
              </Form.Item>
              <Form.Item label='值' name='compareValue'>
                <Input placeholder='请输入' />
              </Form.Item>
            </Form>
          </Modal>
        </>
      ),
    },
  ];

  const handleFormReset = () => {
    formRef.current.resetFields();
  };

  // 处理行展开或折叠事件
  const handleExpandChange = (expanded: boolean, record: AggOperation) => {
    setFormValues({}); // 将selectedColorType合并到values中,保存表单值
    setExpandedRowKeys(expanded ? [record.id] : []);
  };

  return (
    <>
      <ProTable<AggOperation>
        size='small'
        rowKey='id'
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        formRef={formRef}
        onReset={handleFormReset}
        request={async (params) => {
          // 清理空字符串和空数组的参数
          const cleanedParams = Object.fromEntries(
            Object.entries(params).filter(([key, value]) => {
              // 检查value是否既不是空字符串也不是空数组
              return value !== '' && !(Array.isArray(value) && value.length === 0);
            }),
          );
          const { environment, ip, recordId, cloudImageFourVersion, creationTime, responseType } =
            cleanedParams;
          // 解析创建时间范围
          let beginTime: string | undefined = undefined;
          let endTime: string | undefined = undefined;
          if (creationTime && creationTime.length === 2) {
            // 格式化日期为 ISO 字符串或其他后端期望的格式
            // 这里假设后端期望 ISO 格式
            beginTime = creationTime[0] ? new Date(creationTime[0]).toISOString() : undefined;
            if (creationTime[1]) {
              const endDate = new Date(creationTime[1]);
              // 将 endTime 设置为当天的最后一秒
              endDate.setUTCHours(23, 59, 59, 999); // 注意：这里使用 999 毫秒来尽可能接近最后一刻
              endTime = endDate.toISOString();
            } else {
              endTime = undefined;
            }
          }
          setLoading(true); // 开始请求时设置加载状态
          try {
            // 调用后端接口
            const response = await ReportService.queryFilterCount({
              appId: props.appId,
              env: environment,
              recordId: recordId,
              recordVersion: cloudImageFourVersion,
              beginTime: beginTime,
              endTime: endTime,
              ips: ip,
              responseType: responseType,
            });
            setLoading(false); // 请求成功后取消加载状态
            // 更新 dataSource
            setDataSource(response || []);
            // setQueryConditions({ env, ips, recordId, cloudImageFourVersion, beginTime, endTime });
            setQueryConditions({
              appId: props.appId,
              appName: props.appName,
              env: environment,
              responseType: responseType,
              ips: ip,
              recordId: recordId,
              recordVersion: cloudImageFourVersion,
              beginTime: beginTime,
              endTime: endTime,
            });
            return {
              data: response,
              success: true,
            };
          } catch (error) {
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        expandable={{
          expandedRowKeys,
          expandedRowRender: (record) => (
            <RecordedCaseListDetail
              appId={record.appId}
              appName={props.appName}
              operationId={record.id}
              operationName={record.operationName}
              operationTypes={record.operationTypes}
              env={queryConditions.env}
              recordVersion={queryConditions.recordVersion}
              beginTime={queryConditions.beginTime}
              endTime={queryConditions.endTime}
              ips={queryConditions.ips}
              responseType={queryConditions.responseType}
              recordId={queryConditions.recordId}
              onClick={() => setOpen(true)}
              selectedFilters={formValues} // 传递 selectedFilters
            />
          ),
          onExpand: handleExpandChange,
        }}
        search={{
          labelWidth: 'auto',
          defaultColsNumber: 3, // 每行最多3个搜索项
          defaultCollapsed: false, // 默认展开所有搜索项
        }}
        pagination={{
          pageSize: 20, // 设置每页显示的记录数
          showSizeChanger: true, // 是否允许用户改变每页显示的记录数
          pageSizeOptions: ['10', '20', '50', '100'], // 用户可以选择的每页记录数选项
        }}
        options={{
          density: false, // 密度选项
          reload: false, // 刷新按钮
        }}
      />
    </>
  );
});

export default RecordedCase;
