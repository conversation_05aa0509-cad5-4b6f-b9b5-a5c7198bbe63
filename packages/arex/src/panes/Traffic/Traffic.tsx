import { ArexPaneFC } from '@arextest/arex-core';
import React from 'react';

import RecordedCase from './RecordedCase/index';

const Traffic: ArexPaneFC = (props) => {
  const paneKey = props.paneKey;
  const parts = paneKey.split('-_-');
  const extractedPart = parts[1];
  return (
    // eslint-disable-next-line react/jsx-no-comment-textnodes
    <div>
      <RecordedCase appId={extractedPart} />
    </div>
  );
};

export default Traffic;
