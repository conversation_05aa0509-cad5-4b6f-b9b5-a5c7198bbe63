import {
  CloseCircleOutlined,
  CodeOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import {
  css,
  getLocalStorage,
  HelpTooltip,
  i18n,
  I18nextLng,
  Label,
  PanesTitle,
  SpaceBetweenWrapper,
  styled,
  TooltipButton,
  useArexPaneProps,
  useTranslation,
} from '@arextest/arex-core';
import { useLocalStorageState, useRequest } from 'ahooks';
import {
  App,
  AutoComplete,
  Badge,
  Button,
  Collapse,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Popover,
  Select,
  Skeleton,
  Switch, // 添加 Switch 组件
  Tag,
  theme,
  Typography,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, {
  createElement,
  FC,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { InterfaceSelect, TagSelect } from '@/components';
import { EMAIL_KEY, isClient, PanesType, TARGET_HOST_AUTOCOMPLETE_KEY } from '@/constant';
import { useNavPane } from '@/hooks';
import { ApplicationService, FilterConfigurationService, ScheduleService } from '@/services';
import { queryGroupedIpList, queryIpList } from '@/services/ConfigService';
import { CaseTags, CreatePlanReq, MessageMap } from '@/services/ScheduleService';
import { isValidIP, isValidIPWithPort } from '@/utils/reUtils';

import CompareNoise from './CompareNoise';
import RecordedCaseList, { RecordedCaseRef } from './RecordedCase';

type AppTitleProps = {
  appId: string;
  env: string;
  appName?: string;
  readOnly?: boolean;
  recordCount?: number;
  tags?: Record<string, string[]>;
  onRefresh?: () => void;
  onQueryRecordCount?: () => void;
};

// 在 CreatePlanForm 类型中添加新字段
type CreatePlanForm = {
  planName?: string;
  targetProtocol: string;
  targetEnv: string;
  caseSourceRange: [Dayjs, Dayjs];
  operationList?: string[];
  descriptionEnv: string;
  caseCountLimit?: number;
  filterConfigIdList: string[];
  caseTags?: CaseTags;
  recordCaseEnv: string[];
  // 新增的字段
  recordIp?: string[]; // 录制时的IP
  recordId?: string[]; // 录制时的Id
  onlyEntryFlux?: boolean; // 新增字段：是否只回放入口流量
};

const TitleWrapper = styled(
  (props: {
    appId: string;
    planId?: string;
    className?: string;
    title: ReactNode;
    count?: number;
    readOnly?: boolean;
    onClickTitle?: () => void;
    onRefresh?: () => void;
    onSetting?: () => void;
  }) => {
    const { t } = useTranslation(['components']);

    return (
      <div
        id='arex-replay-record-detail-btn'
        className={props.className}
        style={{ paddingLeft: '4px' }}
      >
        {props.title ? (
          <>
            {createElement(
              props.count ? Button : 'div',
              props.count
                ? { type: 'text', onClick: props.onClickTitle }
                : { style: { padding: '6px 12px 0' } },
              <Badge size='small' count={props.count} offset={[8, 2]}>
                <Typography.Title level={4}>{props.title}</Typography.Title>
              </Badge>,
            )}
            {props.onRefresh && (
              <TooltipButton
                id='arex-replay-refresh-report-btn'
                size='small'
                type='text'
                title={t('replay.refresh')}
                icon={<SyncOutlined />}
                onClick={props.onRefresh}
              />
            )}

            <Popover
              trigger={['click']}
              overlayStyle={{ width: '320px' }}
              overlayInnerStyle={{ padding: '8px' }}
              title={
                <div style={{ padding: '8px' }}>
                  <Typography.Text strong style={{ display: 'block' }}>
                    Agent Script :
                  </Typography.Text>
                  <Typography.Text code copyable>
                    {`java -javaagent:</path/to/arex-agent.jar> -Darex.service.name=${props.appId} -Darex.storage.service.host=<storage.service.host:port> -jar <your-application.jar>`}
                  </Typography.Text>
                </div>
              }
            >
              <Button size='small' type='text' icon={<CodeOutlined />} />
            </Popover>

            <CompareNoise appId={props.appId} readOnly={props.readOnly} />

            {props.onSetting && (
              <TooltipButton
                id='arex-replay-app-setting-btn'
                size='small'
                type='text'
                title={t('replay.appSetting')}
                icon={<SettingOutlined />}
                onClick={props.onSetting}
              />
            )}
          </>
        ) : (
          <Skeleton.Input active size='small' style={{ width: '200px' }} />
        )}
      </div>
    );
  },
)`
  display: flex;
  align-items: center;
  & > :first-of-type {
    margin-right: 4px;
  }
`;

const InitialValues = {
  targetEnv: '',
  caseSourceRange: [
    dayjs().subtract(1, 'day').startOf('day'), // 前一天零点
    dayjs().add(1, 'day').startOf('day').subtract(1, 'second'), // 当天最后一秒
  ],
  descriptionEnv: '',
  onlyEntryFlux: false, // 添加默认值
};

type Agent = {
  ip: string;
  agentStatus: string;
  scfServicePort: number;
  httpServicePort: number;
  currentRate: string;
  decelerateCode: number;
  genericsEnable: boolean;
  agentActiveState: number;
  cloudGroupName: string;
  cloudImageFourVersion: string;
};

interface AgentInfo {
  [key: string]: Agent[];
}

const AppTitle: FC<AppTitleProps> = ({
  appId,
  env,
  appName,
  readOnly,
  recordCount = 0,
  tags,
  onRefresh,
  onQueryRecordCount,
}) => {
  const [filterRuleOptions, updateFilterRuleOptions] = useState<any[]>([]);
  const { notification } = App.useApp();
  const { token } = theme.useToken();
  const navPane = useNavPane();
  const { t } = useTranslation(['components']);
  const email = getLocalStorage<string>(EMAIL_KEY);
  const { data } = useArexPaneProps<{ planId: string }>();

  const caseListRef = useRef<RecordedCaseRef>(null);

  const [form] = Form.useForm<CreatePlanForm>();
  const targetEnv = Form.useWatch('targetEnv', form);
  const planName = Form.useWatch('planName', form);

  const recordCaseEnv = Form.useWatch('recordCaseEnv', form); //录制回放环境

  const caseSourceRange = Form.useWatch('caseSourceRange', form);
  const operationList = Form.useWatch('operationList', form);
  const caseCountLimit = Form.useWatch('caseCountLimit', form);

  const [agentsInfo, setAgentsInfo] = useState<AgentInfo>({});
  const [protocol, setProtocol] = useState<string>('');
  const [host, setHost] = useState<any[]>([]);
  const [searchHostText, setSearchHostText] = useState('');

  const [openPathDropdown, setOpenPathDropdown] = useState(false);

  const [targetHostSource, setTargetHostSource] = useLocalStorageState<{
    [appId: string]: string[];
  }>(TARGET_HOST_AUTOCOMPLETE_KEY, {
    defaultValue: {},
  });

  const appTitle = useMemo(
    () => appName && `${readOnly ? `[${t('readOnly', { ns: 'common' })}] ` : ''}${appName}`,
    [readOnly, t, appName],
  );

  /**
   * 请求 InterfacesList
   */
  // useRequest(() => ApplicationService.queryInterfacesList<'Global'>({ appId, env }), {
  //   ready: open,
  //   onSuccess(res) {
  //     setInterfacesOptions(res.map((item) => ({ label: item.operationName, value: item.id })));
  //   },
  // });

  //调用创建回放计划接口
  const webhook = useMemo(() => {
    form.setFieldsValue({}); //提交之前set下表单值，有的默认值没触发的需要set
    const url = new URL(`${location.origin}/schedule/createPlan`);

    url.searchParams.append('appId', appId);
    targetEnv && url.searchParams.append('targetEnv', targetEnv.trim());
    planName && url.searchParams.append('planName', planName.trim());

    //recordCaseEnv && url.searchParams.append('recordCaseEnv', ["SandBox"]);

    if (caseSourceRange && caseSourceRange?.length === 2) {
      url.searchParams.append('caseSourceFrom', caseSourceRange[0].valueOf().toString());
      url.searchParams.append('caseSourceTo', caseSourceRange[1].valueOf().toString());
    }
    operationList?.length && url.searchParams.append('operationIds', operationList.join(','));

    typeof caseCountLimit === 'number' &&
      url.searchParams.append('caseCountLimit', caseCountLimit.toString());
    return url.toString();
  }, [appId, targetEnv, planName, recordCaseEnv, caseSourceRange, operationList, caseCountLimit]);

  useEffect(() => {
    if (appId) {
      FilterConfigurationService.getFilterRuleList({ appId }).then((res: any) => {
        const list: any[] = [];
        (res || []).map((item: { filterSummaryInfos: any[] }) => {
          item.filterSummaryInfos.map((cItem: { filterName: any; id: any }) => {
            list.push({
              label: cItem.filterName,
              value: cItem.id,
            });
            return cItem;
          });
          return item;
        });
        updateFilterRuleOptions(list);
      });
    }
  }, [appId]);

  useEffect(() => {
    const groupedIpList = async () => {
      const res: AgentInfo = await queryGroupedIpList({
        appId: appId,
      });
      setAgentsInfo(res);
    };
    if (appId) {
      groupedIpList();
    }
  }, []);

  useEffect(() => {
    setHost(transformedData);
    form.setFieldValue('targetEnv', '');
  }, [protocol]);

  /**
   * 创建回放
   */
  const { run: createPlan, loading: confirmLoading } = useRequest(
    isClient ? ScheduleService.createPlanLocal : ScheduleService.createPlan,
    {
      manual: true,
      onSuccess(res) {
        if (res.result === 1) {
          notification.success({
            message: t('replay.startSuccess'),
          });
          onRefresh?.();
        } else {
          console.error(res.desc);
          notification.error({
            message: t('replay.startFailed'),
            description: MessageMap[i18n.language as I18nextLng][res.data.reasonCode],
          });
        }
      },
      onError(e) {
        notification.error({
          message: t('replay.startFailed'),
          description: e.message,
        });
      },
      onFinally() {
        setOpenPathDropdown(false);
        // form.resetFields();
      },
    },
  );

  //原来的
  const handleStartReplay1 = () => {
    console.log(form.getFieldsValue());
    form
      .validateFields()
      .then((values) => {
        let targetEnv = values.targetEnv.trim();
        const targetProtocol = values.targetProtocol.trim();
        if (isValidIP(targetEnv) || isValidIPWithPort(targetEnv)) {
          targetEnv = `${targetProtocol}://${targetEnv}`;
        }
        const descriptionEnv = values.descriptionEnv.trim();
        // 获取新表单项的值
        const recordIps = values.recordIp || []; // 确保即使用户没有输入任何值，也是一个空数组
        const recordIds = values.recordId || []; // 确保即使用户没有输入任何值，也是一个空数组

        createPlan({
          appId,
          sourceEnv: 'pro',
          targetEnv,
          descriptionEnv,
          planName: values.planName,
          caseSourceFrom: values.caseSourceRange[0].valueOf(),
          caseSourceTo: values.caseSourceRange[1].valueOf(),
          operationCaseInfoList: values.operationList?.map((operationId) => ({
            operationId,
          })),
          operator: email as string,
          replayPlanType: Number(Boolean(values.operationList?.length)),
          caseCountLimit: values.caseCountLimit,
          filterConfigIdList: values.filterConfigIdList,
          caseTags: values.caseTags,
          //recordCaseEnv:["SandBox"]
          recordCaseEnv: values.recordCaseEnv,
          // 新增的字段
          recordIps, // 录制时的IP
          recordIds, // 录制时的Id
        });
        console.log('values.recordCaseEnv--->', values.recordCaseEnv);

        // update targetHostSource
        setTargetHostSource((source) => {
          !source && (source = {});

          if (source?.[appId] && !source?.[appId].includes(targetEnv))
            source[appId].push(targetEnv);
          else if (!source?.[appId]) source[appId] = [targetEnv];

          return source;
        });
      })
      .catch((info) => {
        console.error('Validate Failed:', info);
      });
  };

  const handleStartReplay = () => {
    console.log(form.getFieldsValue());
    form
      .validateFields()
      .then((values: CreatePlanForm) => {
        let targetEnv = values.targetEnv.trim();
        const targetProtocol = values.targetProtocol.trim();
        if (isValidIP(targetEnv) || isValidIPWithPort(targetEnv)) {
          targetEnv = `${targetProtocol}://${targetEnv}`;
        }
        const descriptionEnv = values.descriptionEnv.trim();

        // 构建传递给 createPlan 的参数对象
        const createPlanParams: CreatePlanReq = {
          appId,
          sourceEnv: 'pro',
          targetEnv,
          descriptionEnv,
          planName: values.planName,
          caseSourceFrom: values.caseSourceRange[0].valueOf(),
          caseSourceTo: values.caseSourceRange[1].valueOf(),
          operationCaseInfoList: values.operationList?.map((operationId) => ({
            operationId,
          })),
          operator: email as string,
          replayPlanType: Number(Boolean(values.operationList?.length)),
          caseCountLimit: values.caseCountLimit,
          filterConfigIdList: values.filterConfigIdList,
          caseTags: values.caseTags,
          recordCaseEnv: values.recordCaseEnv,
          // 只有当 values 中存在 recordIp 和 recordId 时，才包含它们
          ...(values.recordIp && { recordIps: values.recordIp }),
          ...(values.recordId && { recordIds: values.recordId }),
          onlyEntryFlux: values.onlyEntryFlux || false, // 添加新字段
        };

        createPlan(createPlanParams);

        console.log('values.recordCaseEnv--->', values.recordCaseEnv);
        console.log('values.recordIp--->', values.recordIp);
        console.log('values.recordId--->', values.recordId);

        // update targetHostSource
        setTargetHostSource((source) => {
          !source && (source = {});

          if (source?.[appId] && !source?.[appId].includes(targetEnv))
            source[appId].push(targetEnv);
          else if (!source?.[appId]) source[appId] = [targetEnv];

          return source;
        });
      })
      .catch((info) => {
        console.error('Validate Failed:', info);
      });
  };

  const targetHostOptions = useMemo(
    () =>
      targetHostSource?.[appId]?.map((item) => ({
        label: (
          <SpaceBetweenWrapper>
            <Typography.Text>{item}</Typography.Text>
            <Button
              size='small'
              type='text'
              icon={
                <CloseCircleOutlined
                  style={{ fontSize: '10px', color: token.colorTextSecondary }}
                />
              }
              onClick={(e) => {
                e.stopPropagation();

                setTargetHostSource((source) => {
                  const targetHostList = source?.[appId] || [];
                  const index = targetHostList.indexOf(item);
                  if (index > -1) {
                    targetHostList.splice(index, 1);
                  }
                  return {
                    ...source,
                    [appId]: targetHostList,
                  };
                });
              }}
            />
          </SpaceBetweenWrapper>
        ),
        value: item,
      })) || [],
    [appId, targetHostSource, openPathDropdown],
  );

  const handleClickTitle = useCallback(() => caseListRef.current?.open(), [caseListRef]);

  const handleRefresh = useCallback(() => {
    onQueryRecordCount?.();
    onRefresh?.();
  }, []);

  const handleSetting = useCallback(() => {
    navPane({
      id: appId,
      type: PanesType.APP_SETTING,
      name: appName,
    });
  }, [appId]);

  const handleCloseModal = useCallback(() => {
    setOpenPathDropdown(false);
    // form.resetFields();
  }, [form]);

  const transformedData = Object.keys(agentsInfo).map((key) => ({
    label: <span>{key}</span>,
    title: key,
    options: agentsInfo[key].map((agentInfo: any) => {
      let port = protocol === 'scf' ? agentInfo.scfServicePort : agentInfo.httpServicePort;
      port = port === -1 ? '' : `:${port}`;
      return {
        label: (
          <span>
            {agentInfo.ip}
            {port}
          </span>
        ),
        value: `${agentInfo.ip}${port}`,
      };
    }),
  }));

  // 获取ip列表
  function handleChange(value: any) {
    form.setFieldsValue({});
    setHost([]);
    setProtocol(value);
  }

  //本次改动点
  const [recordIps, setRecordIps] = useState<string[]>([]); // 使用数组类型
  const [recordIds, setRecordIds] = useState<string[]>([]); // 使用数组类型

  const handleRecordIpChange = (value: string[]) => {
    setRecordIps(value);
  };

  const handleRecordIdChange = (value: string[]) => {
    setRecordIds(value);
  };

  return (
    <div>
      <PanesTitle
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <TitleWrapper
              appId={appId}
              planId={data?.planId}
              title={appTitle}
              readOnly={readOnly}
              count={recordCount}
              onClickTitle={handleClickTitle}
              onRefresh={handleRefresh}
              onSetting={handleSetting}
            />
          </div>
        }
        extra={
          <Button
            id='arex-replay-create-plan-btn'
            size='small'
            type='primary'
            disabled={readOnly}
            icon={<PlayCircleOutlined />}
            onClick={() => setOpenPathDropdown(true)}
          >
            {t('replay.startButton')}
          </Button>
        }
      />

      <Modal
        title={`${t('replay.startButton')} - ${appId}`}
        open={openPathDropdown}
        onOk={handleStartReplay}
        onCancel={handleCloseModal}
        styles={{
          body: { padding: '8px 0' },
        }}
        confirmLoading={confirmLoading}
      >
        <Form
          name={`startReplay-${appId}`}
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={InitialValues}
          autoComplete='off'
        >
          <Form.Item
            label={
              <HelpTooltip title={t('replay.targetHostTooltip')}>
                {t('replay.targetHost')}
              </HelpTooltip>
            }
            required={true}
          >
            <Form.Item
              // name='targetEnv'
              rules={[{ required: true, message: t('replay.emptyHost') as string }]}
              noStyle
              name='targetProtocol'
            >
              <Select
                // defaultValue='https'
                style={{ width: 95 }}
                onChange={handleChange}
                options={[
                  { value: 'http', label: 'http://' },
                  // { value: 'https', label: 'https://' },
                  { value: 'scf', label: 'scf://' },
                ]}
              />
            </Form.Item>
            <Form.Item
              name='targetEnv'
              rules={[{ required: true, message: t('replay.targetHost') as string }]}
              noStyle
            >
              <AutoComplete options={host} style={{ width: '70%' }} placeholder='<ip>:<port>' />
            </Form.Item>
          </Form.Item>

          <Form.Item
            label={
              <HelpTooltip title={t('replay.descriptionTooltip')}>
                {t('replay.description')}
              </HelpTooltip>
            }
            name='descriptionEnv'
          >
            <AutoComplete allowClear />
          </Form.Item>

          <Form.Item
            label={t('replay.recordCaseEnv')}
            name='recordCaseEnv'
            required={false}
            initialValue={['Product']}
          >
            <Select
              mode='multiple'
              style={{ width: '100%' }}
              options={[
                { value: 'Product', label: 'Product' },
                { value: 'SandBox', label: 'SandBox' },
                { value: 'Stable', label: 'Stable' },
                { value: 'Test', label: 'Test' },
              ]}
            />
          </Form.Item>

          <Form.Item
            label={
              <HelpTooltip title={t('replay.caseRangeTooltip')}>
                {t('replay.caseRange')}
              </HelpTooltip>
            }
            name='caseSourceRange'
            rules={[{ required: true, message: t('replay.emptyCaseRange') as string }]}
            css={css`
              .ant-picker-dropdown .ant-picker-footer-extra {
                position: absolute;
                border: none;
                bottom: 3px;
              }
            `}
          >
            <DatePicker.RangePicker
              format='YYYY-MM-DD HH:mm'
              showTime={{ format: 'HH:mm' }}
              getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
              renderExtraFooter={() => (
                <>
                  <Label type='secondary'>{t('dateRangePreset.quickPick', { ns: 'common' })}</Label>
                  <Button
                    size={'small'}
                    onClick={() => {
                      form.setFieldsValue({
                        caseSourceRange: [dayjs().subtract(1, 'day'), dayjs()],
                      });
                    }}
                  >
                    {t('dateRangePreset.1d', { ns: 'common' })}
                    {/* {t('dateRangePreset.1d', { ns: 'common' })} */}
                  </Button>
                  <Button
                    size={'small'}
                    onClick={() => {
                      form.setFieldsValue({
                        caseSourceRange: [dayjs().startOf('day'), dayjs()],
                      });
                    }}
                  >
                    {t('dateRangePreset.today', { ns: 'common' })}
                    {/* {t('dateRangePreset.1d', { ns: 'common' })} */}
                  </Button>
                </>
              )}
              placeholder={[t('replay.caseStartTime'), t('replay.caseEndTime')]}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Collapse
            css={css`
              margin-top: -8px;
              background-color: transparent;
              .ant-collapse-content-box {
                padding: 0 !important;
              }
            `}
            bordered={false}
            items={[
              {
                key: 'advancedOptions',
                label: (
                  <Typography.Text>
                    {t('advancedOptions', {
                      ns: 'common',
                    })}
                  </Typography.Text>
                ),
                children: (
                  <>
                    <Form.Item label={t('replay.planName')} name='planName'>
                      <Input allowClear placeholder={t('replay.planNamePlaceholder') as string} />
                    </Form.Item>
                    <Form.Item
                      label={
                        <HelpTooltip title={t('replay.pathsTooltip')}>
                          {t('replay.paths')}
                        </HelpTooltip>
                      }
                      name='operationList'
                    >
                      <InterfaceSelect
                        appId={appId}
                        open={openPathDropdown}
                        placeholder={t('replay.pathsPlaceholder')}
                      />
                    </Form.Item>

                    <Form.Item label={t('replay.caseCountLimit')} name='caseCountLimit'>
                      <InputNumber
                        precision={0}
                        min={0}
                        addonAfter={t('replay.caseCountUnit')}
                        placeholder={t('replay.caseCountLimitPlaceholder') as string}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>

                    {/* <Form.Item label={t('replay.caseTags')} name='caseTags'>
                      <TagSelect multiple tags={tags} />
                    </Form.Item> */}

                    <Form.Item label={t('replay.filterRules')} name='filterConfigIdList'>
                      <Select
                        mode='multiple'
                        allowClear
                        style={{ width: '100%' }}
                        placeholder='Please select'
                        options={filterRuleOptions}
                      />
                    </Form.Item>
                    <Form.Item label='录制时的IP' name='recordIp'>
                      <Select
                        mode='tags'
                        placeholder='请输入'
                        value={recordIps}
                        onChange={handleRecordIpChange}
                      ></Select>
                    </Form.Item>
                    <Form.Item label='录制时的Id' name='recordId'>
                      <Select
                        mode='tags'
                        placeholder='请输入recordId'
                        value={recordIds}
                        onChange={handleRecordIdChange}
                      ></Select>
                    </Form.Item>

                    {/* 添加新的表单项 */}
                    <Form.Item
                      label={
                        <HelpTooltip title='只回放入口流量，不Mock及比对内部调用'>
                          Tcp Copy模式
                        </HelpTooltip>
                      }
                      name='onlyEntryFlux'
                      valuePropName='checked'
                    >
                      <Switch checkedChildren='是' unCheckedChildren='否' />
                    </Form.Item>

                    <Form.Item label={'Webhook'}>
                      <Typography.Text copyable ellipsis>
                        {webhook}
                      </Typography.Text>
                    </Form.Item>
                  </>
                ),
              },
            ]}
          />
        </Form>
      </Modal>

      <RecordedCaseList
        ref={caseListRef}
        appId={appId}
        appName={appName}
        onChange={onQueryRecordCount}
      />
    </div>
  );
};

export default AppTitle;
