import { ParameterizedData } from '../BatchRun';

// 定义参数化数据的正则表达式
export const REGEX_PARAMETERIZED = /{{{([^}]+)}}}/g;

// 检查内容中是否包含参数化标记
export function hasParameterizedMarkers(content: string): boolean {
  if (!content) return false;
  // 重置正则表达式的lastIndex
  REGEX_PARAMETERIZED.lastIndex = 0;
  return REGEX_PARAMETERIZED.test(content);
}

// 替换参数化数据
export function replaceParameterizedData(content: string, paramData: ParameterizedData): string {
  if (!content) return content;

  // 重置正则表达式的lastIndex
  REGEX_PARAMETERIZED.lastIndex = 0;

  return content.replace(REGEX_PARAMETERIZED, (match, field) => {
    return paramData[field] !== undefined ? String(paramData[field]) : match;
  });
}

// 提取内容中的所有参数化字段
export function extractParameterizedFields(content: string): string[] {
  if (!content) return [];

  const fields: string[] = [];
  let match;

  // 重置正则表达式的lastIndex
  REGEX_PARAMETERIZED.lastIndex = 0;

  while ((match = REGEX_PARAMETERIZED.exec(content)) !== null) {
    fields.push(match[1]);
  }

  return [...new Set(fields)]; // 去重
}

// 替换字符串中指定位置的内容
export function replaceBetween(string: string, start: number, end: number, what: string) {
  return string.substring(0, start) + what + string.substring(end);
}
