import { useTranslation } from '@arextest/arex-core';
import type { ArexEnvironment, ArexRESTResponse } from '@arextest/arex-request';
import { ArexResponse, getMarkFromToArr, REGEX_ENV_VAR } from '@arextest/arex-request';
import { useRequest } from 'ahooks';
import { Divider } from 'antd';
import React, { FC, Key, useEffect, useMemo, useState } from 'react';

import { CollectionNodeType, PanesType } from '@/constant';
import { useNavPane } from '@/hooks';
import { FileSystemService } from '@/services';
import { useWorkspaces } from '@/store';

import { ParameterizedData } from './BatchRun';
import RequestCard from './components/RequestCard';
import ResponsePanel from './components/ResponsePanel';
import { executeParameterizedRequests } from './services/ParameterizedRequestService';
import {
  extractParameterizedFields,
  hasParameterizedMarkers,
  replaceBetween,
} from './utils/ParameterizedUtils';

export type BatchRunResultItemProps = {
  id: string;
  caseId: Key; // 新增 caseId
  environment?: ArexEnvironment;
  data: Awaited<ReturnType<typeof FileSystemService.queryRequest>>;
  parameterizedData?: ParameterizedData[];
  // onResponse?: (data: ArexRESTResponse) => void; // 旧的单个 response 回调
  onItemComplete: (caseId: Key, response: ArexResponse | ArexResponse[]) => void; // 新的完成回调

  // 参数化相关的回调和初始状态
  onItemProgress: (caseId: Key, current: number, total: number) => void;
  onItemResultUpdate: (caseId: Key, results: ArexResponse[]) => void;
  onHideItemProgress: (caseId: Key) => void;
  initialProgress?: { current: number; total: number };
  initialShowProgress?: boolean;
  initialParameterizedResults?: ArexResponse[];
  parameterizedConcurrency?: number; // 接收并发参数
};

const BatchRunResultItem: FC<BatchRunResultItemProps> = (props) => {
  const {
    caseId,
    onItemComplete,
    onItemProgress,
    onItemResultUpdate,
    onHideItemProgress,
    initialProgress,
    initialShowProgress,
    initialParameterizedResults,
    parameterizedConcurrency = 1, // 默认为1
  } = props;
  const { method, name, endpoint } = props.data;
  const navPane = useNavPane();
  const { activeWorkspaceId } = useWorkspaces();
  const [expanded, setExpanded] = useState(false);
  const { t } = useTranslation('page');

  // 参数化相关状态
  const [parameterizedResults, setParameterizedResults] = useState<ArexResponse[]>(initialParameterizedResults || []);
  const [activeTabKey, setActiveTabKey] = useState<string>('0');
  const [needParameterization, setNeedParameterization] = useState<boolean>(false);
  const [parameterizedFields, setParameterizedFields] = useState<string[]>([]);

  // 进度相关状态
  const [progress, setProgress] = useState<{ current: number; total: number }>({
    current: 0,
    total: 0,
  });
  const [showProgress, setShowProgress] = useState<boolean>(false);

  // 请求ID状态，用于跟踪请求变化
  const [requestId, setRequestId] = useState<string>('');

  // 当请求数据变化时重置状态
  useEffect(() => {
    const currentRequestId = props.data.id || JSON.stringify(props.data);
    if (requestId !== currentRequestId) {
      // 重置所有状态
      setRequestId(currentRequestId);
      setExpanded(false);
      setParameterizedResults([]);
      setActiveTabKey('0');
      setNeedParameterization(false);
      setParameterizedFields([]);
      setProgress({ current: 0, total: 0 });
      setShowProgress(false);
    }
  }, [props.data]);

  // 检查是否需要参数化处理
  useEffect(() => {
    if (!props.parameterizedData || props.parameterizedData.length === 0) {
      setNeedParameterization(false);
      return;
    }

    // 检查请求的各个部分是否包含参数化标记
    const checkParts = [
      endpoint,
      props.data.body?.body,
      typeof props.data.body === 'string' ? props.data.body : '',
      props.data.body?.raw,
      props.data.body?.urlencoded ? JSON.stringify(props.data.body.urlencoded) : '',
      props.data.body?.formdata ? JSON.stringify(props.data.body.formdata) : '',
      props.data.headers ? JSON.stringify(props.data.headers) : '',
      typeof props.data.body === 'object' ? JSON.stringify(props.data.body) : '',
    ];

    const hasMarkers = checkParts.some((part) => hasParameterizedMarkers(part));
    setNeedParameterization(hasMarkers);

    if (hasMarkers) {
      // 提取所有参数化字段
      const fields = checkParts.flatMap((part) => extractParameterizedFields(part));
      setParameterizedFields([...new Set(fields)]);
    }
  }, [props.data, props.parameterizedData, endpoint]);

  const realEndpoint = useMemo(() => {
    const parse = getMarkFromToArr(endpoint, REGEX_ENV_VAR, props.environment);
    let url = endpoint;
    if (parse.length) {
      parse.forEach((item) => {
        if (item.found) {
          url = replaceBetween(url, item.from, item.to, item.matchEnv.value);
        }
      });
    }
    return url;
  }, [endpoint, props.environment]);

  // 修改请求逻辑，处理参数化数据
  const { data, loading } = useRequest<ArexResponse, any>(
    async () => {
      // 在请求开始前清空之前的结果
      setParameterizedResults([]);
      setProgress({ current: 0, total: 0 });
      setShowProgress(false);

      // 如果需要参数化且有参数化数据
      if (needParameterization && props.parameterizedData && props.parameterizedData.length > 0) {
        // 设置进度初始值和显示状态
        setProgress({ current: 0, total: props.parameterizedData.length });
        setShowProgress(true);

        // 执行参数化请求
        const results = await executeParameterizedRequests(
          props.data,
          props.parameterizedData,
          props.environment,
          (current, total) => {
            setProgress({ current, total });
          },
          (results) => {
            setParameterizedResults(results);
          },
          // 可以根据需要调整并发数，例如 10
          // 10
        );

        // 完成后隐藏进度条
        setShowProgress(false);

        // 返回第一个结果作为默认显示
        return results[0] || null;
      }

      // 原有的非参数化请求逻辑，也使用 executeParameterizedRequests
      // 注意：如果是非参数化，parameterizedData 长度为1，并发限制影响不大，但统一了调用方式
      if (props.data.requestType === 'SCF') {
        if (props.data.body?.scf) {
          const scfInfo = JSON.parse(props.data.body.scf);
          // 注意：这里 originalRequest 结构可能需要调整以匹配 executeParameterizedRequests 的期望
          // props.data.body['scfRequest'] = scfInfo; // 这行可能不再直接需要，因为 processSCFRequest 会处理

          const variables_env = props.environment?.variables;
          if (variables_env != null && variables_env.length > 0) {
            const hostObject = variables_env.find((obj) => obj.key === 'SCF_HOST');
            if (hostObject) {
              // scfInfo.ip = hostObject.value; // 这个赋值应该在 processSCFRequest 内部处理
            }
          }
          // 将 scfInfo 合并到顶层，因为 processSCFRequest 期望它在顶层
          // 同时，确保原始的 request.body.scf 仍然存在，供 processSCFRequest 解析
          const scfModifiedRequest = {
            ...props.data,
            // body: { ...props.data.body, scfRequest: scfInfo }, // 确保 scf 字段存在于 body 中
          };

          return await executeParameterizedRequests(
            scfModifiedRequest, // 传递修改后的请求对象
            [{}], // 空参数化数据
            props.environment
          ).then(results => results[0]);
        }
      }

      return await executeParameterizedRequests(
        props.data,
        [{}], // 空参数化数据
        props.environment
      ).then(results => results[0]);
    },
    {
      refreshDeps: [props.data, props.environment, needParameterization, props.parameterizedData, parameterizedConcurrency],
      onSuccess: (res) => {
        // props.onResponse?.(res as unknown as ArexRESTResponse); // 旧回调
        onItemComplete(caseId, res); // 调用新的完成回调
      },
      onError: (err) => {
        console.error('请求执行错误:', err);
        const errorResponse = { /* 构造错误响应 */ } as ArexResponse;
        onItemComplete(caseId, errorResponse);
      },
      onBefore: () => {
        // 在请求开始前隐藏响应面板，重置卡片宽度
        setExpanded(false);
      },
    },
  );

  const handleDebugCase = (e: React.MouseEvent) => {
    e.stopPropagation();
    navPane({
      type: PanesType.REQUEST,
      id: `${activeWorkspaceId}-${CollectionNodeType.case}-${props.data.id}`,
      name: `Debug-${props.data.name}`,
      icon: props.data.method,
    });
  };

  const handleCardClick = () => {
    setExpanded(!expanded);
  };

  return (
    <div
      id={props.id}
      style={{
        padding: '0 16px',
      }}
    >
      <Divider style={{ margin: '8px 0' }} />

      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <RequestCard
          data={props.data}
          loading={loading}
          expanded={expanded}
          needParameterization={needParameterization}
          parameterizedFields={parameterizedFields}
          parameterizedResults={parameterizedResults}
          parameterizedData={props.parameterizedData}
          activeTabKey={activeTabKey}
          setActiveTabKey={setActiveTabKey}
          showProgress={showProgress}
          progress={progress}
          responseData={data || null} // 确保传递 null 而不是 undefined
          realEndpoint={realEndpoint}
          handleCardClick={handleCardClick}
          handleDebugCase={handleDebugCase}
        />

        <ResponsePanel
          data={data || null} // 这里也应该保持一致
          parameterizedResults={parameterizedResults}
          activeTabKey={activeTabKey}
          expanded={expanded}
          setExpanded={setExpanded}
          needParameterization={needParameterization}
        />
      </div>
    </div>
  );
};

export default BatchRunResultItem;
