import { BugOutlined, DownOutlined, RightOutlined } from '@ant-design/icons';
import {
  css,
  RequestMethodIcon,
  SpaceBetweenWrapper,
  TooltipButton,
  useTranslation,
} from '@arextest/arex-core';
import { ArexResponse, ResponseMeta, TestResult } from '@arextest/arex-request';
import { Card, Space, Spin, Tag, Tooltip, Typography } from 'antd';
import React from 'react';

import { CollectionNodeType } from '@/constant';

import { ParameterizedData } from '../BatchRun';
import ParameterizedTabs from './ParameterizedTabs';

interface RequestCardProps {
  data: any;
  loading: boolean;
  expanded: boolean;
  needParameterization: boolean;
  parameterizedFields: string[];
  parameterizedResults: ArexResponse[];
  parameterizedData?: ParameterizedData[];
  activeTabKey: string;
  setActiveTabKey: (key: string) => void;
  showProgress: boolean;
  progress: { current: number; total: number };
  responseData: ArexResponse | null;
  realEndpoint: string;
  handleCardClick: () => void;
  handleDebugCase: (e: React.MouseEvent) => void;
}

const RequestCard: React.FC<RequestCardProps> = ({
  data,
  loading,
  expanded,
  needParameterization,
  parameterizedFields,
  parameterizedResults,
  parameterizedData,
  activeTabKey,
  setActiveTabKey,
  showProgress,
  progress,
  responseData,
  realEndpoint,
  handleCardClick,
  handleDebugCase,
}) => {
  const { t } = useTranslation('page');
  const { method, name, nodeType } = data;

  return (
    <Card
      size={nodeType === CollectionNodeType.case ? 'small' : undefined}
      onClick={handleCardClick}
      style={{
        cursor: 'pointer',
        flex: expanded ? '0 0 35%' : '1',
        marginRight: expanded ? '16px' : '0',
        maxWidth: expanded ? '500px' : 'none',
      }}
    >
      <SpaceBetweenWrapper>
        <Space>
          {expanded ? <DownOutlined /> : <RightOutlined />}
          {nodeType === CollectionNodeType.case && <RequestMethodIcon.case />}

          {/* 使用JSX语法替代React.createElement，并将style应用到外层div */}
          <div style={{ display: 'flex', width: 'max-content' }}>
            {React.createElement(RequestMethodIcon[method])}
          </div>
          <Typography.Text style={{ marginRight: '8px' }}>{name}</Typography.Text>
          <Typography.Text type='secondary'>{realEndpoint}</Typography.Text>

          {/* 显示参数化标记 */}
          {needParameterization && (
            <Tag color='blue'>
              {t('batchRunPage.parameterized', { count: parameterizedData?.length || 0 })}
            </Tag>
          )}

          {/* 显示参数化字段 */}
          {parameterizedFields.length > 0 && (
            <Tooltip
              title={
                <div>
                  <div>{t('batchRunPage.parameterizedFields')}:</div>
                  <ul style={{ margin: '4px 0 0 16px', padding: 0 }}>
                    {parameterizedFields.map((field) => (
                      <li key={field}>{field}</li>
                    ))}
                  </ul>
                </div>
              }
            >
              <Tag color='purple'>
                {t('batchRunPage.fields')}: {parameterizedFields.length}
              </Tag>
            </Tooltip>
          )}
        </Space>
        <TooltipButton
          title='Debug'
          size='small'
          type='text'
          color='primary'
          icon={<BugOutlined />}
          onClick={handleDebugCase}
        />
      </SpaceBetweenWrapper>

      <ResponseMeta response={responseData?.response} />

      <div style={{ minHeight: '32px' }}>
        <Spin
          spinning={loading}
          tip={showProgress ? `处理中 ${progress.current}/${progress.total}` : undefined}
        >
          {!loading && (
            <div>
              {/* 参数化结果标签页 */}
              <ParameterizedTabs
                parameterizedResults={parameterizedResults}
                activeTabKey={activeTabKey}
                setActiveTabKey={setActiveTabKey}
                parameterizedFields={parameterizedFields}
                parameterizedData={parameterizedData}
                onClick={(e) => {
                  // 只阻止标签区域的点击事件冒泡
                  if (e.target && (e.target as HTMLElement).closest('.ant-tabs-tab')) {
                    e.stopPropagation();
                  }
                }}
              />

              {/* 非参数化结果或无参数化数据时显示原始结果 */}
              {(!needParameterization || parameterizedResults.length === 0) && responseData && (
                <>
                  {responseData.testResult?.length ? (
                    <TestResult testResult={responseData.testResult} />
                  ) : (
                    <Typography.Text
                      css={css`
                        display: block;
                        margin: 16px;
                      `}
                      type='secondary'
                    >
                      {t('batchRunPage.noTestScript')}
                    </Typography.Text>
                  )}
                </>
              )}
            </div>
          )}
        </Spin>
      </div>
    </Card>
  );
};

export default RequestCard;
