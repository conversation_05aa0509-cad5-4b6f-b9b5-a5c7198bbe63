import { CloseOutlined } from '@ant-design/icons';
import { css } from '@arextest/arex-core';
import { ArexResponse, ArexRESTResponse } from '@arextest/arex-request';
import { theme } from 'antd';
import React from 'react';

import ResponseOptions from '../ResponseOptions';

interface ResponsePanelProps {
  data: ArexResponse | null;
  parameterizedResults: ArexResponse[];
  activeTabKey: string;
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
  needParameterization: boolean;
}

const ResponsePanel: React.FC<ResponsePanelProps> = ({
  data,
  parameterizedResults,
  activeTabKey,
  expanded,
  setExpanded,
  needParameterization,
}) => {
  const { token } = theme.useToken();

  // 如果未展开或数据为空，不渲染组件
  if (!expanded) {
    return null;
  }

  // 检查是否有可用的响应数据
  const hasParameterizedResponse = needParameterization &&
    parameterizedResults.length > 0 &&
    parameterizedResults[parseInt(activeTabKey)] &&
    parameterizedResults[parseInt(activeTabKey)].response;

  const hasDirectResponse = data && data.response;

  // 如果没有任何可用的响应数据，显示一个空面板
  if (!hasParameterizedResponse && !hasDirectResponse) {
    return (
      <div
        css={css`
          flex: 1;
          border: 1px solid ${token.colorBorderSecondary};
          border-radius: ${token.borderRadius}px;
          padding: 16px;
          min-height: 400px;
          min-width: 500px;
          overflow: auto;
          position: relative;
          margin-left: 8px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          background-color: ${token.colorBgContainer};
          display: flex;
          align-items: center;
          justify-content: center;
        `}
        onClick={(e) => e.stopPropagation()}
      >
        <CloseOutlined
          onClick={(e) => {
            e.stopPropagation();
            setExpanded(false);
          }}
          style={{
            position: 'absolute',
            top: '12px',
            right: '12px',
            fontSize: '16px',
            cursor: 'pointer',
            zIndex: 10,
            color: token.colorTextSecondary,
            background: token.colorBgElevated,
            borderRadius: '50%',
            padding: '4px',
            width: '20px',
            height: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        />
        <div style={{ color: token.colorTextSecondary }}>正在加载响应数据...</div>
      </div>
    );
  }

  return (
    <div
      css={css`
        flex: 1;
        border: 1px solid ${token.colorBorderSecondary};
        border-radius: ${token.borderRadius}px;
        padding: 16px;
        min-height: 400px;
        min-width: 500px;
        overflow: auto;
        position: relative;
        margin-left: 8px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        background-color: ${token.colorBgContainer};
      `}
      onClick={(e) => e.stopPropagation()}
    >
      <CloseOutlined
        onClick={(e) => {
          e.stopPropagation();
          setExpanded(false);
        }}
        style={{
          position: 'absolute',
          top: '12px',
          right: '12px',
          fontSize: '16px',
          cursor: 'pointer',
          zIndex: 10,
          color: token.colorTextSecondary,
          background: token.colorBgElevated,
          borderRadius: '50%',
          padding: '4px',
          width: '20px',
          height: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />

      {/* 显示参数化结果或原始结果 */}
      {hasParameterizedResponse ? (
        <ResponseOptions
          response={parameterizedResults[parseInt(activeTabKey)].response}
          testResult={parameterizedResults[parseInt(activeTabKey)].testResult}
          consoles={parameterizedResults[parseInt(activeTabKey)].consoles}
        />
      ) : hasDirectResponse ? (
        <ResponseOptions
          response={data.response}
          testResult={data.testResult}
          consoles={data.consoles}
        />
      ) : null}
    </div>
  );
};

export default ResponsePanel;
