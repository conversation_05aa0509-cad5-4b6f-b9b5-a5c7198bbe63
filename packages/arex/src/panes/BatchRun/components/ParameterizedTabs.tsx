import { useTranslation } from '@arextest/arex-core';
import { ArexResponse, ResponseMeta, TestResult } from '@arextest/arex-request';
import { Badge, Space, Tabs, Tag, Typography } from 'antd';
import React from 'react';

import { ParameterizedData } from '../BatchRun';

const { Text } = Typography;

interface ParameterizedTabsProps {
  parameterizedResults: ArexResponse[];
  activeTabKey: string;
  setActiveTabKey: (key: string) => void;
  parameterizedFields: string[];
  parameterizedData?: ParameterizedData[];
  onClick?: (e: React.MouseEvent) => void;
}

const ParameterizedTabs: React.FC<ParameterizedTabsProps> = ({
  parameterizedResults,
  activeTabKey,
  setActiveTabKey,
  parameterizedFields,
  parameterizedData,
  onClick,
}) => {
  const { t } = useTranslation('page');

  if (parameterizedResults.length === 0) {
    return null;
  }

  // 添加自定义样式，强制隐藏下拉菜单区域
  const customStyle = `
    .ant-tabs-nav-operations {
      display: none !important;
      visibility: hidden !important;
      width: 0 !important;
      overflow: hidden !important;
    }
  `;

  return (
    <>
      <style>{customStyle}</style>
      <div onClick={onClick}>
        <Tabs
          activeKey={activeTabKey}
          onChange={setActiveTabKey}
          type='card'
          size='small'
          style={{ marginTop: '8px' }}
          tabPosition='top'
          tabBarGutter={4}
          tabBarStyle={{
            overflowX: 'auto',
            marginBottom: '8px',
          }}
          moreIcon={null}
          items={parameterizedResults.map((result, index) => {
            const isSuccess = result.response?.statusCode < 300;
            const testAllSuccess = result.testResult?.every((test) => test.passed) ?? true;

            // 获取当前迭代的参数值，用于显示在标签上
            const currentParamData = parameterizedData?.[index];
            // 获取第一个参数化字段的值作为标签标识
            const paramValue =
              parameterizedFields.length > 0 && currentParamData
                ? currentParamData[parameterizedFields[0]]
                : null;

            return {
              key: String(index),
              label: (
                <Badge
                  status={isSuccess ? 'success' : 'error'}
                  text={
                    <Space>
                      {/* 显示迭代序号和参数值 */}
                      {paramValue
                        ? `${index + 1}: ${paramValue}`
                        : t('batchRunPage.iteration', { index: index + 1 })}
                      {result.testResult?.length > 0 && (
                        <Tag color={testAllSuccess ? 'success' : 'error'}>
                          {testAllSuccess
                            ? t('batchRunPage.testPassed')
                            : t('batchRunPage.testFailed')}
                        </Tag>
                      )}
                    </Space>
                  }
                />
              ),
              children: (
                <div style={{ padding: '8px 0' }}>
                  <ResponseMeta response={result.response} />
                  <div style={{ minHeight: '32px', marginTop: '8px' }}>
                    {result.testResult?.length ? (
                      <TestResult testResult={result.testResult} />
                    ) : (
                      <Text type='secondary'>{t('batchRunPage.noTestScript')}</Text>
                    )}
                  </div>
                </div>
              ),
            };
          })}
        />
      </div>
    </>
  );
};

export default ParameterizedTabs;
