import { QuestionCircleOutlined, SendOutlined, UploadOutlined } from '@ant-design/icons';
import {
  ArexPaneFC,
  css,
  EmptyWrapper,
  getLocalStorage,
  Label,
  SpaceBetweenWrapper,
  useTranslation,
} from '@arextest/arex-core';
import { ArexEnvironment, ArexResponse, EnvironmentSelect } from '@arextest/arex-request';
import { useRequest } from 'ahooks';
import {
  Button,
  Divider,
  message,
  Popover,
  Spin,
  theme,
  Tooltip,
  TreeSelect,
  Typography,
  Upload,
} from 'antd';
import React, { FC, Key, useCallback, useEffect, useMemo, useState } from 'react';
import { useImmer } from 'use-immer';
import { useImmerReducer } from 'use-immer';

import { CollectionNodeType, WORKSPACE_ENVIRONMENT_PAIR_KEY } from '@/constant';
import BatchRunResultItem from '@/panes/BatchRun/BatchRunResultItem';
import { WorkspaceEnvironmentPair } from '@/panes/Request/EnvironmentDrawer';
import { EnvironmentService, FileSystemService } from '@/services';
import { useCollections } from '@/store';
import { decodePaneKey } from '@/store/useMenusPanes';

const StatusBlock: FC<{
  color: string;
  text?: React.ReactNode;
  children?: React.ReactNode;
}> = (props) => {
  return (
    <span style={{ marginRight: '4px' }}>
      <div
        style={{
          display: 'inline-block',
          height: '6px',
          width: '16px',
          margin: '2px 4px',
          backgroundColor: props.color,
        }}
      >
        {props.children}
      </div>
      <Typography.Text type='secondary'>{props.text}</Typography.Text>
    </span>
  );
};

// 添加参数化数据类型定义
export interface ParameterizedData {
  [key: string]: any;
}

const MAX_CONCURRENT_ITEMS = 10; // 全局最多同时处理10个Case Item

interface BatchRunState {
  allCaseMetas: { id: Key; nodeType: CollectionNodeType }[]; // 存储选中的case的元数据
  processingItems: Map<Key, Awaited<ReturnType<typeof FileSystemService.queryRequest>>>; // 正在处理或已获取数据的items
  itemResponses: Map<Key, ArexResponse | ArexResponse[]>; // 存储每个item的最终结果
  activeRequests: number;
  currentIndex: number; // 当前处理到 allCaseMetas 的哪个索引
  progress: Map<Key, { current: number; total: number }>; // 每个item的参数化进度
  showProgress: Map<Key, boolean>; // 是否显示每个item的参数化进度条
  parameterizedResults: Map<Key, ArexResponse[]>; // 每个item的参数化结果暂存
}

const initialState: BatchRunState = {
  allCaseMetas: [],
  processingItems: new Map(),
  itemResponses: new Map(),
  activeRequests: 0,
  currentIndex: 0,
  progress: new Map(),
  showProgress: new Map(),
  parameterizedResults: new Map(),
};

// 使用 reducer 来管理复杂状态会更清晰
function reducer(draft: BatchRunState, action: any) {
  switch (action.type) {
    case 'START_BATCH':
      draft.allCaseMetas = action.payload.caseMetas;
      draft.processingItems.clear();
      draft.itemResponses.clear();
      draft.activeRequests = 0;
      draft.currentIndex = 0;
      draft.progress.clear();
      draft.showProgress.clear();
      draft.parameterizedResults.clear();
      return;
    case 'ADD_PROCESSING_ITEM':
      draft.processingItems.set(action.payload.id, action.payload.data);
      return;
    case 'ITEM_REQUEST_START':
      draft.activeRequests++;
      return;
    case 'ITEM_REQUEST_COMPLETE':
      draft.activeRequests--;
      draft.itemResponses.set(action.payload.id, action.payload.response);
      return;
    case 'SET_ITEM_PROGRESS':
      draft.progress.set(action.payload.id, {
        current: action.payload.current,
        total: action.payload.total,
      });
      draft.showProgress.set(action.payload.id, true);
      return;
    case 'SET_ITEM_PARAMETERIZED_RESULTS':
      draft.parameterizedResults.set(action.payload.id, action.payload.results);
      return;
    case 'HIDE_ITEM_PROGRESS':
      draft.showProgress.set(action.payload.id, false);
      return;
    case 'INCREMENT_CURRENT_INDEX':
      draft.currentIndex++;
      return;
    // ... 其他 action
  }
}

const BatchRun: ArexPaneFC = (props) => {
  const { paneKey } = props;
  const { token } = theme.useToken();
  const { t } = useTranslation('page');
  const [workspaceId, id] = useMemo(() => decodePaneKey(paneKey).id.split('-'), [paneKey]);

  const { collectionsTreeData, collectionsFlatData } = useCollections();

  const [activeEnvironment, setActiveEnvironment] = useState<ArexEnvironment>();
  const [checkValue, setCheckValue] = useState<Key[]>([]);

  const [responseList, setResponseList] = useImmer<ArexResponse[]>([]);
  const [parameterizedData, setParameterizedData] = useState<ParameterizedData[]>([]);
  const [parameterizedFileName, setParameterizedFileName] = useState<string>('');

  const treeData = useMemo(
    () => (id ? collectionsFlatData.get(id)?.children || [] : collectionsTreeData), // collection right click folder to batch run
    [collectionsFlatData, collectionsTreeData, id],
  );
  const [state, dispatch] = useImmerReducer(reducer, initialState);

  const { runAsync: queryCaseDetail } = useRequest(FileSystemService.queryRequest, {
    manual: true,
  });

  const processQueue = useCallback(async () => {
    if (
      state.activeRequests < MAX_CONCURRENT_ITEMS &&
      state.currentIndex < state.allCaseMetas.length
    ) {
      const caseMeta = state.allCaseMetas[state.currentIndex];
      dispatch({ type: 'INCREMENT_CURRENT_INDEX' });
      dispatch({ type: 'ITEM_REQUEST_START' });

      try {
        const caseDetail = await queryCaseDetail({
          id: String(caseMeta.id),
          nodeType: caseMeta.nodeType,
        });
        dispatch({ type: 'ADD_PROCESSING_ITEM', payload: { id: caseMeta.id, data: caseDetail } });
        // 移除这里的递归调用
        // processQueue();
      } catch (e) {
        console.error('Failed to query case detail:', e);
        dispatch({
          type: 'ITEM_REQUEST_COMPLETE',
          payload: {
            id: caseMeta.id,
            response: {
              /* 构造一个错误响应 */
            },
          },
        });
        // 移除这里的递归调用
        // processQueue();
      }
    }
  }, [state.activeRequests, state.currentIndex, state.allCaseMetas, queryCaseDetail, dispatch]);

  useEffect(() => {
    // 修改：只在批量开始后执行一次，而不是依赖于processingItems的变化
    // 这样可以避免循环依赖问题
    if (state.allCaseMetas.length > 0 && state.currentIndex === 0) {
      processQueue();
    }
  }, [state.allCaseMetas.length, state.activeRequests, state.currentIndex, processQueue]);

  //批量执行前，请求获取环境变量
  const { data: environments } = useRequest(EnvironmentService.getEnvironments, {
    defaultParams: [{ workspaceId }],
    onSuccess(res) {
      const workspaceEnvironmentPair = getLocalStorage<WorkspaceEnvironmentPair>(
        WORKSPACE_ENVIRONMENT_PAIR_KEY,
      );
      const initialEnvId = props.data?.environmentId || workspaceEnvironmentPair?.[workspaceId];
      if (initialEnvId) {
        const env = res.find((env) => env.id === initialEnvId);
        setActiveEnvironment(env);
      }
    },
  });

  //批量执行前，获取接口数据/webApi/filesystem/queryInterface
  const {
    data: cases = [],
    loading,
    runAsync: queryCases,
  } = useRequest(() =>
    Promise.all(
      checkValue.map((key) => {
        const nodeType = collectionsFlatData.get(key.toString())!.nodeType;
        return FileSystemService.queryRequest({ id: String(key), nodeType });
      }),
    ),
  );

  // 添加处理JSON文件上传的函数
  const handleUploadParameterizedData = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const data = JSON.parse(content);

        if (!Array.isArray(data)) {
          message.error(t('batchRunPage.parameterizedDataShouldBeArray'));
          return;
        }

        setParameterizedData(data);
        setParameterizedFileName(file.name);
        message.success(t('batchRunPage.parameterizedDataUploaded', { count: data.length }));
      } catch (error) {
        message.error(t('batchRunPage.invalidJsonFormat'));
      }
    };
    reader.readAsText(file);
    return false; // 阻止自动上传
  };

  // 修改批量执行函数，考虑参数化数据
  const handleBatchRun = () => {
    if (checkValue.length === 0) {
      message.warning(t('batchRunPage.pleaseSelectCase'));
      return;
    }
    const caseMetas = checkValue.map((key) => ({
      id: key,
      nodeType: collectionsFlatData.get(key.toString())!.nodeType,
    }));
    dispatch({ type: 'START_BATCH', payload: { caseMetas } });
    // processQueue 将在 useEffect 中被调用
  };

  // onItemComplete 由 BatchRunResultItem 调用
  const onItemComplete = useCallback(
    (caseId: Key, response: ArexResponse | ArexResponse[]) => {
      dispatch({ type: 'ITEM_REQUEST_COMPLETE', payload: { id: caseId, response } });
      processQueue();
    },
    [dispatch, processQueue],
  );

  // 传递给 BatchRunResultItem 的参数化相关回调
  const onItemProgress = useCallback(
    (caseId: Key, current: number, total: number) => {
      dispatch({ type: 'SET_ITEM_PROGRESS', payload: { id: caseId, current, total } });
    },
    [dispatch],
  );

  const onItemResultUpdate = useCallback(
    (caseId: Key, results: ArexResponse[]) => {
      dispatch({ type: 'SET_ITEM_PARAMETERIZED_RESULTS', payload: { id: caseId, results } });
    },
    [dispatch],
  );

  const onHideItemProgress = useCallback(
    (caseId: Key) => {
      dispatch({ type: 'HIDE_ITEM_PROGRESS', payload: { id: caseId } });
    },
    [dispatch],
  );

  // 渲染 BatchRunResultItem
  const renderedItems = useMemo(() => {
    return state.allCaseMetas.map((meta, index) => {
      console.log('state.processingItems', state.processingItems);
      const caseDetail = state.processingItems.get(meta.id);
      console.log('caseDetail', caseDetail);
      if (!caseDetail) {
        // 可以渲染一个占位符或者loading状态，直到caseDetail被获取
        return (
          <div
            key={meta.id}
            id={`batch-run-result-item-${index}`}
            style={{
              padding: '16px',
              border: `1px solid ${token.colorBorder}`,
              borderRadius: token.borderRadiusLG,
              marginBottom: '8px',
              backgroundColor: token.colorBgContainer,
            }}
          >
            <Divider style={{ margin: '0 0 8px 0' }} />
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '50px',
              }}
            >
              <Spin />
              <Typography.Text style={{ marginLeft: '8px' }} type='secondary'>
                Loading case {String(meta.id)}...
              </Typography.Text>
            </div>
          </div>
        );
      }
      return (
        <BatchRunResultItem
          id={`batch-run-result-item-${index}`}
          key={meta.id}
          caseId={meta.id} // 传递 caseId
          environment={activeEnvironment}
          data={caseDetail}
          parameterizedData={parameterizedData} // 全局参数化数据
          // 传递新的回调函数
          onItemComplete={onItemComplete}
          onItemProgress={onItemProgress} // 用于参数化内部进度
          onItemResultUpdate={onItemResultUpdate} // 用于参数化内部结果更新
          onHideItemProgress={onHideItemProgress} // 用于参数化完成隐藏进度条
          // 从 state 中获取对应 item 的参数化状态
          initialProgress={state.progress.get(meta.id)}
          initialShowProgress={state.showProgress.get(meta.id)}
          initialParameterizedResults={state.parameterizedResults.get(meta.id)}
          // 控制 executeParameterizedRequests 的并发数，因为宏观并发已控制
          parameterizedConcurrency={1}
        />
      );
    });
  }, [state.allCaseMetas, state.processingItems, activeEnvironment, parameterizedData, onItemComplete, onItemProgress, onItemResultUpdate, onHideItemProgress, state.progress, state.showProgress, state.parameterizedResults]);

  // 修改获取结果的函数，传递参数化数据
  const getCasesResults = useCallback(
    (cases: Awaited<ReturnType<typeof FileSystemService.queryRequest>>[]) => {
      // 处理参数化数据
      const processedCases = cases;
      // console.log('processedCases', processedCases);

      return processedCases.map((caseItem, index) => (
        <BatchRunResultItem
          id={`batch-run-result-item-${index}`}
          key={caseItem.id}
          environment={activeEnvironment}
          data={caseItem}
          parameterizedData={parameterizedData}
          onResponse={(response) => {
            setResponseList((res) => {
              res[index] = response;
            });
          }}
        />
      ));
    },
    [activeEnvironment, parameterizedData],
  );

  return (
    <div>
      <SpaceBetweenWrapper>
        <Typography.Text type='secondary' style={{ marginLeft: '16px' }}>
          {t('batchRunPage.selectCaseTip')}
        </Typography.Text>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Upload
            beforeUpload={handleUploadParameterizedData}
            showUploadList={false}
            accept='.json'
          >
            <Tooltip
              title={
                parameterizedFileName
                  ? t('batchRunPage.currentFile', { name: parameterizedFileName })
                  : t('batchRunPage.uploadParameterizedData')
              }
            >
              <Button
                icon={<UploadOutlined />}
                style={{ marginRight: '8px' }}
                type={parameterizedData.length > 0 ? 'primary' : 'default'}
              >
                {parameterizedData.length > 0
                  ? t('batchRunPage.parameterizedDataLoaded', { count: parameterizedData.length })
                  : t('batchRunPage.uploadParameterizedData')}
              </Button>
            </Tooltip>
          </Upload>
          <EnvironmentSelect
            value={activeEnvironment?.id}
            options={environments}
            onChange={setActiveEnvironment}
          />
        </div>
      </SpaceBetweenWrapper>
      <Divider style={{ margin: 0 }} />
      <div style={{ display: 'flex', padding: '8px 16px' }}>
        <TreeSelect
          multiple
          allowClear
          treeCheckable
          maxTagCount={3} // 限制显示标签数量
          treeNodeFilterProp='nodeName' // 优化搜索性能
          dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} // 控制下拉框高度
          size='small'
          placeholder={'Please select case'}
          fieldNames={{ label: 'nodeName', value: 'infoId', children: 'children' }}
          value={checkValue}
          treeData={treeData}
          showCheckedStrategy={TreeSelect.SHOW_CHILD}
          onChange={(value) => {
            const filtered = value.filter((v) => {
              const nodeType = collectionsFlatData.get(v.toString())?.nodeType;
              return (
                !!nodeType &&
                [CollectionNodeType.interface, CollectionNodeType.case].includes(nodeType)
              );
            });
            setCheckValue(filtered);
          }}
          style={{ flex: 1 }}
        />

        <Button
          type='primary'
          icon={<SendOutlined />}
          onClick={handleBatchRun}
          style={{ marginLeft: '16px' }}
        >
          {t('batchRunPage.batchSend')}
        </Button>
      </div>

      {/* 修改条件判断，使用state.allCaseMetas和state.itemResponses */}
      {!!state.allCaseMetas.length && (
        <div style={{ padding: '0 16px 4px', marginBottom: '4px' }}>
          <div style={{ display: 'flex' }}>
            {/* 使用state.allCaseMetas和state.itemResponses生成状态指示器 */}
            {Array.from(state.itemResponses.entries()).map(([id, response], index) => {
              // 处理单个响应或响应数组的情况
              const actualResponse = Array.isArray(response) ? response[0] : response;

              const testAllSuccess = actualResponse.testResult?.every((test) => test.passed) ?? true;
              const testAllFail = actualResponse.testResult?.every((test) => !test.passed) ?? false;

              console.log('actualResponse', actualResponse);

              const requestStatusColor =
                actualResponse.response.type === 'loading'
                  ? token.colorFillSecondary
                  : 'statusCode' in actualResponse.response && actualResponse.response.statusCode < 300
                  ? token.colorSuccess
                  : 'statusCode' in actualResponse.response && actualResponse.response.statusCode < 400
                  ? token.colorWarning
                  : token.colorError;

              const testResultStatusColor = actualResponse.testResult?.length
                ? testAllSuccess
                  ? token.colorSuccess
                  : testAllFail
                  ? token.colorError
                  : token.colorWarning
                : token.colorFillSecondary;

              return (
                <div
                  key={index}
                  onClick={() => {
                    // 查找对应的元素ID
                    const itemIndex = state.allCaseMetas.findIndex(meta => meta.id === id);
                    const element = document.getElementById(`batch-run-result-item-${itemIndex}`);
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  style={{
                    width: '16px',
                    cursor: 'pointer',
                    margin: `0 ${
                      state.itemResponses.size > 100 ? 2 : state.itemResponses.size > 50 ? 3 : 4
                    }px `,
                  }}
                >
                  <div
                    style={{
                      height: '6px',
                      backgroundColor: requestStatusColor,
                    }}
                  />
                  <div
                    style={{
                      height: '6px',
                      backgroundColor: testResultStatusColor,
                    }}
                  />
                </div>
              );
            })}

            <Popover
              title={
                <>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Label>{t('batchRunPage.statusBlockStructure')}</Label>
                    <div style={{ display: 'inline-block' }}>
                      <div
                        style={{
                          textAlign: 'center',
                          backgroundColor: token.colorSuccess,
                          fontSize: '10px',
                          padding: '0 4px',
                          color: token.colorWhite,
                        }}
                      >
                        {t('batchRunPage.requestStatus')}
                      </div>
                      <div
                        style={{
                          textAlign: 'center',
                          backgroundColor: token.colorError,
                          fontSize: '10px',
                          padding: '0 4px',
                          color: token.colorWhite,
                        }}
                      >
                        {t('batchRunPage.testStatus')}
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label>{t('batchRunPage.requestStatus')}</Label>
                    <StatusBlock
                      color={token.colorFillSecondary}
                      text={t('batchRunPage.loading')}
                    />
                    <StatusBlock
                      color={token.colorSuccess}
                      text={t('batchRunPage.requestSuccess')}
                    />
                    <StatusBlock color={token.colorError} text={t('batchRunPage.requestFailed')} />
                  </div>
                  <div>
                    <Label>{t('batchRunPage.testStatus')}</Label>
                    <StatusBlock
                      color={token.colorFillSecondary}
                      text={t('batchRunPage.noTestScript')}
                    />
                    <StatusBlock color={token.colorSuccess} text={t('batchRunPage.allPassed')} />
                    <br />
                    <StatusBlock color={token.colorWarning} text={t('batchRunPage.SomeFailed')} />
                    <StatusBlock color={token.colorError} text={t('batchRunPage.allFailed')} />
                  </div>
                </>
              }
              placement='bottomRight'
              overlayStyle={{ maxWidth: '500px' }}
            >
              <QuestionCircleOutlined style={{ margin: '0 4px' }} />
            </Popover>
          </div>
        </div>
      )}
      <EmptyWrapper
        loading={state.allCaseMetas.length > 0 && state.itemResponses.size < state.allCaseMetas.length}
        empty={state.allCaseMetas.length === 0}
        css={css`
          height: calc(100vh - 226px);
          overflow: auto;
        `}
      >
        {renderedItems}
      </EmptyWrapper>
    </div>
  );
};

export default BatchRun;
