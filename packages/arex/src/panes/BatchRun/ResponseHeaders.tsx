import { ArexRESTHeader } from '@arextest/arex-request';
import { Table } from 'antd';
import React, { FC } from 'react';

const ResponseHeaders: FC<{ headers: ArexRESTHeader[] }> = (props) => {
  const columns = [
    {
      title: '名称',
      dataIndex: 'key',
      key: 'headerKey',
      width: '50%', // 设置名称列宽度为30%
      ellipsis: true, // 文本过长时显示省略号
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'headerValue',
      width: '50%', // 设置值列宽度为70%
      ellipsis: true, // 文本过长时显示省略号
    },
  ];

  return (
    <Table
      size='small'
      pagination={false}
      dataSource={props.headers.map((header, index) => ({
        ...header,
        uniqueKey: `header-${index}`,
      }))}
      rowKey="uniqueKey"
      columns={columns}
      style={{ marginTop: '8px' }}
    />
  );
};

export default ResponseHeaders;
