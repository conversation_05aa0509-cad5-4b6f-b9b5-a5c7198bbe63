import { ArexEnvironment, ArexResponse, sendRequest, sendSCFRequest } from '@arextest/arex-request';

import { ParameterizedData } from '../BatchRun';
import { hasParameterizedMarkers, replaceParameterizedData } from '../utils/ParameterizedUtils';

export async function executeParameterizedRequests(
  originalRequest: any,
  parameterizedData: ParameterizedData[],
  environment?: ArexEnvironment,
  onProgress?: (current: number, total: number) => void,
  onResult?: (results: ArexResponse[]) => void,
  concurrencyLimit: number = 5, // 新增并发限制参数，默认为5
): Promise<ArexResponse[]> {
  const allResults: ArexResponse[] = new Array(parameterizedData.length).fill(null);
  let currentIndex = 0;
  let runningRequests = 0;
  let completedRequests = 0;
  const totalRequests = parameterizedData.length;

  return new Promise((resolve, reject) => {
    function processNext() {
      if (completedRequests === totalRequests) {
        resolve(allResults.filter(Boolean));
        return;
      }

      while (runningRequests < concurrencyLimit && currentIndex < totalRequests) {
        const taskIndex = currentIndex;
        currentIndex++;
        runningRequests++;

        (async () => {
          try {
            const paramData = parameterizedData[taskIndex];
            const parameterizedRequest = JSON.parse(JSON.stringify(originalRequest));

            if (hasParameterizedMarkers(parameterizedRequest.endpoint)) {
              parameterizedRequest.endpoint = replaceParameterizedData(
                parameterizedRequest.endpoint,
                paramData,
              );
            }

            processRequestBody(parameterizedRequest, paramData);
            processRequestHeaders(parameterizedRequest, paramData);

            let result: ArexResponse;
            if (parameterizedRequest.requestType === 'SCF') {
              console.log('SCF request');
              // SCF requests require request.body and request.body.scf to be a string
              if (parameterizedRequest.body && typeof parameterizedRequest.body.scf === 'string') {
                result = await processSCFRequest(parameterizedRequest, environment, paramData);
              } else {
                // Invalid SCF request structure
                const errorMessage = `Invalid SCF request for endpoint ${
                  parameterizedRequest.endpoint
                }: 'body.scf' is missing or not a string. Body: ${JSON.stringify(
                  parameterizedRequest.body,
                )}`;
                console.error(errorMessage);
                throw new Error(errorMessage); // This will be caught by the outer try-catch
              }
            } else {
              // For non-SCF requests
              result = await sendRequest(parameterizedRequest, environment);
            }

            allResults[taskIndex] = result;
          } catch (error) {
            console.error(`参数化请求错误 (索引 ${taskIndex}):`, error);
            allResults[taskIndex] = {
              response: {
                statusCode: 500,
                statusText: '请求失败',
                headers: {},
                body: String(error),
              },
              testResult: null,
              consoles: [],
              visualizer: null,
            } as unknown as ArexResponse;
          } finally {
            runningRequests--;
            completedRequests++;
            onProgress?.(completedRequests, totalRequests);
            // 为了避免过于频繁的onResult回调，可以考虑在这里进行节流或仅在特定条件下调用
            // 例如，每完成一定数量的请求或所有请求完成后再统一回调
            // 当前的实现是每次完成后都尝试回调，如果parameterizedData非常大，这仍然可能导致性能问题
            // 考虑改为: if (completedRequests % concurrencyLimit === 0 || completedRequests === totalRequests)
            onResult?.(allResults.filter(Boolean));
            processNext();
          }
        })();
      }
    }

    processNext();
  });
}

// 处理请求体中的参数化标记
function processRequestBody(request: any, paramData: ParameterizedData): void {
  if (!request.body) return;

  // 处理特殊的body.body结构
  if (request.body.body && hasParameterizedMarkers(request.body.body)) {
    request.body.body = replaceParameterizedData(request.body.body, paramData);
  }

  // 处理字符串类型的body
  if (typeof request.body === 'string' && hasParameterizedMarkers(request.body)) {
    request.body = replaceParameterizedData(request.body, paramData);
  }
  // 处理对象类型的body (JSON)
  else if (typeof request.body === 'object') {
    const bodyStr = JSON.stringify(request.body);
    if (hasParameterizedMarkers(bodyStr)) {
      const replacedBodyStr = replaceParameterizedData(bodyStr, paramData);
      request.body = JSON.parse(replacedBodyStr);
    }
  }

  // 处理raw类型
  if (request.body.raw && hasParameterizedMarkers(request.body.raw)) {
    request.body.raw = replaceParameterizedData(request.body.raw, paramData);
  }

  // 处理urlencoded类型
  if (request.body.urlencoded) {
    request.body.urlencoded = request.body.urlencoded.map((item: any) => ({
      ...item,
      key: hasParameterizedMarkers(item.key)
        ? replaceParameterizedData(item.key, paramData)
        : item.key,
      value: hasParameterizedMarkers(item.value)
        ? replaceParameterizedData(item.value, paramData)
        : item.value,
    }));
  }

  // 处理formdata类型
  if (request.body.formdata) {
    request.body.formdata = request.body.formdata.map((item: any) => ({
      ...item,
      key: hasParameterizedMarkers(item.key)
        ? replaceParameterizedData(item.key, paramData)
        : item.key,
      value: hasParameterizedMarkers(item.value)
        ? replaceParameterizedData(item.value, paramData)
        : item.value,
    }));
  }
}

// 处理请求头中的参数化标记
function processRequestHeaders(request: any, paramData: ParameterizedData): void {
  if (!request.headers) return;

  request.headers = request.headers.map((header: any) => ({
    ...header,
    key: hasParameterizedMarkers(header.key)
      ? replaceParameterizedData(header.key, paramData)
      : header.key,
    value: hasParameterizedMarkers(header.value)
      ? replaceParameterizedData(header.value, paramData)
      : header.value,
  }));
}

// 处理SCF请求
async function processSCFRequest(
  request: any,
  environment?: ArexEnvironment,
  paramData?: ParameterizedData,
): Promise<ArexResponse> {
  // 深拷贝 scfInfo，避免修改原始的 request.body.scf
  const scfInfo = JSON.parse(request.body.scf);

  // 替换scf中的参数化标记
  if (paramData) {
    Object.keys(scfInfo).forEach((key) => {
      if (typeof scfInfo[key] === 'string' && hasParameterizedMarkers(scfInfo[key])) {
        scfInfo[key] = replaceParameterizedData(scfInfo[key], paramData);
      }
    });
  }

  request.body.scf = JSON.stringify(scfInfo);
  request.body['scfRequest'] = scfInfo;

  // 处理环境变量
  const variables_env = environment?.variables;
  if (variables_env != null && variables_env.length > 0) {
    const hostObject = variables_env.find((obj) => obj.key === 'SCF_HOST');
    if (hostObject) {
      scfInfo.ip = hostObject.value;
    }
  }

  return sendSCFRequest({ ...request, ...scfInfo }, environment);
}
