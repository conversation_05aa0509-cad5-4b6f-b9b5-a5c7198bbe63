import {
  base64Decode,
  ContextMenuItem,
  DiffJsonView,
  DiffJsonViewProps,
  DiffJsonViewRef,
  DiffMatch,
  EmptyWrapper,
  FlexCenterWrapper,
  getJsonValueByPath,
  JSONEditor,
  jsonIndexPathFilter,
  Label,
  OnRenderContextMenu,
  removeAllArrayInObject,
  SpaceBetweenWrapper,
  TagBlock,
  TargetEditor,
  tryStringifyJson,
  useTranslation,
} from '@arextest/arex-core';
import { css } from '@emotion/react';
import { useRequest } from 'ahooks';
import { Allotment } from 'allotment';
import { App, Flex, Input, Menu, Modal, Spin, theme, Typography } from 'antd';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { ScheduleService } from '@/services';
import { DiffLog, InfoItem } from '@/services/ReportService';
import { DIFF_TYPE } from '@/services/ScheduleService';
import { isObjectOrArray } from '@/utils';

import PathTitle from './CaseDiffTitle';
import { setDatasets } from 'react-chartjs-2/dist/utils';

export const SummaryCodeMap: { [key: string]: { color: string; message: string } } = {
  '0': {
    color: 'success',
    message: 'SUCCESS', // 'COMPARED_WITHOUT_DIFFERENCE'
  },
  '1': {
    color: 'magenta',
    message: 'COMPARED_WITH_DIFFERENCE',
  },
  '2': {
    color: 'error',
    message: 'EXCEPTION', // 'COMPARED_INTERNAL_EXCEPTION'
  },
  '3': {
    color: 'orange',
    message: 'SEND_FAILED_NOT_COMPARE',
  },
};

export enum IgnoreType {
  Global,
  Interface,
  Temporary,
}

export type CompareResultDetail = {
  id: string;
  categoryName: string;
  operationName: string;
  diffResultCode: number;
  logInfos: DiffLog[] | null;
  exceptionMsg: string | null;
  baseMsg: string;
  testMsg: string;
};
export interface DiffPathViewerProps extends DiffJsonViewProps {
  loading?: boolean;
  data: InfoItem;
  height?: string;
  defaultActiveFirst?: boolean;
  onChange?: (record?: InfoItem, data?: CompareResultDetail) => void;
  onIgnoreKey?: (path: string[], type: IgnoreType) => void;
  onSortKey?: (path: string[]) => void;
}

const CaseDiffViewer: FC<DiffPathViewerProps> = (props) => {
  const { t } = useTranslation('components');
  const { token } = theme.useToken();
  const { message } = App.useApp();

  const [openConditionalIgnore, setOpenConditionalIgnore] = useState(false);

  const [decodeData, setDecodeData] = useState('');

  const [arrayElement, setArrayElement] = useState<{
    json: string; // Json string
    element: any; // Nearest superior array element to the ignored node
    basePath: string[]; // Element path in json
    relativePath: string[]; // Path relative to the elements
  }>();
  const [referencePath, setReference] = useState<{ path: string[]; value: string }>();
  const fullPath = useMemo(
    () =>
      referencePath?.path.length
        ? jsonIndexPathFilter(arrayElement?.basePath, arrayElement?.json)
            .concat(`[${referencePath.path.join('/')}=${referencePath.value}]`)
            .concat(jsonIndexPathFilter(arrayElement?.relativePath, arrayElement?.element))
        : [],
    [arrayElement, referencePath],
  );

  const jsonDiffViewRef = useRef<DiffJsonViewRef>(null);

  const {
    data: { data: diffMsg, encrypted } = {
      data: {
        id: '',
        categoryName: '',
        operationName: '',
        diffResultCode: 0,
        logInfos: null,
        exceptionMsg: null,
        baseMsg: '',
        testMsg: '',
      },
      encrypted: true,
    },
    loading: loadingDiffMsg,
  } = useRequest(ScheduleService.queryDiffMsgById, {
    defaultParams: [{ id: props.data.id }],
    onSuccess: (data) => {
      props.onChange?.(props.data);
    },
  });

  const {
    data: logEntity,
    loading: loadingLogEntity,
    run: queryLogEntity,
  } = useRequest(
    (logIndex) =>
      ScheduleService.queryLogEntity({
        compareResultId: diffMsg!.id,
        logIndex,
      }),
    {
      manual: true,
      ready: !!diffMsg && props.data.id === diffMsg.id,
      onSuccess: (data) => {
        //console.log('LogEntity data:', data); // 打印 logEntity 数据
        //console.log('BaseValue:', data.baseValue); // 打印 baseValue
        //console.log('TestValue:', data.testValue); // 打印 testValue
        const leftPath = data.pathPair.leftUnmatchedPath.map((item) => item.nodeName || item.index);
        const rightPath = data.pathPair.rightUnmatchedPath.map(
          (item) => item.nodeName || item.index,
        );
        jsonDiffViewRef.current?.leftScrollTo(leftPath);
        jsonDiffViewRef.current?.rightScrollTo(rightPath);
      },
    },
  );
  useEffect(() => {
    //console.log('DiffMsg updated:', diffMsg); // 打印 diffMsg 数据
    props.defaultActiveFirst &&
      diffMsg?.logInfos?.length &&
      queryLogEntity(diffMsg.logInfos[0].logIndex);
  }, [diffMsg?.id]);
  useEffect(() => {
    //console.log('LogEntity updated:', logEntity); // 打印 logEntity 数据
  }, [logEntity]);

  const handleIgnoreKey1 = (
    path: string[],
    value: unknown,
    target: TargetEditor,
    type: IgnoreType,
  ) => {
    const filteredPath = jsonIndexPathFilter(
      path,
      target === 'left' ? diffMsg?.baseMsg : diffMsg?.testMsg,
    );
    filteredPath && props.onIgnoreKey?.(filteredPath, type);
  };

  

  const handleIgnoreKey = (
    path: string[],
    value: unknown,
    target: TargetEditor,
    type: IgnoreType,
  ) => {
    console.log('Original path:', path);

    if (!path || path.length === 0) return;

    let processedPath: string[] = [];

    // 情况1：单个数字路径，如 ["0"]
    if (path.length === 1 && /^\d+$/.test(path[0])) {
      processedPath = path;
    }
    // 情况2：两个元素且第一个是数字，如 ["0", "houseId"]
    else if (path.length === 2 && /^\d+$/.test(path[0])) {
      processedPath = path;
    }
    // 情况3：处理较长路径
    else {
      const isFirstNumeric = /^\d+$/.test(path[0]);
      const isLastNumeric = /^\d+$/.test(path[path.length - 1]);
      
      // 如果第一个不是数字，移除所有数字索引
      if (!isFirstNumeric) {
        processedPath = path.filter(item => !/^\d+$/.test(item));
      }
      // 如果第一个是数字，需要特殊处理
      else {
        // 如果最后一个也是数字，保留除最后一个之外的所有数字
        if (isLastNumeric) {
          processedPath = path.slice(0, -1);
        }
        // 如果最后一个不是数字，移除倒数第二个数字（与非数字相邻的数字）
        else {
          for (let i = 0; i < path.length; i++) {
            const current = path[i];
            const next = path[i + 1];
            
            // 如果当前是数字且下一个不是数字，跳过当前数字
            if (/^\d+$/.test(current) && next && !/^\d+$/.test(next)) {
              continue;
            }
            processedPath.push(current);
          }
        }
      }
    }

    if (processedPath.length > 0) {
      props.onIgnoreKey?.(processedPath, type);
    }
  };

  const handleConditionalIgnoreKey = (path: string[], value: unknown, target: TargetEditor) => {
    const json = target === 'left' ? diffMsg?.baseMsg : diffMsg?.testMsg;
    let arrayElement: any = undefined;
    const basePath: string[] = [];
    const relativePath: string[] = [];

    for (let index = path.length - 1; index > 0; index--) {
      const slicedPath = path.slice(0, index);
      const node = getJsonValueByPath(json, slicedPath);

      if (Array.isArray(node)) {
        arrayElement = removeAllArrayInObject((node as Array<any>)[Number(path[index])]); // TODO filter Array
        basePath.push(...slicedPath);
        relativePath.push(...path.slice(index + 1));
        break;
      }
    }

    if (arrayElement === undefined) return message.error(t('replayCase.preciseIgnoreError'));
    setArrayElement({ json, element: arrayElement, basePath, relativePath });
    setOpenConditionalIgnore(true);
  };

  const handleCreateConditionalIgnoreKey = () => {
    if (referencePath?.path.length) {
      fullPath.length && props.onIgnoreKey?.(fullPath, IgnoreType.Interface);
      resetConditionalIgnoreModal();
    } else {
      message.error(t('replayCase.selectConditionNode'));
    }
  };

  const resetConditionalIgnoreModal = () => {
    setOpenConditionalIgnore(false);
    setReference(undefined);
  };

  const handleSortKey = (path: string[], value: unknown, target: TargetEditor) => {
    const filteredPath = jsonIndexPathFilter(
      path,
      target === 'left' ? diffMsg?.baseMsg : diffMsg?.testMsg,
    );
    filteredPath && props.onSortKey?.(filteredPath);
  };

  //本次改动的点在如下部分
  const [modal, contextHolder] = Modal.useModal();

  // 定义一个辅助函数来检测是否为 Base64 编码
  const isBase64Encoded = (str: string) => {
    try {
      return btoa(atob(str)) === str;
    } catch (err) {
      return false;
    }
  };

  // 定义一个辅助函数来解码并解析 JSON
  const decodeAndParseJson = (encodedString: string) => {
    try {
      // 先解码 Base64
      let decodedString = atob(encodedString);
      console.log('Decoded string:', decodedString); // 调试信息
      // 然后解析为 JSON 对象
      return JSON.parse(decodedString);
    } catch (error) {
      console.error('Failed to decode or parse JSON:', error);
      message.error(t('jsonDiff.failedToDecodeBase64'));
      return null;
    }
  };

  // 定义一个辅助函数来解析 JSON 字符串
  const parseJson = (jsonString: string) => {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Failed to parse JSON:', error);
      message.error(t('jsonDiff.failedToParseJson'));
      return null;
    }
  };
  const handleDiffMatch = useCallback(
    (path: string[]) => {
      // 打印完整的 diffMsg 结构
      console.log('Full diffMsg:', diffMsg);

      // 初始尝试获取 baseMsg 和 testMsg
      let text1 = tryStringifyJson(getJsonValueByPath(diffMsg?.baseMsg, path));
      let text2 = tryStringifyJson(getJsonValueByPath(diffMsg?.testMsg, path));

      // 打印原始值，用于调试
      console.log("Initial text1 is:", text1);
      console.log("Initial text2 is:", text2);

      // 如果 text1 和 text2 都为空，则使用另一种方式处理
      if (!text1 && !text2) {
        console.log('Falling back to alternative path extraction...');

        // 获取路径对应的值
        const baseJsonValue = getJsonValueByPath(diffMsg, [...path, 'baseMsg']);
        const testJsonValue = getJsonValueByPath(diffMsg, [...path, 'testMsg']);

        // 打印原始值，用于调试
        console.log('Extracted baseMsg:', baseJsonValue);
        console.log('Extracted testMsg:', testJsonValue);

        // 检查路径提取的结果是否有效
        if (baseJsonValue === undefined || baseJsonValue === null) {
          message.error(t('jsonDiff.baseMsgNotFound'));
          return;
        }
        if (testJsonValue === undefined || testJsonValue === null) {
          message.error(t('jsonDiff.testMsgNotFound'));
          return;
        }

        // 根据格式进行处理
        let parsedBaseJson, parsedTestJson;

        if (typeof baseJsonValue === 'string') {
          if (isBase64Encoded(baseJsonValue)) {
            parsedBaseJson = decodeAndParseJson(baseJsonValue);
          } else {
            parsedBaseJson = parseJson(baseJsonValue);
          }
        } else {
          parsedBaseJson = baseJsonValue;
        }

        if (typeof testJsonValue === 'string') {
          if (isBase64Encoded(testJsonValue)) {
            parsedTestJson = decodeAndParseJson(testJsonValue);
          } else {
            parsedTestJson = parseJson(testJsonValue);
          }
        } else {
          parsedTestJson = testJsonValue;
        }

        // 打印解析后的 JSON 数据，用于调试
        console.log('Parsed baseMsg:', parsedBaseJson);
        console.log('Parsed testMsg:', parsedTestJson);

        // 检查解析结果是否有效
        if (parsedBaseJson === null || parsedBaseJson === undefined) {
          message.error(t('jsonDiff.failedToParseBaseMsg'));
          return;
        }
        if (parsedTestJson === null || parsedTestJson === undefined) {
          message.error(t('jsonDiff.failedToParseTestMsg'));
          return;
        }

        // 将解析后的 JSON 对象或原始值转换为字符串
        text1 = JSON.stringify(parsedBaseJson, null, 2);
        text2 = JSON.stringify(parsedTestJson, null, 2);
      }

      // 使用 DiffMatch 组件显示差异
      modal.info({
        footer: false,
        maskClosable: true,
        width: '50%',
        title: t('replay.diffMatch'),
        content: <DiffMatch text1={text1} text2={text2} />,
      });
    },
    [diffMsg, t]
  );

  //原来的显示差异
  // const handleDiffMatch = useCallback(
  //   (path: string[]) => {
  //     const text1 = tryStringifyJson(getJsonValueByPath(diffMsg?.baseMsg, path));
  //     const text2 = tryStringifyJson(getJsonValueByPath(diffMsg?.testMsg, path));

  //     modal.info({
  //       footer: false,
  //       maskClosable: true,
  //       width: '50%',
  //       title: t('replay.diffMatch'),
  //       content: <DiffMatch text1={text1} text2={text2} />,
  //     });
  //   },
  //   [diffMsg, t],
  // );
  const handleNodeDecode = (value: string) => {
    try {
      // 去除字符串两端的双引号并打印出来用于调试
      const cleanValue = value.replace(/^"|"$/g, '');
      console.log('Cleaned string:', cleanValue);

      // 检查是否为 Base64 编码
      if (isBase64Encoded(cleanValue)) {
        console.log('Detected Base64 encoded string. Attempting to decode...');

        // 尝试解码 Base64 编码的字符串
        const decodedData = base64Decode(cleanValue);
        console.log('Decoded data:', decodedData);

        setDecodeData(decodedData);
      } else {
        console.log('String is not Base64 encoded. No decoding performed.');

        // 如果不是 Base64 编码，则直接设置原始值
        setDecodeData(cleanValue);
      }
    } catch (e) {
      // 打印详细的错误信息
      console.error('Failed to decode Base64 string:', e);
    }
  };

  const handleShowDecodedDifference = useCallback(
    (path: string[]) => {
      // 打印完整的 diffMsg 结构
      console.log('Full diffMsg:', diffMsg);

      // 初始尝试获取 baseMsg 和 testMsg
      let text1 = tryStringifyJson(getJsonValueByPath(diffMsg?.baseMsg, path));
      let text2 = tryStringifyJson(getJsonValueByPath(diffMsg?.testMsg, path));

      // 打印原始值，用于调试
      console.log("Initial text1 is:", text1);
      console.log("Initial text2 is:", text2);

      // 如果 text1 和 text2 都为空，则使用另一种方式处理
      if (!text1 && !text2) {
        console.log('Falling back to alternative path extraction...');

        // 获取路径对应的值
        const baseJsonValue = getJsonValueByPath(diffMsg, [...path, 'baseMsg']);
        const testJsonValue = getJsonValueByPath(diffMsg, [...path, 'testMsg']);

        // 打印原始值，用于调试
        console.log('Extracted baseMsg:', baseJsonValue);
        console.log('Extracted testMsg:', testJsonValue);

        // 检查路径提取的结果是否有效
        if (baseJsonValue === undefined || baseJsonValue === null) {
          message.error(t('jsonDiff.baseMsgNotFound'));
          return;
        }
        if (testJsonValue === undefined || testJsonValue === null) {
          message.error(t('jsonDiff.testMsgNotFound'));
          return;
        }

        // 根据格式进行处理
        let parsedBaseJson, parsedTestJson;

        if (typeof baseJsonValue === 'string') {
          if (isBase64Encoded(baseJsonValue)) {
            parsedBaseJson = decodeAndParseJson(baseJsonValue);
          } else {
            parsedBaseJson = parseJson(baseJsonValue);
          }
        } else {
          parsedBaseJson = baseJsonValue;
        }

        if (typeof testJsonValue === 'string') {
          if (isBase64Encoded(testJsonValue)) {
            parsedTestJson = decodeAndParseJson(testJsonValue);
          } else {
            parsedTestJson = parseJson(testJsonValue);
          }
        } else {
          parsedTestJson = testJsonValue;
        }

        // 打印解析后的 JSON 数据，用于调试
        console.log('Parsed baseMsg:', parsedBaseJson);
        console.log('Parsed testMsg:', parsedTestJson);

        // 检查解析结果是否有效
        if (parsedBaseJson === null || parsedBaseJson === undefined) {
          message.error(t('jsonDiff.failedToParseBaseMsg'));
          return;
        }
        if (parsedTestJson === null || parsedTestJson === undefined) {
          message.error(t('jsonDiff.failedToParseTestMsg'));
          return;
        }

        //解码base64
        try {
          // 去除字符串两端的双引号并打印出来用于调试
          const cleanValue = parsedBaseJson.replace(/^"|"$/g, '');
          const cleanValue2 = parsedTestJson.replace(/^"|"$/g, '');
          console.log('Cleaned base64 string:', cleanValue);

          // 尝试解码 Base64 编码的字符串
           text1 = base64Decode(cleanValue);
           text2 = base64Decode(cleanValue2);

        } catch (e) {
          // 打印详细的错误信息
          console.error('Failed to decode Base64 string:', e);

        }
      }

      // 使用 DiffMatch 组件显示差异
      modal.info({
        footer: false,
        maskClosable: true,
        width: '50%',
        title: t('replay.diffMatch'),
        content: <DiffMatch text1={text1} text2={text2} />,
      });
    },
    [diffMsg, t]
  );

  const contextMenuRender: OnRenderContextMenu = (path, value, target, oriData = {}) => {
    const isArrayNode = Array.isArray(value);
    const isLeafNode = !!value && !isObjectOrArray(value);
    const isRootNode = !path?.length;

    console.log("path is", path);
    console.log("value is", value);

    // 使用解构赋值和默认值来安全地访问 oriData[target]
    const { [target]: fallbackValue } = oriData;
    value = value !== undefined ? value : fallbackValue;

    return [
      {
        type: 'row',
        items: [
          {
            type: 'column',
            items: ([] as ContextMenuItem[])
              .concat(
                isRootNode
                  ? []
                  : [
                      {
                        type: 'dropdown-button',
                        width: 'max-content',
                        main: {
                          type: 'button',
                          text: t('jsonDiff.ignore')!,
                          onClick: () => handleIgnoreKey(path, value, target, IgnoreType.Global),
                        },
                        items: [
                          {
                            type: 'button',
                            text: t('jsonDiff.ignoreToGlobal')!,
                            onClick: () => handleIgnoreKey(path, value, target, IgnoreType.Global),
                          },
                          {
                            type: 'button',
                            text: t('jsonDiff.ignoreToInterfaceOrDependency')!,
                            onClick: () =>
                              handleIgnoreKey(path, value, target, IgnoreType.Interface),
                          },
                          {
                            type: 'button',
                            text: t('jsonDiff.temporaryIgnore')!,
                            onClick: () =>
                              handleIgnoreKey(path, value, target, IgnoreType.Temporary),
                          },
                          {
                            type: 'button',
                            text: t('jsonDiff.conditionalIgnore')!,
                            disabled: Array.isArray(value), // TODO disabled when not in array
                            onClick: () => handleConditionalIgnoreKey(path, value, target),
                          },
                        ],
                      },
                    ],
              )
              .concat(
                isArrayNode
                  ? [
                      {
                        type: 'button',
                        text: t('jsonDiff.sort')!,
                        onClick: () => handleSortKey(path, value, target),
                      },
                    ]
                  : [],
              )
              .concat(
                isLeafNode || isRootNode
                  ? [
                      {
                        type: 'button',
                        text: t('jsonDiff.diffMatch')!,
                        onClick: () => handleDiffMatch(path),
                      },
                      {
                        type: 'dropdown-button',
                        width: 'max-content',
                        main: {
                          type: 'button',
                          text: t('jsonDiff.decode')!,
                          onClick: () => handleNodeDecode(value as string),
                        },
                        items: [
                          {
                            type: 'button',
                            text: t('解析显示差异')!, // 显示解析差异
                            onClick: () => handleShowDecodedDifference(path),
                          },
                        ],
                      },
                    ]
                  : [],
              ),
          },
        ],
      },
    ] as ContextMenuItem[];
  };

  return (
    <EmptyWrapper loading={loadingDiffMsg} empty={!diffMsg}>
      <Allotment
        css={css`
          height: ${props.height};
          overflow: visible !important;
          .split-view-view-visible:has(.json-diff-viewer) {
            overflow: visible !important;
          }
        `}
      >
        <Allotment.Pane preferredSize={200}>
          {diffMsg && [0, 2].includes(diffMsg?.diffResultCode) ? (
            <FlexCenterWrapper>
              <Typography.Text type='secondary'>
                {SummaryCodeMap[diffMsg?.diffResultCode].message}
              </Typography.Text>
            </FlexCenterWrapper>
          ) : (
            <>
              <SpaceBetweenWrapper>
                <Typography.Text
                  type='secondary'
                  style={{
                    display: 'inline-block',
                    margin: `${token.marginSM}px 0 0 ${token.margin}px`,
                  }}
                >
                  {t('replay.pointOfDifference')}
                </Typography.Text>
                <Spin
                  size='small'
                  spinning={loadingLogEntity}
                  css={css`
                    margin-right: 8px;
                    span {
                      font-size: 16px !important;
                    }
                  `}
                />
              </SpaceBetweenWrapper>
              <Menu
                defaultSelectedKeys={props.defaultActiveFirst ? ['0'] : undefined}
                items={diffMsg?.logInfos?.map((log, index) => {
                  return {
                    label: <PathTitle diffLog={log} />,
                    key: index,
                  };
                })}
                css={css`
                  height: 100%;
                  overflow-y: auto;
                  padding: 4px 8px 0;
                  .ant-menu-item {
                    height: 26px;
                    line-height: 26px;
                  }
                  border-inline-end: none !important;
                `}
                onClick={({ key }) => {
                  diffMsg?.logInfos?.length &&
                    queryLogEntity(diffMsg.logInfos[parseInt(key)].logIndex);
                }}
              />
            </>
          )}
        </Allotment.Pane>

        <Allotment.Pane
          visible
          className='json-diff-viewer'
          css={css`
            height: ${props.height};
          `}
        >
          {diffMsg?.diffResultCode === 2 ? (
            <FlexCenterWrapper style={{ padding: '16px' }}>
              <Typography.Text type='secondary'>{diffMsg.exceptionMsg}</Typography.Text>
            </FlexCenterWrapper>
          ) : (
            <div style={{ position: 'relative', margin: `${token.marginXS}px`, height: '100%' }}>
              <DiffJsonView
                ref={jsonDiffViewRef}
                height={`calc(${props.height} - 8px)`}
                hiddenValue={encrypted}
                diffJson={{
                  left: diffMsg?.baseMsg || '',
                  right: diffMsg?.testMsg || '',
                }}
                onClassName={(path, value, target) =>
                  logEntity?.pathPair[`${target}UnmatchedPath`]
                    .map((item) => item.nodeName || item.index.toString())
                    .join(',') === path.join(',')
                    ? logEntity?.pathPair.unmatchedType === DIFF_TYPE.UNMATCHED
                      ? 'json-difference-node'
                      : 'json-additional-node'
                    : ''
                }
                onRenderContextMenu={(path, value, target) => contextMenuRender(path, value, target, {
                  left: diffMsg?.baseMsg || '',
                  right: diffMsg?.testMsg || '',
                })}
              />
            </div>
          )}
        </Allotment.Pane>
      </Allotment>

      {/* JsonDiffMatchModal */}
      {contextHolder}

      {/* NodeDecodeModal */}
      {/* <Modal
        destroyOnClose
        footer={false}
        open={!!decodeData}
        title={t('base64DecodeContent')}
        onCancel={() => setDecodeData('')}
      >
        <Input.TextArea readOnly value={decodeData} />
      </Modal> */}
      <Modal
        destroyOnClose
        footer={false}
        open={!!decodeData}
        title={t('base64DecodeContent')}
        onCancel={() => setDecodeData('')}
        bodyStyle={{ height: 'auto', overflow: 'hidden' }} // 移除最大高度和溢出滚动
        wrapClassName="custom-modal" // 添加自定义类名以便于选择器
      >
        <Input.TextArea
          readOnly
          value={decodeData}
          autoSize={{ minRows: 3, maxRows: 15 }} // 根据内容自动调整行数
          style={{ height: 'auto', flexGrow: 1 }} // 确保文本区域可以伸缩
        />
      </Modal>

      {/* ConditionalIgnoreKeyModal */}
      <Modal
        destroyOnClose
        open={openConditionalIgnore}
        width='60%'
        title={t('replayCase.conditionalIgnore')}
        onOk={handleCreateConditionalIgnoreKey}
        onCancel={resetConditionalIgnoreModal}
      >
        <div
          css={css`
            .json-conditional-ignore-node {
              background-color: ${token.colorErrorBgHover};
            }
            .json-ignore-reference-node {
              background-color: ${token.colorSuccessBgHover};
            }
            .json-conditional-ignore-node.json-ignore-reference-node {
              background: linear-gradient(
                to bottom,
                ${token.colorErrorBgHover} 0%,
                ${token.colorErrorBgHover} 50%,
                ${token.colorSuccessBgHover} 50%,
                ${token.colorSuccessBgHover} 100%
              );
            }
          `}
        >
          <Flex justify='space-between' align={'center'} style={{ marginBottom: '8px' }}>
            <Flex>
              <TagBlock color={token.colorErrorBgHover} title={t('replayCase.ignoreNode')} />
              <TagBlock color={token.colorSuccessBgHover} title={t('replayCase.conditionNode')} />
            </Flex>
          </Flex>

          <JSONEditor
            readOnly
            height='400px'
            content={{ json: arrayElement?.element }}
            onClassName={(path) => {
              if (
                path?.join(',') === arrayElement?.relativePath.join(',') &&
                path?.join(',') === referencePath?.path.join(',')
              )
                return 'json-conditional-ignore-node json-ignore-reference-node';
              if (path?.join(',') === arrayElement?.relativePath.join(','))
                return 'json-conditional-ignore-node';
              if (referencePath?.path.join(',') === path?.join(','))
                return 'json-ignore-reference-node';
            }}
            onSelect={(selection) => {
              // 打印 selection 和 target
            console.log('Selected value:', selection.value);
            // 确保 logEntity 已加载
            if (!logEntity) {
              console.warn('Log entity is not loaded yet');
              message.error(t('jsonDiff.logEntityNotLoaded'));
              return;
            }
              const isLeafNode = !!selection.value && !isObjectOrArray(selection.value);
              setReference(() =>
                isLeafNode ? { path: selection.path, value: String(selection.value) } : undefined,
              );
            }}
          />
        </div>
        <div style={{ flex: 1, marginTop: '12px' }}>
          <Label>{t('replayCase.ignorePath')}</Label>
          {fullPath.length ? (
            <Typography.Text>{fullPath.join('/')}</Typography.Text>
          ) : (
            <Typography.Text type='secondary'>
              {t('replayCase.selectConditionNodeTip')}
            </Typography.Text>
          )}
        </div>

        <Label style={{ opacity: 0 }}>{t('replayCase.ignorePath')}</Label>
        {!!fullPath.length && (
          <Typography.Text type='secondary'>
            {'IGNORE '}
            <Typography.Text code>
              {jsonIndexPathFilter(arrayElement?.relativePath, arrayElement?.element).join('/')}
            </Typography.Text>
            {' WHEN '}
            <Typography.Text code>
              {`${referencePath?.path.join('/')} = ${referencePath?.value}`}
            </Typography.Text>
          </Typography.Text>
        )}
      </Modal>
    </EmptyWrapper>
  );
};

export default CaseDiffViewer;
