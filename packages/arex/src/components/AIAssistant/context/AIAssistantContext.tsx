import { getLocalStorage } from '@arextest/arex-core';
import React, { createContext, FC, ReactNode, useContext, useState } from 'react';

import { EMAIL_KEY } from '@/constant';
import { ChatHistory, Message, sendMessage } from '@/services/AIAssistantService';

interface AIAssistantContextType {
  visible: boolean;
  messages: Message[];
  chatHistory: ChatHistory[];
  currentMessage: string;
  streamingMessage: string;
  isStreaming: boolean;
  historyVisible: boolean;
  quickCommandsVisible: boolean;
  loading: boolean;
  feedbackStatus: Record<string, number>;
  setVisible: (visible: boolean) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  setChatHistory: React.Dispatch<React.SetStateAction<ChatHistory[]>>;
  setCurrentMessage: (message: string) => void;
  setStreamingMessage: (message: string) => void;
  setIsStreaming: (isStreaming: boolean) => void;
  setHistoryVisible: (visible: boolean) => void;
  setQuickCommandsVisible: (visible: boolean) => void;
  setLoading: (loading: boolean) => void;
  setFeedbackStatus: React.Dispatch<React.SetStateAction<Record<string, number>>>;
  analyzeRecord: (recordId: string) => void;
  handleSendMessage: (content: string, moduleName?: string) => Promise<void>;
  handleRegenerateResponse: (messageId: string) => Promise<void>;
}

const AIAssistantContext = createContext<AIAssistantContextType | undefined>(undefined);

export const useAIAssistantContext = () => {
  const context = useContext(AIAssistantContext);
  if (!context) {
    throw new Error('useAIAssistantContext must be used within an AIAssistantProvider');
  }
  return context;
};

interface AIAssistantProviderProps {
  children: ReactNode;
}

const AIAssistantContextProvider: FC<AIAssistantProviderProps> = ({ children }) => {
  const [visible, setVisible] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [streamingMessage, setStreamingMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [quickCommandsVisible, setQuickCommandsVisible] = useState(true);
  const [loading, setLoading] = useState(false);
  const [feedbackStatus, setFeedbackStatus] = useState<Record<string, number>>({});

  // 处理发送消息
  const handleSendMessage = async (content: string, moduleName: string = '') => {
    if (!content.trim() || isStreaming) return;

    setIsStreaming(true);
    setStreamingMessage('');

    try {
      // 构建请求参数
      const params = {
        oa: getLocalStorage(EMAIL_KEY) as string,
        message: content,
        moduleName: moduleName,
        serviceName: 'wreplay-copilot',
        triggerUrl: window.location.href,
        isHidden: false,
      };

      const response = await sendMessage(params);

      if (!response || !response.ok || !response.body) {
        throw new Error('Invalid response');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let completeResponse = '';
      let buffer = '';
      let responseId = ''; // 添加变量用于存储从响应中解析的ID

      let isReading = true;
      while (isReading) {
        const { done, value } = await reader.read();

        if (done) {
          isReading = false;

          // 处理缓冲区中可能剩余的数据
          if (buffer.trim()) {
            try {
              if (buffer.includes('data:')) {
                const dataContent = buffer.substring(buffer.indexOf('data:') + 5).trim();
                if (dataContent) {
                  try {
                    const jsonData = JSON.parse(dataContent);

                    // 尝试从响应中提取ID
                    if (jsonData.data?.id && !responseId) {
                      responseId = jsonData.data.id;
                    }

                    // 只提取需要的内容，避免输出整个JSON
                    if (jsonData.content) {
                      completeResponse += jsonData.content;
                    } else if (jsonData.data?.streamResp) {
                      const processedContent = jsonData.data.streamResp.replace(/\\n/g, '\n');
                      completeResponse += processedContent;
                    } else if (jsonData.data?.content) {
                      completeResponse += jsonData.data.content;
                    }
                    setStreamingMessage(completeResponse);
                  } catch (e) {
                    console.warn('解析最终缓冲区JSON失败:', e);
                    // 不要在解析失败时添加原始JSON文本
                  }
                }
              }
            } catch (e) {
              console.warn('处理最终缓冲区失败:', e);
            }
          }
          break;
        }

        // 解码当前数据块
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理完整的数据行
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim().startsWith('data:')) {
            try {
              // 提取data后的内容
              const dataContent = line.substring(line.indexOf('data:') + 5).trim();

              if (dataContent) {
                try {
                  // 尝试解析JSON
                  const jsonData = JSON.parse(dataContent);

                  // 尝试从响应中提取ID
                  if (jsonData.data?.id && !responseId) {
                    responseId = jsonData.data.id;
                  }

                  // 只提取需要的内容字段，避免输出整个JSON对象
                  if (jsonData.content) {
                    // 如果有content字段，直接使用
                    const newContent = jsonData.content;
                    if (typeof newContent === 'string') {
                      completeResponse += newContent;
                    }
                  } else if (jsonData.data?.streamResp) {
                    // 处理streamResp字段
                    const processedContent = jsonData.data.streamResp.replace(/\\n/g, '\n');
                    completeResponse += processedContent;
                  } else if (jsonData.data?.content) {
                    // 处理data.content字段
                    const newContent = jsonData.data.content;
                    if (typeof newContent === 'string') {
                      completeResponse += newContent;
                    }
                  }
                  // 不再使用fallback方式输出整个JSON

                  setStreamingMessage(completeResponse);
                } catch (e) {
                  // 如果解析JSON失败，不要直接添加原始内容
                  console.warn('JSON parsing error:', e);
                  // 尝试提取可能的纯文本内容
                  if (dataContent.startsWith('"') && dataContent.endsWith('"')) {
                    // 可能是JSON字符串，尝试解析
                    try {
                      const textContent = JSON.parse(dataContent);
                      if (typeof textContent === 'string') {
                        completeResponse += textContent;
                        setStreamingMessage(completeResponse);
                      }
                    } catch {
                      // 解析失败，忽略
                    }
                  }
                }
              }
            } catch (e) {
              console.warn('Error processing data line:', e);
            }
          } else if (line.trim() && !line.includes('{') && !line.includes('}')) {
            // 只处理不包含JSON结构的纯文本行
            completeResponse += line + '\n';
            setStreamingMessage(completeResponse);
          }
        }
      }

      // 确保消息不为空再添加到消息列表
      if (completeResponse.trim()) {
        const assistantMessage = {
          id: responseId || `assistant-${Date.now()}`, // 使用从响应中解析出的ID
          role: 'assistant' as const,
          content: completeResponse,
          timestamp: Date.now(),
        };

        setMessages((prev) => [...prev, assistantMessage]);
      } else {
        console.warn('Empty response, not adding to messages');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: '抱歉，发生了错误，请稍后再试。',
          timestamp: Date.now(),
        },
      ]);
    } finally {
      setIsStreaming(false);
      setStreamingMessage('');
    }
  };

  // 添加智能分析方法
  const analyzeRecord = (recordId: string) => {
    // 显示AI助手
    setVisible(true);

    // 构建分析请求消息
    const analysisMessage = `请帮我分析一下${recordId}`;

    // 创建用户消息
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: analysisMessage,
      timestamp: Date.now(),
    };

    // 添加到消息列表 - 使用函数式更新确保基于最新状态
    setMessages((prevMessages) => {
      const newMessages = [...prevMessages, userMessage];
      return newMessages;
    });

    // 清空当前输入框
    setCurrentMessage('');

    // 发送消息 - 使用setTimeout确保状态更新后再发送
    setTimeout(() => {
      handleSendMessage(analysisMessage, 'WREPLAY_INTELLIGENT_ANALYSIS');
    }, 0);
  };

  // 添加重新生成响应的处理函数
  const handleRegenerateResponse = async (messageId: string) => {
    if (!messageId || isStreaming) return;

    // 移除最后一条助手回复
    const filteredMessages = messages.filter(
      (msg) => !(msg.role === 'assistant' && messages.indexOf(msg) === messages.length - 1),
    );

    setMessages(filteredMessages);
    setIsStreaming(true);
    setStreamingMessage('');

    try {
      // 调用 resend API
      const response = await fetch(`/ai/ai-assistant/resend?id=${messageId}`);

      if (!response.ok) {
        throw new Error('重新生成响应失败');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取响应流');
      }

      const decoder = new TextDecoder();
      let completeResponse = '';
      let buffer = '';
      let responseId = ''; // 用于存储从响应中解析出的ID

      let isReading = true;

      while (isReading) {
        const { done, value } = await reader.read();

        if (done) {
          isReading = false;

          // 处理缓冲区中可能剩余的数据
          if (buffer.trim()) {
            try {
              if (buffer.includes('data:')) {
                const dataContent = buffer.substring(buffer.indexOf('data:') + 5).trim();
                if (dataContent) {
                  try {
                    const jsonData = JSON.parse(dataContent);

                    // 尝试从响应中提取ID
                    if (jsonData.data?.id && !responseId) {
                      responseId = jsonData.data.id;
                    }

                    if (jsonData.content) {
                      completeResponse += jsonData.content;
                    } else if (jsonData.data?.streamResp) {
                      const processedContent = jsonData.data.streamResp.replace(/\\n/g, '\n');
                      completeResponse += processedContent;
                    } else if (jsonData.data?.content) {
                      completeResponse += jsonData.data.content;
                    }
                    setStreamingMessage(completeResponse);
                  } catch (e) {
                    console.warn('解析最终缓冲区JSON失败:', e);
                  }
                }
              }
            } catch (e) {
              console.warn('处理最终缓冲区失败:', e);
            }
          }
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理完整的数据行
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim().startsWith('data:')) {
            try {
              const dataContent = line.substring(line.indexOf('data:') + 5).trim();
              if (dataContent) {
                try {
                  const jsonData = JSON.parse(dataContent);

                  // 尝试从响应中提取ID
                  if (jsonData.data?.id && !responseId) {
                    responseId = jsonData.data.id;
                  }

                  if (jsonData.content) {
                    const newContent = jsonData.content;
                    if (typeof newContent === 'string') {
                      completeResponse += newContent;
                    }
                  } else if (jsonData.data?.streamResp) {
                    const processedContent = jsonData.data.streamResp.replace(/\\n/g, '\n');
                    completeResponse += processedContent;
                  } else if (jsonData.data?.content) {
                    const newContent = jsonData.data.content;
                    if (typeof newContent === 'string') {
                      completeResponse += newContent;
                    }
                  }
                  setStreamingMessage(completeResponse);
                } catch (e) {
                  console.warn('JSON解析错误:', e);
                  if (dataContent.startsWith('"') && dataContent.endsWith('"')) {
                    try {
                      const textContent = JSON.parse(dataContent);
                      if (typeof textContent === 'string') {
                        completeResponse += textContent;
                        setStreamingMessage(completeResponse);
                      }
                    } catch {
                      // 解析失败，忽略
                    }
                  }
                }
              }
            } catch (e) {
              console.warn('处理数据行错误:', e);
            }
          } else if (line.trim() && !line.includes('{') && !line.includes('}')) {
            completeResponse += line + '\n';
            setStreamingMessage(completeResponse);
          }
        }
      }

      // 确保消息不为空再添加到消息列表
      if (completeResponse.trim()) {
        const assistantMessage = {
          id: responseId || `assistant-${Date.now()}`, // 使用从响应中解析出的ID
          role: 'assistant' as const,
          content: completeResponse,
          timestamp: Date.now(),
        };

        setMessages((prev) => [...prev, assistantMessage]);
      } else {
        console.warn('重新生成的响应为空，不添加到消息列表');
      }
    } catch (error) {
      console.error('重新生成响应时出错:', error);
      setMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: '抱歉，重新生成响应时发生了错误，请稍后再试。',
          timestamp: Date.now(),
        },
      ]);
    } finally {
      setIsStreaming(false);
      setStreamingMessage('');
    }
  };

  const value = {
    visible,
    messages,
    chatHistory,
    currentMessage,
    streamingMessage,
    isStreaming,
    historyVisible,
    quickCommandsVisible,
    loading,
    feedbackStatus,
    setVisible,
    setMessages,
    setChatHistory,
    setCurrentMessage,
    setStreamingMessage,
    setIsStreaming,
    setHistoryVisible,
    setQuickCommandsVisible,
    setLoading,
    setFeedbackStatus,
    analyzeRecord,
    handleSendMessage,
    handleRegenerateResponse,
  };

  return <AIAssistantContext.Provider value={value}>{children}</AIAssistantContext.Provider>;
};

export default AIAssistantContextProvider;
