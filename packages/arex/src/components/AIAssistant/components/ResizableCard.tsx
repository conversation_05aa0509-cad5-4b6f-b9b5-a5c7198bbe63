import { ShrinkOutlined } from '@ant-design/icons';
import { css } from '@emotion/react';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import React, { ReactNode } from 'react';
import { Resizable } from 'react-resizable';

import { containerStyles, resizeHandleStyles } from '../styles';

interface ResizableCardProps {
  title: ReactNode;
  width: number;
  height: number;
  position: { right: number; bottom: number };
  isDragging: boolean;
  cardHeaderRef: React.RefObject<HTMLDivElement>;
  onMouseDown: (e: React.MouseEvent) => void;
  onClose: () => void;
  onResizeStart: (e: React.SyntheticEvent, data: { handle: string }) => void;
  onResize: (
    e: React.SyntheticEvent,
    data: { size: { width: number; height: number }; handle: string },
  ) => void;
  children: ReactNode;
}

const ResizableCard: React.FC<ResizableCardProps> = ({
  title,
  width,
  height,
  position,
  isDragging,
  cardHeaderRef,
  onMouseDown,
  onClose,
  onResizeStart,
  onResize,
  children,
}) => {
  return (
    <div
      css={css`
        position: fixed;
        right: ${position.right}px;
        bottom: ${position.bottom}px;
        z-index: 1000;
      `}
    >
      <Resizable
        width={width}
        height={height}
        minConstraints={[300, 400]}
        maxConstraints={[800, window.innerHeight - 50]}
        onResizeStart={onResizeStart}
        onResize={onResize}
        resizeHandles={['nw', 'sw']}
        handle={(h, ref) => {
          const handleClass = `react-resizable-handle react-resizable-handle-${h}`;
          return <div className={handleClass} ref={ref} css={resizeHandleStyles} />;
        }}
      >
        <Card
          title={
            <div
              ref={cardHeaderRef}
              onMouseDown={onMouseDown}
              css={css`
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                cursor: grab;
                user-select: none;
              `}
            >
              {title}
            </div>
          }
          extra={
            <Button
              type="text"
              icon={
                <span
                  role="img"
                  aria-label="close"
                  css={css`
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                  `}
                >
                  <ShrinkOutlined />
                </span>
              }
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
            />
          }
          css={[
            containerStyles.card,
            css`
              width: ${width}px;
              height: ${height}px;
            `,
            isDragging && containerStyles.dragging,
          ]}
        >
          {children}
        </Card>
      </Resizable>
    </div>
  );
};

export default ResizableCard;