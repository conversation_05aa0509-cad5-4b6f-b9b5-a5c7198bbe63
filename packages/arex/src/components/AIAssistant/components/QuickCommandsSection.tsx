import { CloseOutlined } from '@ant-design/icons';
import { css } from '@emotion/react';
import { Button, Typography } from 'antd';
import React, { useEffect } from 'react';

import { Message } from '@/services/AIAssistantService'; // 添加 Message 类型导入

import { useAIAssistantContext } from '../context/AIAssistantContext';
import { quickCommandsStyles } from '../styles';

interface QuickCommandsSectionProps {
  visible: boolean;
  onClose: () => void;
  onCommand: (command: string) => void;
}

const QuickCommandsSection: React.FC<QuickCommandsSectionProps> = ({
  visible,
  onClose,
  onCommand,
}) => {
  const { setMessages, handleSendMessage, setCurrentMessage } = useAIAssistantContext();

  useEffect(() => {
    if (visible) {
      // 延迟执行，确保DOM已经渲染
      setTimeout(() => {
        const container = document.getElementById('quickCommandsContainer');
        const leftArrow = document.getElementById('quickCommandsLeftArrow');
        const rightArrow = document.getElementById('quickCommandsRightArrow');

        if (container && leftArrow && rightArrow) {
          // 初始化时检查是否需要显示右箭头
          if (container.scrollWidth <= container.clientWidth) {
            rightArrow.style.display = 'none';
          } else {
            rightArrow.style.display = 'flex';
          }

          // 初始化时左箭头应该隐藏
          leftArrow.style.display = 'none';
        }
      }, 100);
    }
  }, [visible]);

  if (!visible) return null;

  // 处理命令点击，模拟发送新的聊天内容
  const handleCommandClick = (command: string) => {
    // 创建用户消息，确保 role 是字面量类型 "user"
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user', // 使用字面量类型 'user'，而不是 string 类型
      content: command,
      timestamp: Date.now(),
    };

    // 添加到消息列表
    setMessages((prevMessages) => [...prevMessages, userMessage]);

    // 清空当前输入框
    setCurrentMessage('');

    // 发送消息
    setTimeout(() => {
      handleSendMessage(command);
    }, 0);
  };

  return (
    <div css={quickCommandsStyles.container}>
      <div css={quickCommandsStyles.header}>
        <Typography.Text strong css={quickCommandsStyles.headerText}>
          快捷指令
        </Typography.Text>
        <Button
          type='text'
          size='small'
          icon={<CloseOutlined />}
          onClick={onClose}
          css={quickCommandsStyles.closeButton}
        />
      </div>

      <div css={quickCommandsStyles.scrollContainer}>
        <div
          id='quickCommandsContainer'
          css={quickCommandsStyles.commandsContainer}
          onScroll={(e) => {
            const container = e.currentTarget;
            const leftArrow = document.getElementById('quickCommandsLeftArrow');
            const rightArrow = document.getElementById('quickCommandsRightArrow');

            if (container.scrollLeft <= 0 && leftArrow) {
              leftArrow.style.display = 'none';
            } else if (leftArrow) {
              leftArrow.style.display = 'flex';
            }

            if (
              container.scrollLeft + container.clientWidth >= container.scrollWidth - 5 &&
              rightArrow
            ) {
              rightArrow.style.display = 'none';
            } else if (rightArrow) {
              rightArrow.style.display = 'flex';
            }
          }}
        >
          {/* 常见问题分类 */}
          <div css={quickCommandsStyles.categoryCard}>
            <div css={quickCommandsStyles.categoryHeader}>
              <div
                css={[
                  quickCommandsStyles.categoryIcon,
                  css`
                    color: #1890ff;
                    background-color: #e6f7ff;
                  `,
                ]}
              >
                ?
              </div>
              <Typography.Text strong>常见问题</Typography.Text>
            </div>
            <div css={quickCommandsStyles.categoryDescription}>平台的常见问题</div>
            <div css={quickCommandsStyles.commandsWrapper}>
              <Button
                size='small'
                onClick={() => handleCommandClick('接入流程')}
                css={quickCommandsStyles.commandButton}
              >
                接入流程
              </Button>
              <Button
                size='small'
                onClick={() => handleCommandClick('框架支持')}
                css={quickCommandsStyles.commandButton}
              >
                框架支持
              </Button>
              <Button
                size='small'
                onClick={() => handleCommandClick('流量回放有什么用？')}
                css={quickCommandsStyles.commandButton}
              >
                平台作用
              </Button>
            </div>
          </div>

          {/* 帮助文档分类 */}
          <div css={quickCommandsStyles.categoryCard}>
            <div css={quickCommandsStyles.categoryHeader}>
              <div
                css={[
                  quickCommandsStyles.categoryIcon,
                  css`
                    color: #fa8c16;
                    background-color: #fff7e6;
                  `,
                ]}
              >
                📚
              </div>
              <Typography.Text strong>帮助文档</Typography.Text>
            </div>
            <div css={quickCommandsStyles.categoryDescription}>平台相关操作手册</div>
            <div css={quickCommandsStyles.commandsWrapper}>
              <Button
                size='small'
                onClick={() =>
                  window.open('https://ishare.58corp.com/articleDetail?id=125244', '_blank')
                }
                css={quickCommandsStyles.commandButton}
              >
                平台介绍
              </Button>
              <Button
                size='small'
                onClick={() => window.open('https://doc.58corp.com/sharingan/', '_blank')}
                css={quickCommandsStyles.commandButton}
              >
                平台使用
              </Button>
              <Button
                size='small'
                onClick={() =>
                  window.open('https://docs.58corp.com/#/space/1906554765467209728', '_blank')
                }
                css={quickCommandsStyles.commandButton}
              >
                平台接入
              </Button>
            </div>
          </div>
        </div>

        {/* 左右滑动按钮 */}
        <Button
          id='quickCommandsLeftArrow'
          type='text'
          css={[
            quickCommandsStyles.arrowButton,
            css`
              left: 4px;
              display: none;
            `,
          ]}
          onClick={() => {
            const container = document.getElementById('quickCommandsContainer');
            if (container) {
              container.scrollBy({ left: -220, behavior: 'smooth' });
            }
          }}
        >
          ◀
        </Button>
        <Button
          id='quickCommandsRightArrow'
          type='text'
          css={[
            quickCommandsStyles.arrowButton,
            css`
              right: 4px;
              display: flex;
            `,
          ]}
          onClick={() => {
            const container = document.getElementById('quickCommandsContainer');
            if (container) {
              container.scrollBy({ left: 220, behavior: 'smooth' });
            }
          }}
        >
          ▶
        </Button>
      </div>
    </div>
  );
};

export default QuickCommandsSection;
