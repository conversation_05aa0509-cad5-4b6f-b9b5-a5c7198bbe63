import { useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { Button, List, Pagination, Spin, Tooltip, Typography } from 'antd'; // 导入 Tooltip 组件
import dayjs from 'dayjs';
import React, { FC, useMemo, useState } from 'react'; // 添加 useState

import { ChatHistory, Message } from '@/services/AIAssistantService';

// 添加服务器返回的历史记录数据接口
interface ServerHistoryItem {
  id: string | undefined;
  user_message: string;
  answer: string;
  timestamp?: number;
  module_name: string;
  desc_status: number;
  draw_type: number;
  reply_type: number;
}

// 修改接口，使其接受 ChatHistory[] 或 ServerHistoryItem[]
interface HistorySectionProps {
  loading: boolean;
  historyVisible: boolean;
  chatHistory: ChatHistory[] | ServerHistoryItem[]; // 修改为联合类型
  pagination: {
    current: number;
    size: number;
  };
  totalRecords: number;
  onFetchHistory: (page?: number) => void;
  onClearHistory: () => void;
  onSelectHistory: (history: ChatHistory) => void;
  onPageChange: (page: number) => void;
}

const HistorySection: FC<HistorySectionProps> = ({
  loading,
  historyVisible,
  chatHistory,
  pagination,
  totalRecords,
  onFetchHistory,
  onClearHistory,
  onSelectHistory,
  onPageChange,
}) => {
  const { t } = useTranslation(['components', 'common']);
  // 添加状态来跟踪当前选中的历史记录项
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  // 使用 useMemo 将 ServerHistoryItem[] 转换为 ChatHistory[]
  const normalizedChatHistory = useMemo(() => {
    return chatHistory
      .map((item: any) => {
        if ('user_message' in item) {
          const serverItem = item as ServerHistoryItem;
          // 确保 id 不为 undefined，提供默认值
          const id = serverItem.id?.toString() || 'unknown-id';

          const messages: Message[] = [
            {
              id: `user-${id}`,
              role: 'user',
              content: serverItem.user_message,
              timestamp: serverItem.timestamp || Date.now(),
            },
            {
              id, // 使用已处理的 id
              role: 'assistant',
              content: serverItem.answer,
              timestamp: serverItem.timestamp || Date.now(),
              originalId: id, // 保存原始ID作为备份
            },
          ];

          return {
            id, // 确保 id 始终为 string
            messages,
            createdAt: serverItem.timestamp || Date.now(),
            originalItem: serverItem, // 确保完整保存原始数据
          } as ChatHistory; // 明确类型转换
        } else if (item.originalItem) {
          // 如果已经是ChatHistory类型且有originalItem，确保保留
          return item as ChatHistory;
        } else {
          // 如果是ChatHistory类型但没有originalItem，记录日志
          return item as ChatHistory;
        }
      })
      .filter((item): item is ChatHistory => !!item.id); // 过滤掉无效的项
  }, [chatHistory]);

  // 添加处理函数，将服务器返回的历史记录转换为应用需要的格式
  const handleSelectHistoryItem = (item: ChatHistory) => {
    setSelectedItemId(item.id); // 设置选中项

    // 为历史记录中的消息添加 isFromHistory 标记
    const markedMessages = item.messages.map((msg) => ({
      ...msg,
      isFromHistory: true,
    }));

    // 使用标记过的消息创建新的历史记录对象
    const markedHistory = {
      ...item,
      messages: markedMessages,
    };

    onSelectHistory(markedHistory);
  };

  // 如果历史记录不可见，只显示"显示历史记录"按钮
  if (!historyVisible) {
    return (
      <div
        css={css`
          margin-bottom: 12px;
          text-align: center;
        `}
      >
        <Button type='link' onClick={() => onFetchHistory()}>
          {t('aiAssistant.showHistory', { ns: 'components' })}
        </Button>
      </div>
    );
  }

  // 如果历史记录可见，显示完整的历史记录区域
  return (
    <div
      css={css`
        margin-bottom: 16px;
        border: 1px solid #eaeaea;
        border-radius: 8px;
        padding: 12px;
        background-color: #f9f9f9;
        height: 280px;
        display: flex;
        flex-direction: column;
      `}
    >
      <div
        css={css`
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          flex-shrink: 0;
        `}
      >
        <Typography.Text strong>
          {t('aiAssistant.historyTitle', { ns: 'components' })}
        </Typography.Text>
        <Button type='link' onClick={onClearHistory}>
          {t('aiAssistant.hideHistory', { ns: 'components' })}
        </Button>
      </div>

      {loading ? (
        <div
          css={css`
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
          `}
        >
          <Spin />
        </div>
      ) : (
        <div
          css={css`
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            overflow: hidden;
          `}
        >
          <div
            css={css`
              flex-grow: 1;
              overflow-y: auto;
              margin-bottom: 12px;
            `}
          >
            <List
              dataSource={normalizedChatHistory}
              renderItem={(item: ChatHistory) => {
                // 获取用户消息内容
                const userMessage = item.messages.find((msg) => msg.role === 'user');
                const content = userMessage?.content || '';
                // 判断当前项是否被选中
                const isSelected = selectedItemId === item.id;

                return (
                  <List.Item
                    css={css`
                      cursor: pointer;
                      padding: 8px 12px;
                      border-radius: 4px;
                      height: 40px;
                      overflow: hidden;
                      background-color: ${isSelected ? '#e6f7ff' : 'transparent'};
                      border-left: ${isSelected ? '3px solid #1890ff' : 'none'};
                      padding-left: ${isSelected ? '9px' : '12px'};
                      &:hover {
                        background-color: ${isSelected ? '#e6f7ff' : '#f0f0f0'};
                      }
                    `}
                    onClick={() => handleSelectHistoryItem(item)}
                  >
                    <div
                      css={css`
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                      `}
                    >
                      {/* 使用 Tooltip 包裹文本，当鼠标悬浮时显示完整内容 */}
                      <Tooltip
                        title={content}
                        placement='topLeft'
                        overlayStyle={{ maxWidth: '400px' }}
                        mouseEnterDelay={0.5} // 设置鼠标悬浮 0.5 秒后显示
                      >
                        <Typography.Text ellipsis style={{ maxWidth: '70%' }}>
                          {content.substring(0, 50)}
                          {content.length > 50 ? '...' : ''}
                        </Typography.Text>
                      </Tooltip>
                      <Typography.Text type='secondary' style={{ fontSize: '12px' }}>
                        {dayjs(item.createdAt).format('MM-DD HH:mm')}
                      </Typography.Text>
                    </div>
                  </List.Item>
                );
              }}
            />
          </div>

          {totalRecords > pagination.size && (
            <div
              css={css`
                display: flex;
                justify-content: center;
                flex-shrink: 0;
                height: 32px;
              `}
            >
              <Pagination
                current={pagination.current}
                pageSize={pagination.size}
                total={totalRecords}
                onChange={onPageChange}
                size='small'
                showSizeChanger={false}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default HistorySection;
