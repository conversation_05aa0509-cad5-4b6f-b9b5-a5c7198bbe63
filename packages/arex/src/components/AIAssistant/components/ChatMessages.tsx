import { css } from '@emotion/react';
import React from 'react';

import { Message } from '@/services/AIAssistantService';

interface ChatMessagesProps {
  messages: Message[];
  streamingMessage: string;
  isStreaming: boolean;
}

const ChatMessages: React.FC<ChatMessagesProps> = ({ messages, streamingMessage, isStreaming }) => {
  return (
    <div
      css={css`
        flex: 1;
        overflow-y: auto;
        padding: 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;
      `}
    >
      {messages.map((message) => (
        <div
          key={message.id}
          css={css`
            display: flex;
            flex-direction: column;
            gap: 8px;
            ${message.role === 'assistant'
              ? `
                  background-color: #fff;
                  border-radius: 8px;
                  padding: 12px;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                `
              : ''}
          `}
        >
          <div
            css={css`
              white-space: pre-wrap;
              word-break: break-word;
            `}
          >
            {message.content}
          </div>
        </div>
      ))}
      {isStreaming && (
        <div
          css={css`
            background-color: #fff;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            white-space: pre-wrap;
            word-break: break-word;
          `}
        >
          {streamingMessage}
        </div>
      )}
    </div>
  );
};

export default ChatMessages;