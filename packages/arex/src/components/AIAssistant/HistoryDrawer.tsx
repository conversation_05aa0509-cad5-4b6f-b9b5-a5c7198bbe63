import { useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { Button, Drawer, Empty, List, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { FC } from 'react';

import { ChatHistory } from '@/services/AIAssistantService';

interface HistoryDrawerProps {
  visible: boolean;
  loading: boolean;
  history: ChatHistory[];
  onClose: () => void;
  onFetch: () => void;
  onSelect: (history: ChatHistory) => void;
}

const HistoryDrawer: FC<HistoryDrawerProps> = ({
  visible,
  loading,
  history,
  onClose,
  onFetch,
  onSelect,
}) => {
  const { t } = useTranslation(['components', 'common']);

  return (
    <Drawer
      title={t('aiAssistant.historyTitle', { ns: 'components' })}
      placement="right"
      onClose={onClose}
      open={visible}
      width={320}
    >
      <Button
        type="primary"
        onClick={onFetch}
        loading={loading}
        css={css`
          margin-bottom: 16px;
          width: 100%;
        `}
      >
        {t('aiAssistant.fetchHistory', { ns: 'components' })}
      </Button>

      {loading ? (
        <div
          css={css`
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
          `}
        >
          <Spin />
        </div>
      ) : history.length > 0 ? (
        <List
          dataSource={history}
          renderItem={(item) => (
            <List.Item
              key={item.id}
              onClick={() => onSelect(item)}
              css={css`
                cursor: pointer;
                padding: 12px;
                border-radius: 4px;
                &:hover {
                  background-color: #f5f5f5;
                }
              `}
            >
              <div>
                <Typography.Text strong>
                  {item.messages[0]?.content.slice(0, 20) || '对话记录'}
                  {item.messages[0]?.content.length > 20 ? '...' : ''}
                </Typography.Text>
                <div>
                  <Typography.Text type="secondary">
                    {dayjs(item.createdAt).format('YYYY-MM-DD HH:mm')}
                  </Typography.Text>
                </div>
              </div>
            </List.Item>
          )}
        />
      ) : (
        <Empty description={t('aiAssistant.noHistory', { ns: 'components' })} />
      )}
    </Drawer>
  );
};

export default HistoryDrawer;
