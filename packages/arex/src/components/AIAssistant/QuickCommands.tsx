import { useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { Button, Space, Typography } from 'antd';
import React, { FC } from 'react';

interface QuickCommandsProps {
  onSelect: (command: string) => void;
}

const QuickCommands: FC<QuickCommandsProps> = ({ onSelect }) => {
  const { t } = useTranslation(['components', 'common']);

  // 预设的快捷指令
  const commands = [
    { key: 'help', label: '帮助', command: '你能做什么?' },
    { key: 'api', label: 'API', command: '如何使用AREX的API?' },
    { key: 'test', label: '测试', command: '如何创建测试用例?' },
    { key: 'debug', label: '调试', command: '如何调试接口?' },
  ];

  return (
    <div
      css={css`
        margin-bottom: 16px;
      `}
    >
      <Typography.Text type='secondary' style={{ marginBottom: 8, display: 'block' }}>
        {t('aiAssistant.quickCommands', { ns: 'components' })}
      </Typography.Text>
      <Space wrap>
        {commands.map((cmd) => (
          <Button key={cmd.key} size='small' onClick={() => onSelect(cmd.command)}>
            {cmd.label}
          </Button>
        ))}
      </Space>
    </div>
  );
};

export default QuickCommands;
