import { SendOutlined } from '@ant-design/icons';
import { css } from '@emotion/react';
import { Button, Input } from 'antd';
import React, { FC, KeyboardEvent } from 'react';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: (content: string) => void;
  disabled?: boolean;
}

const ChatInput: FC<ChatInputProps> = ({ value, onChange, onSend, disabled }) => {
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim()) {
        onSend(value);
      }
    }
  };

  return (
    <div
      css={css`
        display: flex;
        margin-top: 12px;
        align-items: flex-end;
      `}
    >
      <Input.TextArea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="请输入问题..."
        autoSize={{ minRows: 1, maxRows: 4 }}
        disabled={disabled}
        css={css`
          flex: 1;
          resize: none;
        `}
      />
      <Button
        type="primary"
        icon={<SendOutlined />}
        onClick={() => onSend(value)}
        disabled={!value.trim() || disabled}
        css={css`
          margin-left: 8px;
        `}
      />
    </div>
  );
};

export default ChatInput;
