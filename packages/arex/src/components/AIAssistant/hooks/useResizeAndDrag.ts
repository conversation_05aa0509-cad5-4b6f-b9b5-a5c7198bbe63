import { useEffect, useRef, useState } from 'react';

interface Position {
  right: number;
  bottom: number;
}

interface ResizeStartPos {
  bottom: number;
  height: number;
}

export const useResizeAndDrag = (initialWidth = 500, initialHeight?: number) => {
  // 使用窗口高度减去上下边距计算初始高度
  const [width, setWidth] = useState(initialWidth);
  const [height, setHeight] = useState(
    initialHeight || (typeof window !== 'undefined' ? window.innerHeight - 20 : 750),
  );
  const [position, setPosition] = useState<Position>({ right: 90, bottom: 10 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [resizeStartPos, setResizeStartPos] = useState<ResizeStartPos>({ bottom: 0, height: 0 });
  const cardHeaderRef = useRef<HTMLDivElement>(null);

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    // 确保只有点击卡片头部才能拖动
    if (!cardHeaderRef.current?.contains(e.target as Node)) return;

    // 阻止默认行为和冒泡
    e.preventDefault();
    e.stopPropagation();

    // 计算鼠标点击位置与窗口右下角的偏移量
    const offsetX = window.innerWidth - e.clientX - position.right;
    const offsetY = window.innerHeight - e.clientY - position.bottom;

    setDragOffset({ x: offsetX, y: offsetY });
    setIsDragging(true);
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    // 根据鼠标当前位置和偏移量计算新位置
    const newRight = window.innerWidth - e.clientX - dragOffset.x;
    const newBottom = window.innerHeight - e.clientY - dragOffset.y;

    // 确保不会移出屏幕（右侧、底部和顶部）
    const newPosition = {
      right: Math.max(0, newRight),
      bottom: Math.max(0, newBottom),
    };

    // 确保不会超过顶部边界
    const topPosition = window.innerHeight - newPosition.bottom - height;
    if (topPosition < 0) {
      newPosition.bottom = window.innerHeight - height;
    }

    // 确保不会超过左侧边界
    const leftPosition = window.innerWidth - newPosition.right - width;
    if (leftPosition < 0) {
      newPosition.right = window.innerWidth - width;
    }

    setPosition(newPosition);
  };

  // 处理鼠标释放事件
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 处理调整大小开始事件
  const handleResizeStart = (e: React.SyntheticEvent, { handle }: { handle: string }) => {
    if (handle === 'sw') {
      setResizeStartPos({
        bottom: position.bottom,
        height: height,
      });
    }
  };

  // 处理调整大小事件
  const handleResize = (
    e: React.SyntheticEvent,
    { size, handle }: { size: { width: number; height: number }; handle: string },
  ) => {
    // 检查调整后的宽度是否会导致超出左侧边界
    const newWidth = Math.min(
      size.width,
      window.innerWidth - (window.innerWidth - position.right - width),
    );
    setWidth(newWidth);

    if (handle === 'sw') {
      // 计算高度差异
      const heightDiff = size.height - resizeStartPos.height;

      // 调整高度
      setHeight(size.height);

      // 调整底部位置，使窗口向下扩展
      setPosition((prev) => ({
        ...prev,
        bottom: Math.max(0, resizeStartPos.bottom - heightDiff),
      }));
    } else {
      setHeight(size.height);
    }
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);

      // 添加鼠标样式
      if (document.body) {
        document.body.style.cursor = 'grabbing';
      }
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);

      // 恢复鼠标样式
      if (document.body) {
        document.body.style.cursor = '';
      }
    };
  }, [isDragging]);

  // 添加窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      // 确保聊天框不会超出屏幕
      const topPosition = window.innerHeight - position.bottom - height;
      const leftPosition = window.innerWidth - position.right - width;

      const newPosition = { ...position };
      let needUpdate = false;

      if (topPosition < 0) {
        // 如果超出顶部，优先调整底部位置
        newPosition.bottom = Math.max(0, window.innerHeight - height);
        needUpdate = true;
      }

      if (leftPosition < 0) {
        // 如果超出左侧，调整右侧位置
        newPosition.right = Math.max(0, window.innerWidth - width);
        needUpdate = true;
      }

      if (needUpdate) {
        setPosition(newPosition);
      }

      // 如果窗口变小，可能需要调整高度和宽度
      if (height > window.innerHeight - 20) {
        setHeight(window.innerHeight - 20);
      }

      if (width > window.innerWidth) {
        setWidth(window.innerWidth);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [height, width, position]);

  return {
    width,
    height,
    position,
    isDragging,
    cardHeaderRef,
    handleMouseDown,
    handleResizeStart,
    handleResize,
  };
};
