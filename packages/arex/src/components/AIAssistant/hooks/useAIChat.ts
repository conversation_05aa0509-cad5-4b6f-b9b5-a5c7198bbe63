import { getLocalStorage, useTranslation } from '@arextest/arex-core';
import { useEffect, useState } from 'react';

import { EMAIL_KEY } from '@/constant';
import { ChatHistory, getAIHistory, Message, sendMessage } from '@/services/AIAssistantService';

import { useAIAssistantContext } from '../context/AIAssistantContext';

// 添加分页参数接口
interface PaginationParams {
  current: number;
  size: number;
}

// 添加服务器返回的记录类型定义
interface ServerHistoryRecord {
  id: number | string;
  user_message: string;
  answer: string;
  timestamp?: number;
  module_name?: string;
  desc_status?: number;
  draw_type?: number;
  reply_type?: number;
  answer_time?: number;
}

// 添加服务器响应类型定义
interface ServerResponse {
  records?: ServerHistoryRecord[];
  histories?: ChatHistory[];
  pages?: number;
  total?: number;
  pagination?: {
    pages: number;
    total: number;
  };
}

export const useAIChat = (visible: boolean) => {
  const { t } = useTranslation(['components', 'common']);
  const {
    messages,
    setMessages,
    currentMessage,
    setCurrentMessage,
    streamingMessage,
    setStreamingMessage,
    isStreaming,
    setIsStreaming,
    historyVisible,
    setHistoryVisible,
    loading,
    setLoading,
    feedbackStatus, // 添加这一行
    setFeedbackStatus, // 添加这一行
    handleSendMessage: contextHandleSendMessage,
  } = useAIAssistantContext();

  // 本地状态只保留那些在Context中没有的
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  // 添加分页状态
  const [pagination, setPagination] = useState<PaginationParams>({
    current: 1,
    size: 4,
  });
  // 添加总页数状态
  const [totalPages, setTotalPages] = useState(0);
  // 添加总记录数状态
  const [totalRecords, setTotalRecords] = useState(0);

  // 初始化欢迎消息
  useEffect(() => {
    if (visible && messages.length === 0) {
      setMessages([
        {
          id: 'welcome',
          role: 'assistant',
          content: t('aiAssistant.welcomeMessage', { ns: 'components' }),
          timestamp: Date.now(),
          isWelcomeMessage: true, // 添加标记，表示这是欢迎消息
        },
      ]);
    }
  }, [visible, t, messages.length, setMessages]);

  // 获取历史记录
  const fetchHistory = async (page?: number) => {
    setLoading(true);
    try {
      // 如果提供了页码，更新分页状态
      if (page !== undefined) {
        setPagination((prev) => ({ ...prev, current: page }));
      }

      const currentPage = page || pagination.current;

      // 构建完整的请求参数
      const requestParams = {
        req: {
          oa: getLocalStorage(EMAIL_KEY) as string,
          service_name: 'wreplay-copilot',
          module_name: '',
        },
        page: {
          current: currentPage,
          size: pagination.size,
        },
      };

      // 发送请求获取历史记录
      const response = await fetch('/ai/ai-assistant/get-ai-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestParams),
      });

      if (!response.ok) {
        throw new Error('获取历史记录失败');
      }

      const data = await response.json();

      // 处理服务器返回的数据
      if (data.success && data.data) {
        const { records = [], total = 0, pages = 0 } = data.data;

        // 转换服务器返回的记录为应用需要的格式
        const formattedHistory = records.map((record: any) => {
          // 确保 id 不为 undefined
          const id = record.id?.toString() || 'unknown-id';

          // 创建消息数组
          const messages = [
            {
              id: `user-${id}`,
              role: 'user' as const,
              content: record.user_message,
              timestamp: record.timestamp || Date.now(),
            },
            {
              id,
              role: 'assistant' as const,
              content: record.answer,
              timestamp: record.timestamp || Date.now(),
              originalId: id,
            },
          ];

          // 返回格式化的历史记录，包含原始数据
          return {
            id,
            messages,
            createdAt: record.timestamp || Date.now(),
            originalItem: {
              id: record.id,
              user_message: record.user_message,
              answer: record.answer,
              timestamp: record.timestamp,
              module_name: record.module_name || '',
              desc_status: record.desc_status || 0,
              draw_type: record.draw_type || 0,
              reply_type: record.reply_type || 0,
            },
          };
        });

        setChatHistory(formattedHistory);
        setTotalPages(pages);
        setTotalRecords(total);
        setHistoryVisible(true);
      }
    } catch (error) {
      console.error('获取历史记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 清除历史记录显示
  const clearHistory = () => {
    // 隐藏历史记录区域
    setHistoryVisible(false);

    // 如果当前没有聊天记录，可以添加一条欢迎消息
    if (messages.length === 0) {
      setMessages([
        {
          id: 'welcome',
          role: 'assistant',
          content: t('aiAssistant.welcomeMessage', { ns: 'components' }),
          timestamp: Date.now(),
        },
      ]);
    }
  };

  // 切换页码
  const handlePageChange = (page: number) => {
    fetchHistory(page);
  };

  // 发送消息
  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content,
      timestamp: Date.now(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setCurrentMessage('');
    setIsStreaming(true);
    setStreamingMessage('');

    try {
      // 构建请求参数 - 修改为新的参数结构
      const params = {
        oa: getLocalStorage(EMAIL_KEY) as string,
        message: content,
        moduleName: '',
        serviceName: 'wreplay-copilot',
        triggerUrl: window.location.href,
        isHidden: false,
      };

      const response = await sendMessage(params);

      if (!response || !response.ok || !response.body) {
        throw new Error('Invalid response');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let completeResponse = ''; // 存储完整的响应文本
      let buffer = ''; // 添加缓冲区处理不完整的数据块
      let responseId = ''; // 用于存储从响应中解析出的ID

      // Replace while(true) with a variable condition
      let isReading = true;
      while (isReading) {
        const { done, value } = await reader.read();

        if (done) {
          isReading = false;
          // 处理缓冲区中可能剩余的数据
          if (buffer.trim()) {
            try {
              // 尝试处理最后可能不完整的数据
              if (buffer.includes('data:')) {
                const content = buffer.substring(buffer.indexOf('data:') + 5).trim();
                if (content) {
                  try {
                    const data = JSON.parse(content);
                    console.log('处理最终缓冲区，解析出data:', data);
                    // 尝试从响应中提取ID
                    if (data?.data?.id && !responseId) {
                      responseId = data.data.id;
                      console.log('从最终缓冲区解析出ID:', responseId);
                    }
                    if (data?.data?.streamResp) {
                      const processedContent = data.data.streamResp.replace(/\\n/g, '\n');
                      completeResponse += processedContent;
                      setStreamingMessage(completeResponse);
                    }
                  } catch (e) {
                    console.warn('解析最终缓冲区JSON失败:', e, 'content:', content);
                  }
                }
              }
            } catch (e) {
              console.warn('处理最终缓冲区失败:', e);
            }
          }
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk; // 将新数据添加到缓冲区

        // 处理完整的数据行
        const lines = buffer.split('\n');
        // 保留最后一行，它可能不完整
        buffer = lines.pop() || '';

        // 处理完整的行
        for (const line of lines) {
          if (line.trim().startsWith('data:')) {
            try {
              // 提取data:后面的内容
              const content = line.substring(line.indexOf('data:') + 5).trim();
              if (content) {
                try {
                  const data = JSON.parse(content);
                  console.log('处理数据行，解析出data:', data);
                  // 尝试从响应中提取ID
                  if (data?.data?.id && !responseId) {
                    responseId = data.data.id;
                    console.log('从数据行解析出ID:', responseId);
                  }
                  if (data?.data?.streamResp) {
                    const processedContent = data.data.streamResp.replace(/\\n/g, '\n');
                    completeResponse += processedContent;
                    setStreamingMessage(completeResponse);
                  }
                } catch (jsonError) {
                  console.warn('JSON解析错误:', jsonError, 'content:', content);
                }
              }
            } catch (lineError) {
              console.warn('处理数据行错误:', lineError, 'line:', line);
            }
          }
        }
      }

      console.log('最终完整响应:', completeResponse);
      console.log('最终解析出的ID:', responseId);

      // 将完整的消息添加到消息列表
      if (completeResponse) {
        const assistantMessage: Message = {
          id: responseId || `assistant-${Date.now()}`, // 使用解析出的ID或生成新ID
          role: 'assistant',
          content: completeResponse,
          timestamp: Date.now(),
          isWelcomeMessage: false, // 明确标记这不是欢迎消息
        };
        console.log('添加助手消息，使用ID:', assistantMessage.id);
        setMessages((prev) => [...prev, assistantMessage]);
      } else {
        console.error('处理后的消息为空');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: t('aiAssistant.errorMessage', { ns: 'components' }),
          timestamp: Date.now(),
        },
      ]);
    } finally {
      setIsStreaming(false);
      setStreamingMessage('');
    }
  };

  // 选择历史对话
  // 修改handleSelectHistory函数，确保Markdown内容正确处理
  const handleSelectHistory = (history: ChatHistory) => {
    // 确保消息内容被正确处理，特别是assistant的回复
    const processedMessages = history.messages.map((msg) => {
      if (msg.role === 'assistant') {
        return {
          ...msg,
          content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content),
          id: msg.originalId || msg.id, // 优先使用originalId
        };
      }
      return msg;
    });

    setMessages(processedMessages);

    // 如果有originalItem，设置反馈状态
    if (history.originalItem && typeof history.originalItem.desc_status !== 'undefined') {
      // 创建一个新的反馈状态对象
      const newFeedbackStatus: Record<string, number> = {};

      // 找到assistant消息并设置其反馈状态
      const assistantMsg = processedMessages.find(msg => msg.role === 'assistant');
      if (assistantMsg) {
        newFeedbackStatus[assistantMsg.id] = history.originalItem.desc_status;
        setFeedbackStatus(newFeedbackStatus);
      }
    } else {
      console.log('警告: 历史记录缺少originalItem或desc_status字段'); // 添加日志
    }
  };

  // 处理快捷指令
  const handleQuickCommand = (command: string) => {
    setCurrentMessage(command);
  };

  // 添加重新生成响应的处理函数
  const handleRegenerateResponse = async (messageId: string) => {
    if (!messageId || isStreaming) return;

    console.log('重新生成响应，使用ID:', messageId);

    // 移除最后一条助手回复
    const filteredMessages = messages.filter(
      (msg) => !(msg.role === 'assistant' && messages.indexOf(msg) === messages.length - 1),
    );

    setMessages(filteredMessages);

    try {
      // 设置加载状态
      setIsStreaming(true);
      setStreamingMessage('');

      // 调用 resend API，使用原始ID
      const response = await fetch(`/ai/ai-assistant/resend?id=${messageId}`);

      if (!response.ok) {
        throw new Error('重新生成响应失败');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取响应流');
      }

      const decoder = new TextDecoder();
      let completeResponse = '';
      let buffer = '';
      let responseId = ''; // 用于存储从响应中解析出的ID

      console.log('开始读取重新生成的响应流...');
      let isReading = true;

      while (isReading) {
        const { done, value } = await reader.read();

        if (done) {
          isReading = false;
          console.log('重新生成的响应流读取完成');

          // 处理缓冲区中可能剩余的数据
          if (buffer.trim()) {
            try {
              // 尝试处理最后可能不完整的数据
              if (buffer.includes('data:')) {
                const content = buffer.substring(buffer.indexOf('data:') + 5).trim();
                if (content) {
                  try {
                    const data = JSON.parse(content);
                    // 尝试从响应中提取ID
                    console.log('处理最终缓冲区，解析出data:', data);
                    if (data?.data?.id && !responseId) {
                      responseId = data.data.id;
                      console.log('从最终缓冲区解析出ID:', responseId);
                    } else if (data?.id && !responseId) {
                      responseId = data.id;
                      console.log('从最终缓冲区解析出顶层ID:', responseId);
                    }
                    if (data?.data?.streamResp) {
                      const processedContent = data.data.streamResp.replace(/\\n/g, '\n');
                      completeResponse += processedContent;
                      setStreamingMessage(completeResponse);
                    }
                  } catch (e) {
                    console.warn('解析最终缓冲区JSON失败:', e, 'content:', content);
                  }
                }
              }
            } catch (e) {
              console.warn('处理最终缓冲区失败:', e);
            }
          }
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk; // 将新数据添加到缓冲区

        // 处理完整的数据行
        const lines = buffer.split('\n');
        // 保留最后一行，它可能不完整
        buffer = lines.pop() || '';

        // 处理完整的行
        for (const line of lines) {
          if (line.trim().startsWith('data:')) {
            try {
              // 提取data:后面的内容
              const content = line.substring(line.indexOf('data:') + 5).trim();
              if (content) {
                try {
                  const data = JSON.parse(content);
                  // 尝试从响应中提取ID
                  console.log('处理数据行，解析出data:', data);
                  if (data?.data?.id && !responseId) {
                    responseId = data.data.id;
                    console.log('从数据行解析出ID:', responseId);
                  } else if (data?.id && !responseId) {
                    responseId = data.id;
                    console.log('从数据行解析出顶层ID:', responseId);
                  }
                  if (data?.data?.streamResp) {
                    const processedContent = data.data.streamResp.replace(/\\n/g, '\n');
                    completeResponse += processedContent;
                    setStreamingMessage(completeResponse);
                  }
                } catch (jsonError) {
                  console.warn('JSON解析错误:', jsonError, 'content:', content);
                }
              }
            } catch (lineError) {
              console.warn('处理数据行错误:', lineError, 'line:', line);
            }
          }
        }
      }

      console.log('最终完整响应:', completeResponse);
      console.log('最终解析出的ID:', responseId);

      // 确保消息不为空再添加到消息列表
      if (completeResponse.trim()) {
        const assistantMessage = {
          // 使用从响应中解析出的ID，如果没有则生成一个新ID
          id: responseId || `assistant-${Date.now()}`,
          role: 'assistant' as const,
          content: completeResponse,
          timestamp: Date.now(),
        };

        console.log('添加重新生成的助手消息，使用ID:', assistantMessage.id);
        setMessages((prev) => [...prev, assistantMessage]);
      } else {
        console.warn('重新生成的响应为空，不添加到消息列表');
      }
    } catch (error) {
      console.error('重新生成响应时出错:', error);
      setMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: t('aiAssistant.errorMessage', {
            ns: 'components',
            defaultValue: '抱歉，重新生成响应时发生了错误，请稍后再试。',
          }),
          timestamp: Date.now(),
        },
      ]);
    } finally {
      setIsStreaming(false);
      setStreamingMessage('');
    }
  };

  return {
    loading,
    messages,
    chatHistory,
    currentMessage,
    streamingMessage,
    isStreaming,
    historyVisible,
    pagination,
    totalPages,
    totalRecords,
    feedbackStatus, // 添加这一行
    setCurrentMessage,
    setMessages,
    setStreamingMessage,
    setIsStreaming,
    fetchHistory,
    clearHistory,
    handleSendMessage,
    handleSelectHistory,
    handleQuickCommand,
    handlePageChange,
    handleRegenerateResponse, // 添加这个函数到返回值中
  };
};
