import { css } from '@emotion/react';

export const containerStyles = {
  wrapper: css`
    position: fixed;
    z-index: 1000;
  `,
  card: css`
    display: flex;
    flex-direction: column;
    background-color: #f7f9fd !important;

    .ant-card-body {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      padding: 12px;
      background-color: #f7f9fd !important;
    }

    .ant-card-head {
      cursor: grab;
      background-color: #f0f4fd !important;
    }
  `,
  dragging: css`
    .ant-card-head {
      cursor: grabbing;
    }
  `,
};

export const quickCommandsStyles = {
  container: css`
    margin-bottom: 12px;
    border: 1px solid #eaeaea;
    border-radius: 16px;
    background-color: #edf2f9;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  `,
  header: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 16px;
    background-color: #edf2f9;
    border-bottom: 1px solid #f0f0f0;
  `,
  headerText: css`
    background-color: #edf2f9;
  `,
  closeButton: css`
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  `,
  scrollContainer: css`
    position: relative;
    padding: 6px 0;
  `,
  commandsContainer: css`
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
    gap: 4px;
    padding: 0 24px;
  `,
  categoryCard: css`
    min-width: 220px;
    flex-shrink: 0;
    padding: 8px 12px;
    background-color: #fff;
    border-radius: 12px;
    margin-right: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
  `,
  categoryHeader: css`
    display: flex;
    align-items: center;
    margin-bottom: 6px;
  `,
  categoryIcon: css`
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    border-radius: 6px;
    font-size: 12px;
  `,
  categoryDescription: css`
    color: #666;
    font-size: 12px;
    margin-bottom: 6px;
  `,
  commandsWrapper: css`
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  `,
  commandButton: css`
    border-radius: 16px;
    background-color: #f5f5f5;
    border: none;
    padding: 0 10px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    &:hover {
      background-color: #e6e6e6;
    }
  `,
  arrowButton: css`
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    z-index: 2;
    border: 1px solid #f0f0f0;
  `,
};

export const historyStyles = {
  container: css`
    text-align: center;
    margin-bottom: 12px;
    color: #666;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
  `,
  toggle: css`
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
  `,
  link: css`
    color: #1890ff;
    cursor: pointer;
  `,
  list: css`
    margin-bottom: 16px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 8px;
  `,
  item: css`
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    &:hover {
      background-color: #f5f5f5;
    }
  `,
};

export const resizeHandleStyles = css`
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: transparent;
  border: none;
  &.react-resizable-handle-se {
    bottom: 0;
    right: 0;
    cursor: se-resize;
    width: 20px;
    height: 20px;
  }
  &.react-resizable-handle-sw {
    bottom: 0;
    left: 0;
    cursor: sw-resize;
    width: 20px;
    height: 20px;
  }
  &.react-resizable-handle-nw {
    top: 0;
    left: 0;
    cursor: nw-resize;
    width: 20px;
    height: 20px;
  }
  &.react-resizable-handle-ne {
    top: 0;
    right: 0;
    cursor: ne-resize;
    width: 20px;
    height: 20px;
  }
  &.react-resizable-handle-n {
    top: 0;
    left: 0;
    right: 0;
    height: 10px;
    width: 100%;
    margin-left: 0;
    cursor: n-resize;
  }
  &.react-resizable-handle-s {
    bottom: 0;
    left: 0;
    right: 0;
    height: 10px;
    width: 100%;
    margin-left: 0;
    cursor: s-resize;
  }
  &.react-resizable-handle-e {
    right: 0;
    top: 0;
    bottom: 0;
    width: 10px;
    height: 100%;
    margin-top: 0;
    cursor: e-resize;
  }
  &.react-resizable-handle-w {
    left: 0;
    top: 0;
    bottom: 0;
    width: 10px;
    height: 100%;
    margin-top: 0;
    cursor: w-resize;
  }
  &::after {
    display: none;
  }
`;