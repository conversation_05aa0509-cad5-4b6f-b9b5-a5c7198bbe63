import { CommentOutlined, OpenAIOutlined } from '@ant-design/icons';
import { useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { FloatButton, Tooltip } from 'antd';
import React, { FC } from 'react';

import { useAIAssistantContext } from './context/AIAssistantContext';

interface FloatingButtonProps {
  onClick?: () => void; // 设为可选
}

const AIFloatingButton: FC<FloatingButtonProps> = ({ onClick }) => {
  const { t } = useTranslation(['components', 'common']);
  const { setVisible } = useAIAssistantContext();

  // 直接使用 context 中的 setVisible，忽略传入的 onClick
  const handleClick = () => {
    setVisible(true);
  };

  return (
    <Tooltip title={t('aiAssistant.openChat', { ns: 'components' })}>
      <FloatButton
        icon={<CommentOutlined />}
        type='primary'
        onClick={handleClick}
        css={css`
          position: fixed;
          right: 24px;
          bottom: 135px;
          z-index: 999;
        `}
      />
    </Tooltip>
  );
};

export default AIFloatingButton;
