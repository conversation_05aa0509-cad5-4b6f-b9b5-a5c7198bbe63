import { CloseOutlined, ShrinkOutlined } from '@ant-design/icons';
import { useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { Button, Card, List, Spin, Typography, Empty } from 'antd';
import dayjs from 'dayjs';
import React, { FC, useEffect, useState, useRef } from 'react';
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css'; // 引入样式文件

import { ChatHistory, Message } from '@/services/AIAssistantService';
import { getAIHistory, sendMessage } from '@/services/AIAssistantService';

import ChatInput from './ChatInput';
import ChatMessages from './ChatMessages';
import QuickCommands from './QuickCommands';

import QuickCommandsSection from './components/QuickCommandsSection';
import HistorySection from './components/HistorySection';
import ResizableCard from './components/ResizableCard';
import { useAIChat } from './hooks/useAIChat';
import { useResizeAndDrag } from './hooks/useResizeAndDrag';

interface AIAssistantProps {
  visible: boolean;
  onClose: () => void;
}

const AIAssistant: FC<AIAssistantProps> = ({ visible, onClose }) => {
  const { t } = useTranslation(['components', 'common']);
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [streamingMessage, setStreamingMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [width, setWidth] = useState(400);
  const [height, setHeight] = useState(700);
  const [quickCommandsVisible, setQuickCommandsVisible] = useState(true);

  // 添加拖动相关状态
  const [position, setPosition] = useState({ right: 20, bottom: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const cardHeaderRef = useRef<HTMLDivElement>(null);
  // Add resize tracking state
  const [resizeStartPos, setResizeStartPos] = useState({ bottom: 0, height: 0 });

  // 初始化欢迎消息
  useEffect(() => {
    if (visible && messages.length === 0) {
      setMessages([
        {
          id: 'welcome',
          role: 'assistant',
          content: t('aiAssistant.welcomeMessage', { ns: 'components' }),
          timestamp: Date.now(),
        },
      ]);
    }
  }, [visible]);

  // 初始化快捷指令箭头显示状态
  useEffect(() => {
    if (visible && quickCommandsVisible) {
      // 延迟执行，确保DOM已经渲染
      setTimeout(() => {
        const container = document.getElementById('quickCommandsContainer');
        const leftArrow = document.getElementById('quickCommandsLeftArrow');
        const rightArrow = document.getElementById('quickCommandsRightArrow');

        if (container && leftArrow && rightArrow) {
          // 初始化时检查是否需要显示右箭头
          if (container.scrollWidth <= container.clientWidth) {
            rightArrow.style.display = 'none';
          } else {
            rightArrow.style.display = 'flex';
          }

          // 初始化时左箭头应该隐藏
          leftArrow.style.display = 'none';
        }
      }, 100);
    }
  }, [visible, quickCommandsVisible]);

  // 获取历史记录
  const fetchHistory = async () => {
    setLoading(true);
    try {
      const history = await getAIHistory();
      setChatHistory(history);
      setHistoryVisible(true); // 设置历史记录为可见
    } catch (error) {
      console.error('Failed to fetch history:', error);
    } finally {
      setLoading(false);
    }
  };

  // 清除历史记录显示
  const clearHistory = () => {
    setChatHistory([]);
    setHistoryVisible(false);
  };

  // 发送消息
  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content,
      timestamp: Date.now(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setCurrentMessage('');
    setIsStreaming(true);
    setStreamingMessage('');

    try {
      const response = await sendMessage(content);

      // 检查 response 是否存在
      if (!response) {
        throw new Error('No response received from server');
      }

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 检查 response.body 是否存在
      if (!response.body) {
        throw new Error('Response body is empty');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      let accumulatedMessage = '';

      while (true) {
        try {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          // 解码当前数据块
          const chunk = decoder.decode(value);
          console.log('Received chunk:', chunk); // 调试日志

          if (chunk) {
            // 解析 SSE 格式数据
            const lines = chunk.split('\n');
            for (const line of lines) {
              if (line.startsWith('data:')) {
                // 只获取 data 字段的内容
                const data = line.slice(5).trim();
                accumulatedMessage += data;
                setStreamingMessage(accumulatedMessage);
              }
            }
          }
        } catch (readError) {
          console.error('Error reading stream:', readError);
          throw readError;
        }
      }

      // 确保消息不为空再添加到消息列表
      if (accumulatedMessage.trim()) {
        const assistantMessage: Message = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: accumulatedMessage,
          timestamp: Date.now(),
        };

        setMessages((prev) => [...prev, assistantMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: t('aiAssistant.errorMessage', { ns: 'components' }),
          timestamp: Date.now(),
        },
      ]);
    } finally {
      setIsStreaming(false);
      setStreamingMessage('');
    }
  };

  // 选择历史对话
  const handleSelectHistory = (history: ChatHistory) => {
    setMessages(history.messages);
    // setShowHistory(false);
  };

  // 处理快捷指令
  const handleQuickCommand = (command: string) => {
    setCurrentMessage(command);
  };

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    // 确保只有点击卡片头部才能拖动
    if (!cardHeaderRef.current?.contains(e.target as Node)) return;

    // 阻止默认行为和冒泡
    e.preventDefault();
    e.stopPropagation();

    // 计算鼠标点击位置与窗口右下角的偏移量
    const offsetX = window.innerWidth - e.clientX - position.right;
    const offsetY = window.innerHeight - e.clientY - position.bottom;

    setDragOffset({ x: offsetX, y: offsetY });
    setIsDragging(true);
  };

  // 修改鼠标移动事件处理
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    // 根据鼠标当前位置和偏移量计算新位置
    const newRight = window.innerWidth - e.clientX - dragOffset.x;
    const newBottom = window.innerHeight - e.clientY - dragOffset.y;

    // 确保不会移出屏幕
    setPosition({
      right: Math.max(0, newRight),
      bottom: Math.max(0, newBottom),
    });
  };

  // 处理鼠标释放事件
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (visible && isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);

      // 添加鼠标样式
      if (document.body) {
        document.body.style.cursor = 'grabbing';
      }
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);

      // 恢复鼠标样式
      if (document.body) {
        document.body.style.cursor = '';
      }
    };
  }, [visible, isDragging]);

  if (!visible) return null;
  // Add resize start handler
  const handleResizeStart = (e: React.SyntheticEvent, { handle }: { handle: string }) => {
    if (handle === 'sw') {
      setResizeStartPos({
        bottom: position.bottom,
        height: height,
      });
    }
  };

  // Add resize handler
  const handleResize = (
    e: React.SyntheticEvent,
    { size, handle }: { size: { width: number; height: number }; handle: string },
  ) => {
    setWidth(size.width);

    if (handle === 'sw') {
      // Calculate height difference
      const heightDiff = size.height - resizeStartPos.height;

      // Adjust height
      setHeight(size.height);

      // Adjust bottom position to make window expand downward
      setPosition((prev) => ({
        ...prev,
        bottom: Math.max(0, resizeStartPos.bottom - heightDiff),
      }));
    } else {
      setHeight(size.height);
    }
  };
  return (
    <div
      css={css`
        position: fixed;
        right: ${position.right}px;
        bottom: ${position.bottom}px;
        z-index: 1000;
      `}
    >
      <Resizable
        width={width}
        height={height}
        minConstraints={[300, 400]}
        maxConstraints={[800, window.innerHeight - 50]}
        onResizeStart={handleResizeStart}
        onResize={handleResize}
        resizeHandles={['nw', 'sw']}
        handle={(h, ref) => {
          const handleClass = `react-resizable-handle react-resizable-handle-${h}`;
          return (
            <div
              className={handleClass}
              ref={ref}
              css={css`
                position: absolute;
                width: 10px;
                height: 10px;
                background-color: transparent;
                border: none;
                &.react-resizable-handle-se {
                  bottom: 0;
                  right: 0;
                  cursor: se-resize;
                  width: 20px;
                  height: 20px;
                }
                &.react-resizable-handle-sw {
                  bottom: 0;
                  left: 0;
                  cursor: sw-resize;
                  width: 20px;
                  height: 20px;
                }
                &.react-resizable-handle-nw {
                  top: 0;
                  left: 0;
                  cursor: nw-resize;
                  width: 20px;
                  height: 20px;
                }
                &.react-resizable-handle-ne {
                  top: 0;
                  right: 0;
                  cursor: ne-resize;
                  width: 20px;
                  height: 20px;
                }
                &.react-resizable-handle-n {
                  top: 0;
                  left: 0;
                  right: 0;
                  height: 10px;
                  width: 100%;
                  margin-left: 0;
                  cursor: n-resize;
                }
                &.react-resizable-handle-s {
                  bottom: 0;
                  left: 0;
                  right: 0;
                  height: 10px;
                  width: 100%;
                  margin-left: 0;
                  cursor: s-resize;
                }
                &.react-resizable-handle-e {
                  right: 0;
                  top: 0;
                  bottom: 0;
                  width: 10px;
                  height: 100%;
                  margin-top: 0;
                  cursor: e-resize;
                }
                &.react-resizable-handle-w {
                  left: 0;
                  top: 0;
                  bottom: 0;
                  width: 10px;
                  height: 100%;
                  margin-top: 0;
                  cursor: w-resize;
                }
                &::after {
                  display: none;
                }
              `}
            />
          );
        }}
      >
        <Card
          title={
            <div
              ref={cardHeaderRef}
              onMouseDown={handleMouseDown}
              css={css`
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                cursor: grab;
                user-select: none;
              `}
            >
              <span>{t('aiAssistant.title', { ns: 'components' })}</span>
              {!quickCommandsVisible && (
                <Button
                  type='text'
                  icon={
                    <span
                      role='img'
                      aria-label='magic'
                      css={css`
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        color: #1890ff;
                        font-size: 16px;
                      `}
                    >
                      ✨
                    </span>
                  }
                  onClick={(e) => {
                    e.stopPropagation(); // 阻止冒泡，避免触发拖动
                    setQuickCommandsVisible(true);
                  }}
                  css={css`
                    padding: 0;
                  `}
                />
              )}
            </div>
          }
          extra={
            <Button
              type='text'
              icon={
                <span
                  role='img'
                  aria-label='close'
                  css={css`
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                  `}
                >
                  <ShrinkOutlined />
                </span>
              }
              onClick={(e) => {
                e.stopPropagation(); // 阻止冒泡，避免触发拖动
                onClose();
              }}
            />
          }
          css={css`
            width: ${width}px;
            height: ${height}px;
            display: flex;
            flex-direction: column;
            background-color: #f7f9fd !important;

            .ant-card-body {
              flex: 1;
              overflow: hidden;
              display: flex;
              flex-direction: column;
              padding: 12px;
              background-color: #f7f9fd !important;
            }

            .ant-card-head {
              cursor: grab;
              background-color: #f0f4fd !important;
            }

            ${isDragging &&
            `
              .ant-card-head {
                cursor: grabbing;
              }
            `}
          `}
        >
          {/* 快捷指令区域 - 根据状态显示或隐藏 */}
          {quickCommandsVisible && (
            <div
              css={css`
                margin-bottom: 12px; /* 减小底部间距 */
                border: 1px solid #eaeaea;
                border-radius: 16px;
                background-color: #edf2f9;
                position: relative;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
              `}
            >
              <div
                css={css`
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 4px 16px; /* 进一步减小上下内边距 */
                  background-color: #edf2f9;
                  border-bottom: 1px solid #f0f0f0;
                `}
              >
                <Typography.Text strong css={css`background-color: #edf2f9;`}>快捷指令</Typography.Text>
                <Button
                  type='text'
                  size='small'
                  icon={<CloseOutlined />}
                  onClick={() => setQuickCommandsVisible(false)}
                  css={css`
                    &:hover {
                      background-color: rgba(0, 0, 0, 0.05);
                    }
                  `}
                />
              </div>

              {/* 滑动区域容器 */}
              <div
                css={css`
                  position: relative;
                  padding: 6px 0; /* 进一步减小上下内边距 */
                `}
              >
                <div
                  id='quickCommandsContainer'
                  css={css`
                    display: flex;
                    overflow-x: auto;
                    scrollbar-width: none;
                    &::-webkit-scrollbar {
                      display: none;
                    }
                    gap: 4px; // 从 12px 减小到 8px
                    padding: 0 24px;
                  `}
                  onScroll={(e) => {
                    // 获取滚动容器
                    const container = e.currentTarget;
                    // 获取左右箭头按钮
                    const leftArrow = document.getElementById('quickCommandsLeftArrow');
                    const rightArrow = document.getElementById('quickCommandsRightArrow');

                    // 判断是否滚动到最左边
                    if (container.scrollLeft <= 0 && leftArrow) {
                      leftArrow.style.display = 'none';
                    } else if (leftArrow) {
                      leftArrow.style.display = 'flex';
                    }

                    // 判断是否滚动到最右边
                    if (
                      container.scrollLeft + container.clientWidth >= container.scrollWidth - 5 &&
                      rightArrow
                    ) {
                      rightArrow.style.display = 'none';
                    } else if (rightArrow) {
                      rightArrow.style.display = 'flex';
                    }
                  }}
                >
                  {/* 常见问题分类 */}
                  <div
                    css={css`
                      min-width: 220px;
                      flex-shrink: 0;
                      padding: 8px 12px;
                      background-color: #fff;
                      border-radius: 12px;
                      margin-right: 4px; // 从 12px 减小到 8px
                      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                      border: 1px solid #f0f0f0;
                    `}
                  >
                    <div
                      css={css`
                        display: flex;
                        align-items: center;
                        margin-bottom: 6px; /* 进一步减小下边距 */
                      `}
                    >
                      <div
                        css={css`
                          width: 20px; /* 减小图标尺寸 */
                          height: 20px; /* 减小图标尺寸 */
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          margin-right: 8px;
                          color: #1890ff;
                          background-color: #e6f7ff;
                          border-radius: 6px;
                          font-size: 12px; /* 减小字体大小 */
                        `}
                      >
                        ?
                      </div>
                      <Typography.Text strong>常见问题</Typography.Text>
                    </div>
                    <div
                      css={css`
                        color: #666;
                        font-size: 12px; /* 减小字体大小 */
                        margin-bottom: 6px; /* 进一步减小下边距 */
                      `}
                    >
                      平台的常见问题
                    </div>
                    <div
                      css={css`
                        display: flex;
                        flex-wrap: wrap;
                        gap: 6px; /* 减小按钮间距 */
                      `}
                    >
                      <Button
                        size='small'
                        onClick={() => handleQuickCommand('权限问题')}
                        css={css`
                          border-radius: 16px;
                          background-color: #f5f5f5;
                          border: none;
                          padding: 0 10px; /* 减小按钮内边距 */
                          height: 24px; /* 减小按钮高度 */
                          line-height: 24px;
                          font-size: 12px; /* 减小字体大小 */
                          &:hover {
                            background-color: #e6e6e6;
                          }
                        `}
                      >
                        权限问题
                      </Button>
                      <Button
                        size='small'
                        onClick={() => handleQuickCommand('SDK问题')}
                        css={css`
                          border-radius: 16px;
                          background-color: #f5f5f5;
                          border: none;
                          padding: 0 10px; /* 减小按钮内边距 */
                          height: 24px; /* 减小按钮高度 */
                          line-height: 24px;
                          font-size: 12px; /* 减小字体大小 */
                          &:hover {
                            background-color: #e6e6e6;
                          }
                        `}
                      >
                        SDK问题
                      </Button>
                      <Button
                        size='small'
                        onClick={() => handleQuickCommand('数据问题')}
                        css={css`
                          border-radius: 16px;
                          background-color: #f5f5f5;
                          border: none;
                          padding: 0 10px; /* 减小按钮内边距 */
                          height: 24px; /* 减小按钮高度 */
                          line-height: 24px;
                          font-size: 12px; /* 减小字体大小 */
                          &:hover {
                            background-color: #e6e6e6;
                          }
                        `}
                      >
                        数据问题
                      </Button>
                    </div>
                  </div>

                  {/* 帮助文档分类 - 应用相同的样式调整 */}
                  <div
                    css={css`
                      min-width: 220px;
                      flex-shrink: 0;
                      padding: 8px 12px;
                      background-color: #fff;
                      border-radius: 12px;
                      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                      border: 1px solid #f0f0f0;
                    `}
                  >
                    <div
                      css={css`
                        display: flex;
                        align-items: center;
                        margin-bottom: 6px; /* 进一步减小下边距 */
                      `}
                    >
                      <div
                        css={css`
                          width: 20px; /* 减小图标尺寸 */
                          height: 20px; /* 减小图标尺寸 */
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          margin-right: 8px;
                          color: #fa8c16;
                          background-color: #fff7e6;
                          border-radius: 6px;
                          font-size: 12px; /* 减小字体大小 */
                        `}
                      >
                        📚
                      </div>
                      <Typography.Text strong>帮助文档</Typography.Text>
                    </div>
                    <div
                      css={css`
                        color: #666;
                        font-size: 12px; /* 减小字体大小 */
                        margin-bottom: 6px; /* 进一步减小下边距 */
                      `}
                    >
                      平台相关操作手册
                    </div>
                    <div
                      css={css`
                        display: flex;
                        flex-wrap: wrap;
                        gap: 6px; /* 减小按钮间距 */
                      `}
                    >
                      <Button
                        size='small'
                        onClick={() => handleQuickCommand('平台使用')}
                        css={css`
                          border-radius: 16px;
                          background-color: #f5f5f5;
                          border: none;
                          padding: 0 10px; /* 减小按钮内边距 */
                          height: 24px; /* 减小按钮高度 */
                          line-height: 24px;
                          font-size: 12px; /* 减小字体大小 */
                          &:hover {
                            background-color: #e6e6e6;
                          }
                        `}
                      >
                        平台使用
                      </Button>
                      <Button
                        size='small'
                        onClick={() => handleQuickCommand('SDK指南')}
                        css={css`
                          border-radius: 16px;
                          background-color: #f5f5f5;
                          border: none;
                          padding: 0 10px; /* 减小按钮内边距 */
                          height: 24px; /* 减小按钮高度 */
                          line-height: 24px;
                          font-size: 12px; /* 减小字体大小 */
                          &:hover {
                            background-color: #e6e6e6;
                          }
                        `}
                      >
                        SDK指南
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 左右滑动按钮 - 添加点击功能和ID */}
                <Button
                  id='quickCommandsLeftArrow'
                  type='text'
                  css={css`
                    position: absolute;
                    left: 4px;
                    top: 50%;
                    transform: translateY(-50%);
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    display: none; /* 初始隐藏左箭头 */
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                    z-index: 2;
                    border: 1px solid #f0f0f0;
                  `}
                  onClick={() => {
                    const container = document.getElementById('quickCommandsContainer');
                    if (container) {
                      container.scrollBy({ left: -220, behavior: 'smooth' });
                    }
                  }}
                >
                  ◀
                </Button>
                <Button
                  id='quickCommandsRightArrow'
                  type='text'
                  css={css`
                    position: absolute;
                    right: 4px;
                    top: 50%;
                    transform: translateY(-50%);
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    display: flex; /* 初始显示右箭头 */
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                    z-index: 2;
                    border: 1px solid #f0f0f0;
                  `}
                  onClick={() => {
                    const container = document.getElementById('quickCommandsContainer');
                    if (container) {
                      container.scrollBy({ left: 220, behavior: 'smooth' });
                    }
                  }}
                >
                  ▶
                </Button>
              </div>
            </div>
          )}

          {/* 历史对话记录按钮 - 调整为居中显示的样式 */}
          <div
            css={css`
              text-align: center;
              margin-bottom: 12px;
              color: #666;
              font-size: 14px;
              display: flex;
              justify-content: center;
              align-items: center;
            `}
          >
            {!historyVisible ? (
              <div
                css={css`
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  gap: 4px;
                `}
              >
                <span>历史对话记录 </span>
                <span
                  onClick={fetchHistory}
                  css={css`
                    color: #1890ff;
                    cursor: pointer;
                  `}
                >
                  {loading ? <Spin size='small' /> : '点击获取'}
                </span>
              </div>
            ) : (
              <div
                onClick={clearHistory}
                css={css`
                  cursor: pointer;
                  color: #1890ff;
                `}
              >
                清除历史记录
              </div>
            )}
          </div>

          {loading && !historyVisible && (
            <div
              css={css`
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 16px 0;
              `}
            >
              <Spin />
            </div>
          )}

          {!loading && historyVisible && chatHistory.length > 0 && (
            <div
              css={css`
                margin-bottom: 16px;
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid #f0f0f0;
                border-radius: 4px;
                padding: 8px;
              `}
            >
              <List
                size='small'
                dataSource={chatHistory}
                renderItem={(item) => (
                  <List.Item
                    key={item.id}
                    onClick={() => handleSelectHistory(item)}
                    css={css`
                      cursor: pointer;
                      padding: 8px;
                      border-radius: 4px;
                      &:hover {
                        background-color: #f5f5f5;
                      }
                    `}
                  >
                    <div>
                      <Typography.Text strong>
                        {item.messages[0]?.content.slice(0, 20) || '对话记录'}
                        {item.messages[0]?.content.length > 20 ? '...' : ''}
                      </Typography.Text>
                      <div>
                        <Typography.Text type='secondary' style={{ fontSize: '12px' }}>
                          {dayjs(item.createdAt).format('YYYY-MM-DD HH:mm')}
                        </Typography.Text>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </div>
          )}

          <ChatMessages
            messages={messages}
            streamingMessage={streamingMessage}
            isStreaming={isStreaming}
          />

          <ChatInput
            value={currentMessage}
            onChange={setCurrentMessage}
            onSend={handleSendMessage}
            disabled={isStreaming}
          />
        </Card>
      </Resizable>
    </div>
  );
};

export default AIAssistant;
