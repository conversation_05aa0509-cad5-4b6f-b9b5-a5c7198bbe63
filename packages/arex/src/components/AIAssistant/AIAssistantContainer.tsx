import React, { FC } from 'react';

import { useAIAssistantContext } from './context/AIAssistantContext';
import AIFloatingButton from './FloatingButton';
import AIAssistant from './index';

const AIAssistantContainer: FC = () => {
  const { visible, setVisible } = useAIAssistantContext();

  return (
    <>
      <AIFloatingButton />
      <AIAssistant visible={visible} onClose={() => setVisible(false)} />
    </>
  );
};

export default AIAssistantContainer;
