import 'react-resizable/css/styles.css';
import './styles/markdown.css';

import React, { FC, useState } from 'react';

import ChatInput from './ChatInput';
import ChatMessages from './ChatMessages';
import HistorySection from './components/HistorySection';
import QuickCommandsSection from './components/QuickCommandsSection';
import ResizableCard from './components/ResizableCard';
import { useAIChat } from './hooks/useAIChat';
import { useResizeAndDrag } from './hooks/useResizeAndDrag';

interface AIAssistantProps {
  visible: boolean;
  onClose: () => void;
}

const AIAssistant: FC<AIAssistantProps> = ({ visible, onClose }) => {
  const [quickCommandsVisible, setQuickCommandsVisible] = useState(true);

  const {
    loading,
    messages,
    chatHistory,
    currentMessage,
    streamingMessage,
    isStreaming,
    historyVisible,
    pagination,
    totalPages,
    totalRecords,
    feedbackStatus, // 添加这一行
    setCurrentMessage,
    setMessages,
    setStreamingMessage, // 添加这一行
    setIsStreaming, // 添加这一行
    fetchHistory,
    clearHistory,
    handleSendMessage,
    handleSelectHistory,
    handleQuickCommand,
    handlePageChange,
    handleRegenerateResponse, // 添加这一行
  } = useAIChat(visible);

  const {
    width,
    height,
    position,
    isDragging,
    cardHeaderRef,
    handleMouseDown,
    handleResizeStart,
    handleResize,
  } = useResizeAndDrag();

  if (!visible) return null;

  // 添加重新生成响应的处理函数
  // 删除或注释掉原来的 handleRegenerateResponse 函数
  // const handleRegenerateResponse = async (messageId: string) => { ... }

  return (
    <ResizableCard
      title={
        <>
          <span>AI 助手</span>
          {!quickCommandsVisible && (
            <button
              type='button'
              aria-label='magic'
              onClick={(e) => {
                e.stopPropagation();
                setQuickCommandsVisible(true);
              }}
              style={{
                cursor: 'pointer',
                color: '#1890ff',
                fontSize: '16px',
                marginLeft: '8px',
                background: 'none',
                border: 'none',
                padding: 0,
                display: 'inline-flex',
                alignItems: 'center',
                transform: 'translateY(-2px)', // 向上移动2个像素
              }}
            >
              ✨
            </button>
          )}
        </>
      }
      width={width}
      height={height}
      position={position}
      isDragging={isDragging}
      cardHeaderRef={cardHeaderRef}
      onMouseDown={handleMouseDown}
      onClose={onClose}
      onResizeStart={handleResizeStart}
      onResize={handleResize}
    >
      <QuickCommandsSection
        visible={quickCommandsVisible}
        onClose={() => setQuickCommandsVisible(false)}
        onCommand={handleQuickCommand}
      />

      <HistorySection
        loading={loading}
        historyVisible={historyVisible}
        chatHistory={chatHistory}
        pagination={pagination}
        totalRecords={totalRecords}
        onFetchHistory={fetchHistory}
        onClearHistory={clearHistory}
        onSelectHistory={handleSelectHistory}
        onPageChange={handlePageChange}
      />

      <ChatMessages
        messages={messages}
        streamingMessage={streamingMessage}
        isStreaming={isStreaming}
        onRegenerateResponse={handleRegenerateResponse}
        feedbackStatus={feedbackStatus} // 添加这一行
      />

      <ChatInput
        value={currentMessage}
        onChange={setCurrentMessage}
        onSend={handleSendMessage}
        disabled={isStreaming}
      />
    </ResizableCard>
  );
};

export default AIAssistant;
