import {
  CopyOutlined,
  DislikeOutlined,
  DislikeTwoTone,
  LikeOutlined,
  LikeTwoTone,
  ReloadOutlined,
} from '@ant-design/icons'; // 添加复制图标
import { getLocalStorage } from '@arextest/arex-core';
import { message } from 'antd';
import React, { FC, useEffect, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

import { EMAIL_KEY } from '@/constant';
import { Message } from '@/services/AIAssistantService';

interface ChatMessagesProps {
  messages: Message[];
  streamingMessage: string;
  isStreaming: boolean;
  onRegenerateResponse: (messageId: string) => void;
  feedbackStatus?: Record<string, number>;
}

const ChatMessages: FC<ChatMessagesProps> = ({
  messages,
  streamingMessage,
  isStreaming,
  onRegenerateResponse,
  feedbackStatus = {},
}) => {
  // 添加内部状态来管理反馈
  const [internalFeedbackStatus, setInternalFeedbackStatus] = useState<Record<string, number>>({});

  // 合并外部和内部的反馈状态，修改合并顺序，让内部状态优先级更高
  const combinedFeedbackStatus = { ...feedbackStatus, ...internalFeedbackStatus };
  
  // 修改handleFeedback函数，使用内部状态
  const handleFeedback = async (messageId: string, feedback: number) => {
    // 先立即更新内部状态，确保UI立即响应
    setInternalFeedbackStatus((prev) => ({
      ...prev,
      [messageId]: feedback, // 更新当前消息的反馈状态
    }));
    
    try {
      const response = await fetch('/ai/ai-assistant/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: messageId,
          feedback,
          oa: getLocalStorage(EMAIL_KEY),
        }),
      });

      if (!response.ok) {
        throw new Error('反馈提交失败');
      }
      message.success('反馈提交成功');
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error('反馈提交失败');
      // 请求失败时回滚状态
      setInternalFeedbackStatus((prev) => {
        const newState = { ...prev };
        delete newState[messageId];
        return newState;
      });
    }
  };

  // 添加引用以访问消息容器
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 添加自动滚动效果
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 当消息更新或流式消息更新时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages, streamingMessage]);

  // 添加复制到剪贴板的函数
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        console.log('内容已复制到剪贴板');
        message.success('复制成功'); // 添加成功提示
      },
      (err) => {
        console.error('复制失败: ', err);
        message.error('复制失败'); // 添加失败提示
      },
    );
  };

  return (
    <div className='chat-messages' style={{ flex: 1, overflowY: 'auto', padding: '10px' }}>
      <style>
        {`
          @keyframes spin {
            to { transform: rotate(360deg); }
          }
          .loading-spinner {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #346ee7;
            animation: spin 1s linear infinite;
            margin-left: 8px;
            vertical-align: middle;
          }
          .regenerate-button {
            display: flex;
            align-items: center;
            margin-top: 30px; /* 增加上边距，让按钮往下移动 */
            padding: 6px 0; /* 移除左右内边距，使按钮与文本对齐 */
            background-color: #f5f5f5;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            transition: all 0.3s;
          }
          .regenerate-button:hover {
            color: #346ee7;
          }
          .regenerate-icon {
            display: inline-block;
            margin-right: 6px;
          }
          .feedback-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
          }
          .feedback-buttons {
            display: flex;
            gap: 1px;
          }
          .feedback-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 4px;
            margin-top: 30px;
            background-color: #f5f5f5;
            cursor: pointer;
            transition: all 0.3s;
            padding: 6px 0;
          }
          .feedback-button:hover {
            color: #346ee7;
          }
        `}
      </style>

      {messages.map((message, index) => (
        <div
          key={message.id}
          className={`message ${message.role}`}
          style={{
            marginBottom: '10px',
            padding: '12px 16px',
            borderRadius: '8px',
            backgroundColor: message.role === 'user' ? '#346ee7' : '#f5f5f5',
            color: message.role === 'user' ? 'white' : 'inherit',
            alignSelf: message.role === 'user' ? 'flex-end' : 'flex-start',
            maxWidth: '80%',
            float: message.role === 'user' ? 'right' : 'left',
            clear: 'both',
          }}
        >
          <div className='message-content'>
            {message.role === 'assistant' ? (
              <div className='markdown-content'>
                <ReactMarkdown rehypePlugins={[rehypeRaw]} remarkPlugins={[remarkGfm]}>
                  {message.content}
                </ReactMarkdown>

                {/* 修改条件：添加 !message.isFromHistory 判断 */}
                {index === messages.length - 1 &&
                  message.role === 'assistant' &&
                  !isStreaming &&
                  !message.isWelcomeMessage &&
                  !message.isFromHistory && (
                    <div className='feedback-container'>
                      <button
                        className='regenerate-button'
                        onClick={() => onRegenerateResponse(message.id)}
                      >
                        <span className='regenerate-icon'>
                          <ReloadOutlined />
                        </span>
                        重新生成
                      </button>
                      <div className='feedback-buttons'>
                        <button
                          className='feedback-button'
                          onClick={() => handleFeedback(message.id, 1)}
                        >
                          {combinedFeedbackStatus[message.id] === 1 ? (
                            <LikeTwoTone />
                          ) : (
                            <LikeOutlined />
                          )}
                        </button>
                        <button
                          className='feedback-button'
                          onClick={() => handleFeedback(message.id, -1)}
                        >
                          {combinedFeedbackStatus[message.id] === -1 ? (
                            <DislikeTwoTone />
                          ) : (
                            <DislikeOutlined />
                          )}
                        </button>
                        <button
                          className='feedback-button'
                          onClick={() => copyToClipboard(message.content)}
                          title='复制回答内容'
                        >
                          <CopyOutlined />
                        </button>
                      </div>
                    </div>
                  )}

                {/* 为历史记录消息只显示反馈按钮和复制按钮，不显示重新生成按钮 */}
                {index === messages.length - 1 &&
                  message.role === 'assistant' &&
                  !isStreaming &&
                  !message.isWelcomeMessage &&
                  message.isFromHistory && (
                    <div className='feedback-container'>
                      <div style={{ flex: 1 }}></div> {/* 占位元素，保持布局一致 */}
                      <div className='feedback-buttons'>
                        <button
                          className='feedback-button'
                          onClick={() => handleFeedback(message.id, 1)}
                        >
                          {combinedFeedbackStatus[message.id] === 1 ? (
                            <LikeTwoTone />
                          ) : (
                            <LikeOutlined />
                          )}
                        </button>
                        <button
                          className='feedback-button'
                          onClick={() => handleFeedback(message.id, -1)}
                        >
                          {combinedFeedbackStatus[message.id] === -1 ? (
                            <DislikeTwoTone />
                          ) : (
                            <DislikeOutlined />
                          )}
                        </button>
                        <button
                          className='feedback-button'
                          onClick={() => copyToClipboard(message.content)}
                          title='复制回答内容'
                        >
                          <CopyOutlined />
                        </button>
                      </div>
                    </div>
                  )}
              </div>
            ) : (
              message.content
            )}
          </div>
        </div>
      ))}

      {isStreaming && (
        <div
          className='message assistant'
          style={{
            marginBottom: '10px',
            padding: '12px 16px',
            borderRadius: '8px',
            backgroundColor: '#f5f5f5',
            maxWidth: '80%',
            float: 'left',
            clear: 'both',
          }}
        >
          <div className='message-content'>
            {streamingMessage ? (
              <div className='markdown-content'>
                <ReactMarkdown rehypePlugins={[rehypeRaw]} remarkPlugins={[remarkGfm]}>
                  {streamingMessage}
                </ReactMarkdown>
              </div>
            ) : (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                正在思考中<div className='loading-spinner'></div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 添加一个空的div作为滚动目标 */}
      <div ref={messagesEndRef} style={{ clear: 'both' }}></div>
    </div>
  );
};

export default ChatMessages;
