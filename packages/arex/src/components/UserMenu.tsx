import { clearLocalStorage, getLocalStorage, useTranslation } from '@arextest/arex-core';
import { Avatar, Badge, Dropdown, DropdownProps, Space } from 'antd';
import React, { FC, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import {
  ACCESS_TOKEN_KEY,
  EMAIL_KEY,
  PanesType,
  REFRESH_TOKEN_KEY,
  WORKSPACE_STORAGE_KEY,
} from '@/constant';
import { useNavPane } from '@/hooks';
import { REDIRECT_58_PASSPORT, REDIRECT_LOGOUT } from '@/services/LoginService/constants';
import { useMessageQueue, useUserProfile } from '@/store';

import globalStoreReset from '../utils/globalStoreReset';
import Icon from './Icon';

const UserMenu: FC = () => {
  const { avatar } = useUserProfile();
  const { messageQueue } = useMessageQueue();
  const { t } = useTranslation();
  const email = getLocalStorage(EMAIL_KEY) as string;
  const navPane = useNavPane();
  const nav = useNavigate();

  const handleLogout = () => {
    globalStoreReset();
    //nav('/login');
    //Logout--> 58 sso登录退出
    // localStorage.clear();
    clearLocalStorage(EMAIL_KEY);
    clearLocalStorage(ACCESS_TOKEN_KEY);
    clearLocalStorage(REFRESH_TOKEN_KEY);
    clearLocalStorage(WORKSPACE_STORAGE_KEY);
    clearLocalStorage(getLocalStorage<string>(EMAIL_KEY));
    clearLocalStorage(getLocalStorage<string>(ACCESS_TOKEN_KEY));
    clearLocalStorage(getLocalStorage<string>(REFRESH_TOKEN_KEY));
    clearLocalStorage(getLocalStorage<string>(WORKSPACE_STORAGE_KEY));

    window.location.href = REDIRECT_LOGOUT;
  };

  const userMenu: DropdownProps['menu'] = useMemo(
    () => ({
      items: [
        {
          key: 'setting',
          label: (
            <Badge dot={!!messageQueue.length} offset={[3, 0]}>
              {t('setting')}
            </Badge>
          ),
          icon: <Icon name='Settings' />,
        },
        // {
        //   key: 'logout',
        //   label: t('logout'),
        //   icon: <LogoutOutlined />,
        // },
      ],
      onClick: (e) => {
        if (e.key === 'logout') handleLogout();
        else if (e.key === 'setting') {
          navPane({
            id: 'setting',
            type: PanesType.SYSTEM_SETTING,
            name: false,
          });
        }
      },
    }),
    [t, messageQueue],
  );

  return (
    <Space size='small'>
      <Dropdown menu={userMenu} trigger={['click']}>
        <Badge dot={!!messageQueue.length} offset={[-3, 3]}>
          <Avatar src={avatar} size={24} style={{ marginLeft: '0px', cursor: 'pointer' }}>
            {email?.slice(0, 1).toUpperCase() || 'G'}
          </Avatar>
        </Badge>
      </Dropdown>
    </Space>
  );
};

export default UserMenu;
