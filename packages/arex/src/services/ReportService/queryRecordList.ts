import { request } from '@/utils';

export interface QueryRecordListReq {
  appId: string;
  operationType: string;
  pageSize: number;
  pageIndex: number;
  operationName: string;
}

export interface QueryRecordListFilterReq {
  appId: string;
  operationType: string;
  pageSize: number;
  pageIndex: number;
  operationName: string;
  beginTime?: string;
  endTime?: string;
  ips?: Set<string>;
  responseType?: string;
  env?: string;
  recordVersion?: string;
  recordId?: string;
  compareValue?: string;
  filterRuleEnum?: number;
  filters?: Set<string>;
  colorType?: number;
}

export type RecordType = {
  createTime: number;
  operationType: string;
  recordId: string;
};
export interface QueryRecordListRes {
  totalCount: number;
  recordList: RecordType[];
}

export async function queryRecordList(params: QueryRecordListReq) {
  return request.post<QueryRecordListRes>('/webApi/report/listRecord', params).then((res) =>
    Promise.resolve({
      total: res.body.totalCount,
      list: res.body.recordList,
    }),
  );
}

export async function queryRecordListSimple(params: QueryRecordListReq) {
  return request.post<QueryRecordListRes>('/webApi/report/listRecordSimple', params).then((res) =>
    Promise.resolve({
      total: res.body.totalCount,
      list: res.body.recordList,
    }),
  );
}

export async function queryRecordListFilter(params: QueryRecordListFilterReq) {
  return request.post<QueryRecordListRes>('/webApi/report/listRecordFilter', params).then((res) =>
    Promise.resolve({
      total: res.body.totalCount,
      list: res.body.recordList,
    }),
  );
}
