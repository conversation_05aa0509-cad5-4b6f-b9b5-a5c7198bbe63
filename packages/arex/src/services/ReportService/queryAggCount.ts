import { request } from '@/utils';

export interface QueryAggCountReq {
  appId: string;
  beginTime?: number;
  endTime?: number;
}

export interface QueryFilterCountReq {
  appId: string;
  beginTime?: string;
  endTime?: string;
  ips?: Set<string>;
  env?: string;
  recordVersion?: string;
  recordId?: string;
  responseType?: string;
  cloudImageFourVersion?: string;
}

export type AggOperation = {
  status: number;
  modifiedTime: number;
  id: string;
  appId: string;
  serviceId: string;
  operationName: string;
  operationType: string;
  operationTypes: string[];
  operationResponse: string | null;
  recordedCaseCount: number;
  ip?: Set<string>;
  env: string;
  recordVersion?: string;
  recordId?: string;
  responseType?: string;
  creationTime?: string; // ISO字符串格式
};

export interface QueryAggCountRes {
  operationList: AggOperation[];
}

export async function queryAggCount(params: QueryAggCountReq) {
  return request
    .post<QueryAggCountRes>('/webApi/report/aggCountSimple', params)
    .then((res) => Promise.resolve(res.body.operationList));
}

export async function queryFilterCount(params: QueryFilterCountReq) {
  return request
    .post<QueryAggCountRes>('/webApi/report/filterCount', params)
    .then((res) => Promise.resolve(res.body.operationList));
}
