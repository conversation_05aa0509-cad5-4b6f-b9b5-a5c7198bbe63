import { request } from '@/utils';

import { Contract } from './queryContract';

export interface SyncRequestContractReq {
  appId?: string;
  operationId: string;
}

export type RequestDependencyInfo = {
  dependencyId: string;
  operationName: string;
  operationType: string;
};

export type equestDependencyData = RequestDependencyInfo & { contract: Contract };

export interface SyncRequestContractRes {
  entryPointContractStr: Contract;
  dependencyList?: equestDependencyData[];
}

export async function syncRequestContract(params: SyncRequestContractReq) {
  const res = await request.post<SyncRequestContractRes | null>(
    '/webApi/report/syncRequestContract',
    params,
  );

  return res.body;
}
