import { request } from '@/utils';
import internal from 'stream';

export interface ClusterIntegrationReq  {
  appId: string;
  envNames: string[] | string;
  oaName:String;
}

export interface ClusterIntegrationRes  {
    appId: string;
    envNames: string[] | string;
    oaName:string;
    code:number;
    success:string;
    msg:string;
  }

export async function clusterIntegration(params: ClusterIntegrationReq) {
  const res = await request.post<ClusterIntegrationRes>('/webApi/config/application/clusterIntegration', params);
  return res.body;
}