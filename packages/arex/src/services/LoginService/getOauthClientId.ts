import { request } from '@/utils';

export function getOauthClientId() {
  return request
    .get<{ clientId: string; redirectUri: string; oauthUri: string }>(
      `/webApi/login/oauthInfo/GitlabOauth`,
      undefined,
      {
        headers: { 'access-token': 'no' },
      },
    )
    .then((res) => res.body);
}

//白泽迁移，获取登录用户信息
import { debug } from 'console';
import { useState } from 'react';

import request_v2 from '@/utils/request_v2';

import { CurrentUser } from './data';
import { loginVerifyReq, loginVerifyRes } from './loginVerify';

const REQUEST_OTHER_PARAMS = {
  method: 'post',
  timeout: 3000,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
  },
};

export function queryCurrent(): Promise<CurrentUser | any> {
  // 获取登录用户信息
  return request_v2(
    `https://${window.location.hostname}/server/index.php?g=Web&c=User&o=getUserInfo`,
    REQUEST_OTHER_PARAMS,
  )
    .then((res) => {
      console.log('获取/server/index.php?g=Web&c=User&o=getUserInfo 返回值');

      console.log(res);
      if (res && res.statusCode && res.statusCode === '000000') {
        const userInfo: CurrentUser = {
          realName: res.realName,
          userName: res.userInfo.userName,
          currentUserID: res.currentUserID,
          type: res.type,
          orgId: res.orgId,
        };
        return userInfo;
      }
      console.log('/server/index.php?g=Web&c=User&o=getUserInfo用户信息--->undefined');

      return undefined;
    })
    .catch((error: any) => {
      console.log('请求用户数据失败', error);
    });
}

//请求arex登录接口，去除验证码
export function toLoginVerify58(params: loginVerifyReq): Promise<loginVerifyRes | any> {
  return request_v2('/report/login/verify58', {
    method: 'POST',
    data: { ...params },
  })
    .then((res: { body: loginVerifyRes }) => {
      console.log('请求接口-->/report/login/verify58拿token登录信息');
      //console.log(res.body);
      return res.body;
      //return verify58_res.body;
    })
    .then((error: any) => {
      return error;
    });
}
