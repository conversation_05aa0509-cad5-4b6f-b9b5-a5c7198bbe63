import { request } from '@/utils';

export type loginVerifyReq = {
  userName?: string | undefined;
  verificationCode?: string;
};

export type loginVerifyRes = {
  success: boolean;
  accessToken: string;
  refreshToken: string;
  userName: string; //58登录加
};

export function loginVerify(params: loginVerifyReq) {
  return request.post<loginVerifyRes>(`/webApi/login/verify`, params).then((res) => res.body);
}

//58登录loginVerify58
export function loginVerify58(params: loginVerifyReq) {
  return request.post<loginVerifyRes>(`/report/login/verify58`, params).then((res) => res.body);
}
