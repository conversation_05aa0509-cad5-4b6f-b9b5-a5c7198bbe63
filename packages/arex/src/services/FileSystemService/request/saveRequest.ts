import type { ArexRESTRequest } from '@arextest/arex-request';
import jsonBigInt from 'json-bigint';

import { CollectionNodeType } from '@/constant';
import { request } from '@/utils';

export async function saveRequest(
  workspaceId: string,
  params: ArexRESTRequest & { inherited?: boolean; tags: string[] },
  nodeType: number,
) {
  let copy = { ...params.body.scfRequest };
  if (!params.body.scfRequest) {
    try {
      const scfInfo = jsonBigInt.parse(params.body.scf as string);
      copy = scfInfo;
    } catch (e) {
      console.log('parsed scf failed, params is', params);
    }
  }
  delete copy?.ipOptions;
  delete copy?.branchList;
  const saveParams = {
    address: {
      method: ['WMB', 'SCF', 'WMB_MOCK'].includes(params.requestType)
        ? params.requestType
        : params.method,
      endpoint: params.endpoint,
      requestType: params.requestType || 'SCF',
    },
    params: params.params,
    headers: params.headers,
    testScripts: [
      {
        type: '0',
        icon: null,
        label: 'CustomScript',
        value: params.testScript ? params.testScript : '',
        disabled: false,
      },
    ],
    body: {
      ...params.body,
      ...(params.body.wmb && { wmb: jsonBigInt.stringify(params.body.wmb) }),
      wmb_mock: jsonBigInt.stringify(params.body?.wmb_Consumer),
      //body: params.body?.wmb_Consumer?.msgBody,
      //...(params.body.wmb_mock && { wmb_mock: jsonBigInt.stringify(params.body.wmb_Consumer) }),
      ...(params.body.scfRequest && { scf: jsonBigInt.stringify(copy) }),
      // ...(params.body.scfRequest && { scf: jsonBigInt.stringify(params.body.scfRequest) }),
    },

    preRequestScripts: [
      {
        type: '0',
        icon: null,
        label: 'CustomScript',
        value: params.preRequestScript ? params.preRequestScript : '',
        disabled: false,
      },
    ],
    // id
    workspaceId: workspaceId,
    id: params.id,
    inherited: params.inherited,
    description: params.description,
    labelIds: params.tags,
    contract: jsonBigInt.stringify(params.contract),
  };

  let res;
  if (workspaceId === "682c47f8bd26001c54f4f901") {
    res = await request.post<{ success: boolean }>(
      '/webApi/filesystem/saveCaseByDefaultCaseWorkspace',
      saveParams,
    );
      return res.body.success;
  } else {
    const res = await request.post<{ success: boolean }>(
      `/webApi/filesystem/${
       nodeType === CollectionNodeType.interface ? 'saveInterface' : 'saveCase'
     }`,
      saveParams,
    );
      return res.body.success;
  }
}
