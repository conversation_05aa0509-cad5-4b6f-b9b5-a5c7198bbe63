import type { ArexRESTRequest } from '@arextest/arex-request';
import jsonBigInt from 'json-bigint';

import { CollectionNodeType } from '@/constant';
import { queryDebuggingCase } from '@/services/FileSystemService';
import { request } from '@/utils';

export async function queryRequest(params: {
  id: string;
  nodeType: CollectionNodeType;
  recordId?: string;
  planId?: string;
}): Promise<
  ArexRESTRequest & {
    recordId: string;
    inherited: boolean;
    nodeType: CollectionNodeType;
    tags: string[];
    parentPath: { id: string; name: string; nodeType: CollectionNodeType }[];
  }
> {
  const res = await request.post<any>(
    `/webApi/filesystem/query${
      params.nodeType === CollectionNodeType.interface ? 'Interface' : 'Case'
    }`,
    params,
  );

  if (params.id.length !== 24) {
    // 如果有recordId是从调试页面进来的
    
    if (params.recordId) {
      
      const res = await queryDebuggingCase({
        recordId: params.recordId,
        planId: params.planId,
      });
      if (!res.body) return Promise.reject(res.responseStatusType.responseDesc);
      
      const {
        body: { address, testAddress, ...rest },
        
      } = res;


      return {
        id: rest.id,
        name: rest.name,
        method: address?.method || 'GET',
        requestType: address?.requestType || 'SCF',
        endpoint: address?.endpoint || '',
        headers: rest.headers || [],
        params: rest.params || [],
        body: rest.body || { contentType: 'application/json', body: '' },
        testScript: rest.testScripts?.length > 0 ? rest.testScripts[0].value : '',
        preRequestScript: rest.preRequestScripts?.length > 0 ? rest.preRequestScripts[0].value : '',
        recordId: rest.recordId || params.recordId,
        // @ts-ignore
        inherited: undefined,
        inheritedMethod: '',
        inheritedEndpoint: '',
        nodeType: params.nodeType,
        tags: rest.labelIds || [],
        description: rest.description,
        parentPath: rest?.parentPath,
        // wmb: JSON.parse(rest?.body?.wmb) || {},
        wmb: rest?.body?.wmb ? jsonBigInt.parse(rest.body.wmb) : {},
        contract: jsonBigInt.parse(rest.contract || '[]'),
        parentPreRequestScripts: rest.parentPreRequestScripts?.length > 0 ? rest.parentPreRequestScripts[0].value : '',
        parentTestScripts: rest.parentTestScripts?.length > 0 ? rest.parentTestScripts[0].value : '',
        planId: params.planId,
      };
      
    }

    

    // 如果没有recordId是新增页面进来的
    return {
      id: params.id,
      name: '',
      method: 'GET',
      endpoint: '',
      headers: [],
      params: [],
      body: { contentType: 'application/json', body: '' },
      testScript: '',
      preRequestScript: '',
      // @ts-ignore
      recordId: null,
      // @ts-ignore
      inherited: undefined,
      inheritedMethod: '',
      inheritedEndpoint: '',
      nodeType: params.nodeType,
      tags: [],
      description: '',
      parentPath: [],
      wmb: {},
      contract: [],
      parentPreRequestScripts: '',
      parentTestScripts: '',
    };
  }
  const {
    body: { address, testAddress, ...rest },
  } = res;
  // console.log('rest.wmb', rest?.body.wmb)
  // // console.log('rest.wmb', JSON.parse(rest?.body.wmb))
  // console.log('rest.wmb.key', JSON.parse(rest?.body.wmb).key)

  // scf 信息回写
  const scfInfo = new Map();
  if (address?.method == 'SCF') {
    const scfInfoParams = rest.params;
    for (const index in scfInfoParams) {
      scfInfo.set(scfInfoParams[index].key, scfInfoParams[index].value);
    }
    
  }

  return {
    id: rest.id,
    name: rest.name,
    method: address?.method || 'GET',
    endpoint: address?.endpoint || '',
    headers: rest.headers || [],
    params: rest.params || [],
    body: rest.body || { contentType: 'application/json', body: '' },
    testScript: rest.testScripts?.length > 0 ? rest.testScripts[0].value : '',
    preRequestScript: rest.preRequestScripts?.length > 0 ? rest.preRequestScripts[0].value : '',
    recordId: rest.recordId,
    // @ts-ignore
    inherited: undefined,
    inheritedMethod: '',
    inheritedEndpoint: '',
    nodeType: params.nodeType,
    tags: rest.labelIds || [],
    description: rest.description,
    parentPath: rest?.parentPath,

    // scf
    scfID: scfInfo.get('scfId') || '',
    serviceNameValue: scfInfo.get('serviceName') || '',
    interfaceNameValue: scfInfo.get('interfaceName') || '',
    functionNameValue: scfInfo.get('methodName') || '',

    scfEnvType: scfInfo.get('isOnlineScfKey') == 1 ? 'IP' : 'ScfKey',
    scfIP: scfInfo.get('scfIP') || '',
    branchEnv: scfInfo.get('branchEnv') || '',
    branchID: scfInfo.get('branchID') || '',
    branchName: scfInfo.get('branchID') || '',

    // wmb: JSON.parse(rest?.body?.wmb) || {},
    wmb: rest?.body?.wmb ? jsonBigInt.parse(rest.body.wmb) : {},

    requestType: address.requestType || 'HTTP',
    contract: jsonBigInt.parse(rest.contract || '[]'),
    parentPreRequestScripts:
      rest.parentPreRequestScripts?.length > 0 ? rest.parentPreRequestScripts[0].value : '',
    parentTestScripts: rest.parentTestScripts?.length > 0 ? rest.parentTestScripts[0].value : '',
  };
}
