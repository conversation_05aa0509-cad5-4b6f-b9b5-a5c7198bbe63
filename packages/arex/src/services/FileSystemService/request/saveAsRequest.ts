import { getLocalStorage } from '@arextest/arex-core';
import type { ArexRESTRequest } from '@arextest/arex-request';

import { CollectionNodeType } from '@/constant';
import { request } from '@/utils';

export async function saveAsRequest(
  workspaceId: string,
  params: ArexRESTRequest & { inherited?: boolean; tags: string[] },
  nodeType: number,
  parentPath: any,
) {
  const tmp = { ...params.body.scfRequest };
  delete tmp.ipOptions;
  delete tmp.branchList;

  const copy = {
    ...tmp,
    connectType: tmp.connectType ? tmp.connectType : '1',
    serializedVersion: tmp.serializedVersion ? tmp.serializedVersion : '0',
    env: tmp.env ? tmp.env : '3',
  };

  let method = 'SCF';

  switch (params.requestType) {
    case 'WMB':
    case 'SCF':
      method = params.requestType;
      break;
    case undefined:
    case null:
      method = 'SCF';
      break;
    default:
      method = params.method;
  }

  console.log('params.requestType', method);
  const saveAsParams = {
    address: {
      method,
      endpoint: params.endpoint,
      requestType: method,
    },
    params: params.params,
    headers: params.headers,
    testScripts: params.testScript
      ? [
          {
            type: '0',
            icon: null,
            label: 'CustomScript',
            value: params.testScript,
            disabled: false,
          },
        ]
      : undefined,
    body: {
      ...params.body,
      // wmb: JSON.stringify(params.body.wmb),
      ...(params.body.wmb && { wmb: JSON.stringify(params.body.wmb) }),
      ...(copy && { scf: JSON.stringify(copy) }),
    },
    preRequestScripts: params.preRequestScript
      ? [
          {
            type: '0',
            icon: null,
            label: 'CustomScript',
            value: params.preRequestScript,
            disabled: false,
          },
        ]
      : undefined,
    // id
    workspaceId: workspaceId,
    // id: params.id || "0",
    //id传默认值0，走新建request逻辑，接口返回requestId
    id: '0',
    inherited: params.inherited,
    description: params.description,
    labelIds: params.tags,
    nodeName: params.name || 'Untitled',
    nodeType: 1,
    userName: getLocalStorage('EMAIL_KEY'),
    //这里也需要改下
    requestType: params.requestType,
    parentPath: parentPath,
    // parentPath: ["65b86f9200f7004f8b2f61cc"],
  };
  console.log('------saveAsParams', saveAsParams);
  const res = await request.post<any>(`/report/filesystem/addItemAndSaveInterface`, saveAsParams);
  return res.body;
}
