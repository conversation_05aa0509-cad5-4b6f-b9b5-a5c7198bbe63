import { RoleEnum } from '@arextest/arex-core';

import { request } from '@/utils';

export type InviteToWorkspaceReq = {
  arexUiUrl: string;
  invitor: string;
  role: RoleEnum;
  userNames: string[];
  workspaceId: string;
};

export type InviteToWorkspaceRes = {
  successUsers: string[];
  failedUsers: string[];
  failReason?: string;
};

export type OAUser = {
  realName: string;
  userName: string;
};

export type OAUserRes = {
  status: string;
  msg: string;
  errorCode: string;
  data: OAUser[];
};

export async function inviteToWorkspace(params: InviteToWorkspaceReq) {
  const res = await request.post<InviteToWorkspaceRes>(
    `/webApi/filesystem/inviteToWorkspace`,
    params,
  );
  return res.body;
}

export async function searchOaUserList(params: string) {
  const res = await request.get<OAUserRes>(`/baize/crash/userList?userName=${params}`);
  return res as unknown as OAUserRes;
}
