import { RequestMethodEnum } from '@arextest/arex-core';
import { DataNode } from 'antd/lib/tree';

import { CollectionNodeType, ProtocalType } from '@/constant';
import { request } from '@/utils';
import { QueryWorkspaceByIdRes } from './queryWorkspaceById';

// export interface CollectionType extends DataNode {
//   caseSourceType: number; // 0, 1
//   children: CollectionType[];
//   infoId: string;
//   labelIds: string | null;
//   method: RequestMethodEnum | null;
//   nodeName: string;
//   nodeType: CollectionNodeType;
//   protocal: ProtocalType;
//   requestType: string;
// }

// export type QueryWorkspaceByIdRes = {
//   fsTree: {
//     id: string;
//     roots: CollectionType[];
//     userName: string;
//     workspaceName: string;
//   };
// };

export async function queryDefaultCaseWorkspace(params: { id: string }) {
  return request
    .post<QueryWorkspaceByIdRes>(`/webApi/filesystem/queryDefaultCaseWorkspace`, params)
    .then((res) => res.body?.fsTree);
}