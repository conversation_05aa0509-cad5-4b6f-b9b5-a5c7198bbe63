import { CollectionNodeType } from '@/constant';
import { request } from '@/utils';
import { AddCollectionReq, AddCollectionRes } from './addCollectionItem';

export async function addItemForDefaultCaseWorkspace(params: AddCollectionReq) {
  const {
    nodeName = 'New Collection',
    nodeType = 3,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    requestType = 'SCF',
    parentPath = [],
    ...restParams
  } = params;
  return request
    .post<AddCollectionRes>(`/report/filesystem/addItemForDefaultCaseWorkspace`, {
      nodeName,
      nodeType,
      parentPath,
      requestType,
      ...restParams,
    })
    .then((res) => Promise.resolve(res.body));
}