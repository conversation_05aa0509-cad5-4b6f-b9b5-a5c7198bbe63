import { request } from '@/utils';

export type RemoveCollectionItemReq = {
  id: string;
  removeNodePath: string[];
  userName: string;
};


export type RemoveCollectionItemsReq = {
  id: string;
  removeNodePaths: string[][];  // 修改为二维数组类型
  userName: string;
};
export async function removeCollectionItem(params: RemoveCollectionItemReq) {
  return request.post('/webApi/filesystem/removeItem', params);
}

export async function removeItems(params: RemoveCollectionItemsReq) {
  return request.post('/webApi/filesystem/removeItems', params);
}