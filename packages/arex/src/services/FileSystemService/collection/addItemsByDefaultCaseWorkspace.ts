import request from '@/utils/request';
import { AddItemsByAppNameAndInterfaceNameReq, AddItemsByAppNameAndInterfaceNameRes } from './addItemsByAppNameAndInterfaceName';

export async function addItemsByDefaultCaseWorkspace(
  params: AddItemsByAppNameAndInterfaceNameReq,
) {
  const res = await request.post<AddItemsByAppNameAndInterfaceNameRes>(
    '/webApi/filesystem/addItemsByDefaultCaseWorkspace',
    params,
  );

  return res.body;
}