import { request } from '@/utils';
export interface FilterRuleData {
  coloringType: number;
  filters: string[] | string;
  filterRuleEnum: number;
  compareValue: string;
  alias: string;
}

export interface filterRuleDetailBody {
  id: string;
  dataChangeCreateTime: number;
  dataChangeUpdateTime: number;
  filterName: string;
  appId: string;
  serviceId: string;
  operationName: string;
  operationId: string;
  operationType: string;
  operator: string;
  status: number;
  requestFilterRules: FilterRuleData[];
  responseFilterRules: FilterRuleData[];
  expression: string;
}

export async function getFilterRuleDetail(params: { filterId: string }) {
  const res = await request.get<filterRuleDetailBody[]>(
    '/webApi/config/filter/queryFilterConfiguration?filterId=' + params.filterId,
  );
  return res.body;
}
