import { request } from '@/utils';

export interface FilterSummaryInfosData {
  id: string;
  filterName: string;
}
export interface FilterRuleListBodyData {
  operationId: string;
  operationName: string;
  operator: string;
  operationType: string;
  filterSummaryInfos: FilterSummaryInfosData[];
}
export interface GetFilterRuleListInterface {
  responseStatusType: {
    responseCode: number;
    responseDesc: string;
    timestamp: number;
  };
  body: FilterRuleListBodyData[];
}
export async function getFilterRuleList(params: { appId: string }) {
  const res = await request.get<GetFilterRuleListInterface>(
    '/webApi/config/filter/queryFilterConfigurations?appId=' + params.appId,
  );
  return res.body;
}
