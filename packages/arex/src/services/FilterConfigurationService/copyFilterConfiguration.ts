import { filterRuleDetailBody } from '@/services/FilterConfigurationService/getFilterRuleDetail';
import { request } from '@/utils';

export async function copyFilterConfiguration(params: { filterId: string }) {
  const res = await request.post<filterRuleDetailBody>(
    `/webApi/config/filter/copyFilterConfiguration?filterId=` + params.filterId,
  );
  console.log('copy', res.body);
  return res.body;
}
