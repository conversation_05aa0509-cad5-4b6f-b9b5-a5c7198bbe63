import { request } from '@/utils';

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  originalId?: string;
  isFromHistory?: boolean;
}

// 添加服务器返回的历史记录项类型
export interface ServerHistoryItem {
  id: string | number;
  user_message: string;
  answer: string;
  timestamp?: number;
  module_name?: string;
  desc_status?: number;
  draw_type?: number;
  reply_type?: number;
  answer_time?: number;
}

export interface ChatHistory {
  id: string;
  messages: Message[];
  createdAt: number;
  originalItem?: ServerHistoryItem; // 添加这一行，存储原始服务器返回的数据
}

// 新增分页参数接口
export interface PaginationParams {
  current: number;
  size: number;
}

// 新增请求参数接口
export interface AIHistoryRequestParams {
  req: {
    oa: string;
    service_name: string;
    module_name: string;
  };
  page: PaginationParams;
}

// 修改历史记录响应接口以匹配实际返回格式
export interface AIHistoryResponse {
  status: string;
  code: number;
  data: {
    records: {
      user_message: string;
      answer: string;
      id: number;
      module_name: string;
      desc_status: number;
      draw_type: number;
      reply_type: number;
    }[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  isWelcomeMessage?: boolean; // 添加这个可选属性
}

// 修改历史记录获取函数
export async function getAIHistory(params: AIHistoryRequestParams) {
  try {
    // 修改类型定义，使用AIHistoryResponse
    const response = await request.post<AIHistoryResponse>(
      '/ai/ai-assistant/get-ai-history',
      params,
    );

    const typedResponse = <AIHistoryResponse>(<unknown>response);

    // 假设 response 就是 AIHistoryResponse 类型
    if (typedResponse && typedResponse.data && Array.isArray(typedResponse.data.records)) {
      return typedResponse.data;
    } else {
      console.log('原始响应格式:', response);
      return response;
    }
  } catch (error) {
    console.error('Error in getAIHistory:', error);
    // 返回空结果
    return {
      records: [],
      total: 0,
      size: params.page.size,
      current: params.page.current,
      pages: 0,
    };
  }
}

// 定义发送消息的参数接口
export interface SendMessageParams {
  oa: string;
  message: string;
  moduleName: string;
  serviceName: string;
  triggerUrl: string;
  isHidden: boolean;
}

export async function sendMessage(params: SendMessageParams) {
  return fetch('/ai/ai-assistant/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'text/event-stream',
    },
    body: JSON.stringify(params),
  });
}
