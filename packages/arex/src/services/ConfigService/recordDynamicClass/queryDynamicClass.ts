import { request } from '@/utils';

export type QueryRecordDynamicClassReq = {
  env: string;
  appId: string;
};

export type DynamicClass = {
  env: string;
  modifiedTime?: string;
  id: string;
  appId?: string;
  fullClassName: string;
  methodName?: string;
  keyFormula?: string;
  parameterTypes?: string;
  configType?: number;
};
export type QueryRecordDynamicClassRes = DynamicClass[] | null;

export async function queryDynamicClass(params: QueryRecordDynamicClassReq) {
  const res = await request.get<QueryRecordDynamicClassRes | undefined>(
    '/webApi/config/dynamicClass/useResultAsList/appId/' + params.appId + '/' + params.env,
  );
  return res.body;
}
