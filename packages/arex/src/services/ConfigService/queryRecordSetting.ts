import { MultiEnvironmentConfig } from '@/services/ConfigService/updateMultiEnvCollectSetting';
import { request } from '@/utils';

export interface QueryRecordSettingReq {
  env: string;
  appId: string;
}

export type SerializeSkipInfo = {
  fieldName: string;
  fullClassName: string;
};

export interface QueryRecordSettingRes {
  env: string;
  allowDayOfWeeks: number;
  allowTimeOfDayFrom?: string;
  allowTimeOfDayTo: string;
  appId: string;
  modifiedTime: string;
  sampleRate: number;
  timeMock: boolean;
  excludeServiceOperationSet: string[];
  recordMachineCountLimit?: number;
  serializeSkipInfoList?: SerializeSkipInfo[] | null;
  extendField?: { includeServiceOperations?: string } | null;
  multiEnvConfigs?: MultiEnvironmentConfig[];
  envTags?: Record<string, string[]>;
  collectCoveragePackages?: string[];
  serializationType?: string;
  enableDebugIpSet?: string[];
}

export async function queryRecordSetting(params: QueryRecordSettingReq) {
  const res = await request.get<QueryRecordSettingRes>(
    '/webApi/config/serviceCollect/useResult/appId/' + params.appId + '/' + params.env,
  );
  return res.body;
}
