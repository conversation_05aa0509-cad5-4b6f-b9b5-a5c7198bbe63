import { request } from '@/utils';

export interface UpdateAgentReq {
  appId: string;
  host: string;
  extendField: Record<string, string>;
}
export interface agentSettingReq {
  appId: string;
  extendField: Record<string, string>;
}

export async function updateAgen1(params: UpdateAgentReq) {
  const res = await request.post<boolean>(
    `/webApi/config/applicationInstances/modify/UPDATE`,
    params,
  );
  return res.body;
}

export async function updateAgent(params: UpdateAgentReq) {
  const res = await request.post<boolean>(
    `/webApi/config/applicationInstances/agentStatusChange`,
    params,
  );
  return res.body;
}

export async function openAgentStatus(params: agentSettingReq) {
  const res = await request.post<boolean>(
    `/webApi/config/applicationInstances/agentStatusChange`,
    params,
  );
  return res.body;
}

export async function agentSwitch(env: string, appId: string, checked: boolean) {
  const res = await request.get(
    `/webApi/config/applicationInstances/clusterAgentStatusChange/appId` + '/' + appId + '/' + env + '/' + checked,
  );
  return res.body;
}

export async function updateLimit(
  record: AgentGroup,
  appId: string,
  env: string,
  limit: number | null,
) {
  const res = await request.get(
    `/webApi/config/applicationInstances/updateGroupAgentNumber/appId` +
    '/' +
    appId +
    '/' +
    env +
    '/' +
    record.group +
    '/' +
    limit,
  );
  return res.body;
}