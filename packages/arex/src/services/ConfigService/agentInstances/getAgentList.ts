import { CaseTags } from '@/services/ScheduleService';
import { request } from '@/utils';

export interface AgentData {
  env: string;
  status: number | null;
  modifiedTime: number;
  id: string;
  appId: string;
  recordVersion: string | null;
  ip: string;
  dataUpdateTime: number;
  systemProperties?: CaseTags;
  tags?: CaseTags;
  extendField: Record<string, string>;
  genericsEnable?: int;
  agentActiveState?: number;
  groupRecordMachineCountLimit?: { [key: string]: number };
}

export async function getAgentList(appId: string, env: string) {
  const res: Record<string, any> = await request.get<AgentData[]>(
    // '/webApi/config/applicationInstances/useResultAsList/appId/' + appId + '/' + env,
    '/webApi/config/applicationInstances/agentResultAsList/appId/' + appId + '/' + env,
  );
  return res.body;
}
