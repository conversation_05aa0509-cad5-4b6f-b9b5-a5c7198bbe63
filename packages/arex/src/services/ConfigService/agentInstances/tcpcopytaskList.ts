import { request } from '@/utils';

export interface TaskData {
  id: string;
  taskName: string;
  ip: string;
  recordTime: number;
  timeUnit: string;
  recordQps: number;
  startTimestamp: number;
  endTimestamp: number;
  creator: string;
  createTimestamp: number;
  enableRun: boolean;
  clusterName: string;
  envEnum: string;
}

interface ResponseData {
  msg: string;
  data: never[];
  responseStatusType: {
    responseCode: number;
    responseDesc: string;
    timestamp: number;
  };
  body: {
    code: number;
    msg: string;
    data: TaskData[];
  };
}

export async function getTcpcopytasklist(appId: string, env: string) {
  const res = await request.get<ResponseData>(
    '/webApi/config/serviceCollect/tcpCopy/queryOnlyEntryFluxTask/' + appId + '/' + env,
  );
  // console.log('完整响应:', res); // 检查实际返回结构
  // return res.body;

  if (res?.responseStatusType?.responseCode === 0) {
    return res.body?.data || []; // 直接返回数据数组
  }
  throw new Error(res?.body?.msg || '请求失败');
}


// 创建任务接口
export async function createTcpcopyTask(params: {
  taskName: string;
  ip: string;
  recordTime: number;
  recordQps: number;
  creator: string; 
  clusterName: string;
  timeUnit: 'minutes' | 'hours' | 'days';
  env: 'Product' | 'Test' | 'SandBox' | 'Stable';
}): Promise<boolean> {
  const res = await request.post<{
    code: number;
    msg: string;
    data: boolean | PromiseLike<boolean>;
    responseStatusType: {
      responseCode: number;
      responseDesc: string;
      timestamp: number;
    };
    body: {
      code: number;
      msg: string;
      data: boolean;
    };
  }>('/webApi/config/serviceCollect/tcpCopy/createOnlyEntryFluxTask', params);

  if (res?.responseStatusType?.responseCode === 0 && res?.body?.code === 200) {
    return res.body.data;
  }
  throw new Error(res?.body?.msg || res?.responseStatusType?.responseDesc || '创建任务失败');
}

export async function updateTcpcopyTask(params: {
  id: string;
  taskName: string;
  ip: string;
  recordTime: number;
  recordQps: number;
  creator: string; 
  clusterName: string;
  timeUnit: 'minutes' | 'hours' | 'days';
  env: 'Product' | 'Test' | 'SandBox' | 'Stable';
}): Promise<boolean> {
  const res = await request.post<{
    code: number;
    msg: string;
    data: boolean | PromiseLike<boolean>;
    responseStatusType: {
      responseCode: number;
      responseDesc: string;
      timestamp: number;
    };
    body: {
      code: number;
      msg: string;
      data: boolean;
    };
  }>('/webApi/config/serviceCollect/tcpCopy/updateOnlyEntryFluxTask', params);
  
  if (res?.responseStatusType?.responseCode === 0 && res?.body?.code === 200) {
    return res.body.data;
  }
  throw new Error(res?.body?.msg || res?.responseStatusType?.responseDesc || '更新任务失败');
}


export async function deleteTcpcopyTask(params: {
  taskId: string;
  env: 'Product' | 'Test' | 'SandBox' | 'Stable';
}): Promise<boolean> {
  // 确保参数正确传递
  // console.log('删除任务参数:', params); 
  
  const res = await request.post<{
    code: number;
    msg: string;
    data: boolean | PromiseLike<boolean>;
    responseStatusType: {
      responseCode: number;
      responseDesc: string;
      timestamp: number;
    };
    body: {
      code: number;
      msg: string;
      data: boolean;
    };
  }>('/webApi/config/serviceCollect/tcpCopy/deleteOnlyEntryFluxTask', params);

  if (res?.responseStatusType?.responseCode === 0 && res?.body?.code === 200) {
    return res.body.data;
  }
  throw new Error(res?.body?.msg || res?.responseStatusType?.responseDesc || '删除任务失败');
}



export async function startTcpcopyTask(params: {
  taskId: string;
  env: 'Product' | 'Test' | 'SandBox' | 'Stable';
}): Promise<boolean> {
  const res = await request.post<{
    code: number;
    msg: string;
    data: boolean | PromiseLike<boolean>;
    responseStatusType: {
      responseCode: number;
      responseDesc: string;
      timestamp: number;
    };
    body: {
      code: number;
      msg: string;
      data: boolean;
    };
  }>('/webApi/config/serviceCollect/tcpCopy/startOnlyEntryFluxTask', params);

  if (res?.responseStatusType?.responseCode === 0 && res?.body?.code === 200) {
    return res.body.data;
  }
  throw new Error(res?.body?.msg || res?.responseStatusType?.responseDesc || '启动任务失败');
}


export async function stopTcpcopyTask(params: {
  taskId: string;
  env: 'Product' | 'Test' | 'SandBox' | 'Stable';
}): Promise<boolean> {
  const res = await request.post<{
    code: number;
    msg: string;
    data: boolean | PromiseLike<boolean>;
    responseStatusType: {
      responseCode: number;
      responseDesc: string;
      timestamp: number;
    };
    body: {
      code: number;
      msg: string;
      data: boolean;
    };
  }>('/webApi/config/serviceCollect/tcpCopy/stopOnlyEntryFluxTask', params);

  if (res?.responseStatusType?.responseCode === 0 && res?.body?.code === 200) {
    return res.body.data;
  }
  throw new Error(res?.body?.msg || res?.responseStatusType?.responseDesc || '停止任务失败');
}