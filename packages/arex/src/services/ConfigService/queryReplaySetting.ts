import { request } from '@/utils';

export type ExcludeOperationMap = { [key: string]: string[] };

export type QueryReplaySettingRes = {
  status: number | null;
  modifiedTime: number;
  appId: string;
  excludeOperationMap: ExcludeOperationMap;
  offsetDays: number;
  targetEnv: string[];
  sendMaxQps: number;
  env: string;
};

export async function queryReplaySetting(params: { appId: string; env: string }) {
  const res = await request.get<QueryReplaySettingRes>(
    '/webApi/config/schedule/useResult/appId/' + params.appId + '/' + params.env,
  );
  return res.body;
}

// 查询ip列表
export async function queryIpList(params: { appId: string }) {
  const res = await request.get<QueryReplaySettingRes>(
    // '/webApi/config/applicationInstances/IpList/appId/' + params.appId + '/' + params.env,
    '/webApi/config/applicationInstances/IpList/appId/' + params.appId,
  );
  return res.body;
}

// 查询ip列表
export async function queryGroupedIpList<AgentInfo>(params: { appId: string }) {
  const res = await request.get<AgentInfo>(
    // '/webApi/config/applicationInstances/IpList/appId/' + params.appId + '/' + params.env,
    '/webApi/config/applicationInstances/groupedIpList/appId/' + params.appId,
  );
  return res.body;
}
