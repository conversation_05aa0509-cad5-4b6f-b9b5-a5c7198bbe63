// import { ExcludeOperationMap } from '@/services/ConfigService/queryReplaySetting';
import { request } from '@/utils';
export type TableRow = { [key: string]: string[] };

export type UpdateReplaySettingReq = {
  appId: string;
  offsetDays?: number;
  // excludeOperationMap?: ExcludeOperationMap;
  excludeOperationMap?: TableRow;
  env: string;
};

export async function updateReplaySetting(params: UpdateReplaySettingReq) {
  const res = await request.post<boolean>('/webApi/config/schedule/modify/UPDATE', params);
  return res.body;
}

export type UpdateMockTypeReq = {
  operationIds: string[];
  mockType: boolean;
};

export async function updateMockType(params: UpdateMockTypeReq) {
  const res = await request.post<boolean>(
    '/webApi/config/applicationOperation/useResult/MockType',
    params,
  );
  return res.body;
}

export type QueryMockTypeRes = {
  key: string;
  values: string[];
}[];

export async function queryMockTypeList(appId: string) {
  const res = await request.get<QueryMockTypeRes>(
    `/webApi/config/applicationOperation/useResult/MockType/queryMockList/${appId}`,
  );
  return res.body;
}

export async function deleteMockType(operationId: string) {
  const res = await request.get<boolean>(
    `/webApi/config/applicationOperation/useResult/MockType/delete/${operationId}`,
  );
  return res.body;
}
