import { request } from '@/utils';

export interface CiCdConfigurationBodyData {
  appId: string;
  cicdSwitch: boolean;
  executionEnvironment: number[]; // SANDBOX:1 STABLE:2 TEST:3 PRODUCT:4
  threshold: number;
  replayRange: number; // LAST24HOURS:1 TODAY:2 OTHER:3
  replayRangeHours: number;
  replayName: string;
  triggerTime: number; // ARRANGE:1 SENDFORTEST:2
  yunTriggerTime: number; //(废弃) ARRANGE:1 
  yunTrigger: number; // ARRANGE:1 ; UPGRADE
  replayPath: string[];
  replayCasesNum: number;
  filterRules: string[];
  deleted: number;
  createTime: number;
  updateTime: number;
}
export interface GetCiCdConfigurationInterface {
  responseStatusType: {
    responseCode: number;
    responseDesc: string;
    timestamp: number;
  };
  body: CiCdConfigurationBodyData[];
}
export async function getCiCdConfiguration(params: { appId: string }) {
  const res = (await request.get<GetCiCdConfigurationInterface>(
    '/report/config/ciCdConfiguration/queryCiCdConfiguration?appId=' + params.appId,
  )) as unknown as GetCiCdConfigurationInterface;
  console.log('queryCiCdConfigurationres', res);
  return res.body?.[0];
}
