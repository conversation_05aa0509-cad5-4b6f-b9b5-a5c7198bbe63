import { request } from '@/utils';

export interface CICDConfiguration {
  appId: string;
  cicdSwitch: boolean;
  executionEnvironment: string;
  threshold: number;
  replayRange: string;
  replayRangeHours: number;
  replayName: string;
  triggerTime: string;
  yunTriggerTime: string;   // (废弃)
  yunTrigger: string;
  replayPath: string;
  replayCasesNum: number;
  filterRules: string;
  deleted: number;
}
export async function saveCiCdConfiguration(params: CICDConfiguration) {
  const res = await request.post<boolean>(
    `/report/config/ciCdConfiguration/saveCiCdConfiguration`,
    params,
  );
  return res.body;
}
