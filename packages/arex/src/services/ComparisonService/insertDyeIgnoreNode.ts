import { IgnoreDyeNodeBase } from '@/services/ComparisonService/queryDyeIgnoreNode';
import { request } from '@/utils';

export type DyeDependencyParams =
  | Pick<IgnoreDyeNodeBase, 'operationType' | 'operationName'>
  | false;
export enum DyeExpirationType {
  permanent,
  temporary,
}

export type IgnoreDyeExpiration = {
  expirationType: DyeExpirationType;
  expirationDate: number;
};

export type InterfaceDyeIgnoreNode = {
  compareConfigType: number | null;
  fsInterfaceId: string | null;
} & IgnoreDyeNodeBase &
  Partial<IgnoreDyeExpiration>;

export async function insertDyeIgnoreNode(params: IgnoreDyeNodeBase | InterfaceDyeIgnoreNode) {
  const res = await request.post<boolean>(
    '/webApi/config/coloring/addColoringConfiguration',
    params,
  );
  return res.body;
}
