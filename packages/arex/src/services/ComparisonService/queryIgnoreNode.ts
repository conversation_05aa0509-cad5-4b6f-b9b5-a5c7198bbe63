import { OperationId, OperationType } from '@/services/ApplicationService';
import { request } from '@/utils';

import { IgnoreExpiration } from './insertIgnoreNode';

export interface QueryNodeReq<T extends OperationType> {
  appId: string;
  operationId?: OperationId<T>;
  operationType?: string;
  operationName?: string;
}

export interface IgnoreNodeBase {
  appId?: string;
  operationId: OperationId<'Global'>;
  // 为 dependency 添加忽略项
  operationType?: string;
  operationName?: string;
  exclusions: string[];
  compareTypes: string[];
  path?: string;
}

export type QueryIgnoreNode = {
  modifiedTime: string;
  id: string;
} & IgnoreNodeBase &
  IgnoreExpiration;

//原有的
export async function queryIgnoreNode1(params: QueryNodeReq<'Global'>) {
  const res = await request.post<QueryIgnoreNode[]>(
    '/webApi/config/comparison/exclusions/queryComparisonConfig',
    { ...params, operationId: params.operationId || undefined },
  );
  return res.body
    .map<QueryIgnoreNode>((item) => ({
      ...item,
      path: item.exclusions.concat(['']).join('/'),
    }))
    .sort((a, b) => a.path!.localeCompare(b.path!));
}

//改造的
export async function queryIgnoreNode(params: QueryNodeReq<'Global'>) {
  const res = await request.post<QueryIgnoreNode[]>(
    '/webApi/config/comparison/exclusions/queryComparisonConfig',
    { ...params, operationId: params.operationId || undefined },
  );

  return res.body
    .map<QueryIgnoreNode>((item) => {
      // 处理 exclusions
      const exclusionsPath = (item.exclusions || []).concat(['']).join('/');

      // 处理 compareTypes，确保它不是 null
      const compareTypesPath = (item.compareTypes || []).concat(['']).join('/');

      // 返回一个新的对象，包含处理过的exclusions和compareTypes
      return {
        ...item,
        path: exclusionsPath,
        compareTypesPath, // 新增属性，存放处理后的compareTypes
      };
    })
    .sort((a, b) => a.path!.localeCompare(b.path!));
}
