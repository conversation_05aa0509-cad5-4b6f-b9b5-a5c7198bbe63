import { OperationId, OperationType } from '@/services/ApplicationService';
import { request } from '@/utils';

import { IgnoreExpiration } from './insertIgnoreNode';

export interface QueryDyeNodeReq<T extends OperationType> {
  appId: string;
  operationId?: OperationId<T>;
  colorType: 'req' | 'res' | null;
}

export interface IgnoreDyeNodeBase {
  appId?: string;
  operationId: OperationId<'Global'>;
  // 为 dependency 添加忽略项
  operationType?: string;
  operationName?: string;
  colorings: string[];
  // 3 请求， 4响应
  colorType: number;
  compareTypes: string[];
  path?: string;
}

export type QueryDyeIgnoreNode = {
  modifiedTime: string;
  id: string;
} & IgnoreDyeNodeBase &
  IgnoreExpiration;

//原有的
export async function queryDyeIgnoreNode(params: QueryDyeNodeReq<'Global'>) {
  const res = await request.get<QueryDyeIgnoreNode[]>(
    '/webApi/config/coloring/queryColoringConfiguration',
    // '/webApi/config/comparison/exclusions/queryComparisonConfig',
    { ...params, operationId: params.operationId || undefined },
  );
  let list = (res.body || [])
    .map<QueryDyeIgnoreNode>((item) => ({
      ...item,
      path: (item.colorings || []).concat(['']).join('/'),
    }))
    .sort((a, b) => a.path!.localeCompare(b.path!));
    // .filter((item) => (params.colorType === 'res' ? item.colorType === 4 : item.colorType !== 4));
    //允许colorType传入null
    if (params.colorType !== null) {
      list = list.filter((item) => (params.colorType === 'res' ? item.colorType === 4 : item.colorType !== 4));
    }
  return list;
}
