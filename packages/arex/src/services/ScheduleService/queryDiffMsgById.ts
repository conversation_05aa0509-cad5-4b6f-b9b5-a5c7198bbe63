import { CompareResultDetail } from '@/services/ReportService';
import { request } from '@/utils';

export interface QueryDiffMsgByIdReq {
  id: string;
}

export interface QueryDiffMsgByIdRes {
  compareResultDetail: CompareResultDetail;
  desensitized: boolean;
}

export interface caseQueryDiffMsgById {
  compareResultDetail: CompareResultDetail;
  desensitized: boolean;
}

export async function queryDiffMsgById(params: QueryDiffMsgByIdReq) {
  return request
    .get<QueryDiffMsgByIdRes>(`/schedule/report/queryDiffMsgById/${params.id}`)
    .then((res) =>
      Promise.resolve({
        data: res.body.compareResultDetail,
        encrypted: res.body.desensitized,
      }),
    );
}
export async function caseQueryDiffMsgById(params: QueryDiffMsgByIdReq) {
  console.log('33333333333333')
  return request
    .get<QueryDiffMsgByIdRes>(`/schedule/report/case/caseQueryDiffMsgById/${params.id}`)
    .then((res) =>
      Promise.resolve({
        data: res.body.compareResultDetail,
        encrypted: res.body.desensitized,
      }),
    );
}
