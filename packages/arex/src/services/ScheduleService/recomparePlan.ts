import { getLocalStorage } from '@arextest/arex-core';
import axios from 'axios';

import { ACCESS_TOKEN_KEY, APP_ID_KEY } from '@/constant';

export type RecomparePlanRes = {
  desc: string;
  result: number;
  data: boolean;
};

export function recomparePlan(planId: string) {
  return new Promise<RecomparePlanRes>((resolve, reject) => {
    return axios
      .get<RecomparePlanRes>(`/schedule/replay/compare/reCompare/${planId}`, {
        headers: {
          'access-token': getLocalStorage<string>(ACCESS_TOKEN_KEY),
          appId: getLocalStorage<string>(APP_ID_KEY),
        },
      })
      .then((res) => resolve(res.data))
      .catch((err) => reject(err));
  });
}
