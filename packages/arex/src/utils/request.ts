import { clearLocalStorage, getLocalStorage, setLocalStorage } from '@arextest/arex-core';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

import {
  ACCESS_TOKEN_KEY,
  APP_ID_KEY,
  EMAIL_KEY,
  isClientProd,
  REFRESH_TOKEN_KEY,
} from '@/constant';
import { toLoginVerify58 } from '@/services/LoginService';
import { REDIRECT_58_PASSPORT } from '@/services/LoginService/constants';

import port from '../../config/port.json';

type IRequestConfig<T = AxiosResponse> = AxiosRequestConfig;

export type ResponseStatusType = {
  responseCode: number;
  responseDesc: string;
  timestamp: number;
};

export type IAxiosResponse<T> = {
  responseStatusType: ResponseStatusType;
  body: T;
};

export class Request {
  instance: AxiosInstance;

  constructor(config: IRequestConfig) {
    this.instance = axios.create(config);

    // 全局请求拦截
    this.instance.interceptors.request.use(
      (request) => {
        const accessToken = getLocalStorage<string>(ACCESS_TOKEN_KEY);
        // if (!accessToken && request.headers.get('access-token') !== 'no')
        //   return Promise.reject(
        //     'Required request header "access-token" for method parameter type String is not present',
        //   );

        request.headers.set('access-token', accessToken);
        request.headers.set('appId', getLocalStorage<string>(APP_ID_KEY));

        return request;
      },
      (error) => Promise.reject(error),
    );

    // 全局响应拦截
    this.instance.interceptors.response.use(
      async (response) => {
        if (window.location.hostname === 'localhost') {
          return Promise.resolve(response.data);
        }
        if (response.data.responseStatusType?.responseCode === 4) {
          //window.message.error(response.data.responseStatusType.responseDesc);
          // window.message.error('接口鉴权失效，重新获取中...');
          clearLocalStorage(ACCESS_TOKEN_KEY);
          const userName: string | undefined = getLocalStorage(EMAIL_KEY);
          const verify58_res = await toLoginVerify58({ userName });
          if (verify58_res) {
            setLocalStorage(ACCESS_TOKEN_KEY, verify58_res?.accessToken);
            setLocalStorage(REFRESH_TOKEN_KEY, verify58_res?.refreshToken);
            // 刷新页面
            window.location.reload();
          }
          window.location.href = `${REDIRECT_58_PASSPORT}&u=${encodeURIComponent(
            window.location.href,
          )}`;
          return Promise.reject(response.data.responseStatusType);
        }
        return Promise.resolve(response.data);
      },
      (error) => {
        return Promise.reject(error);
      },
    );
  }

  // 返回的Promise中结果类型为AxiosResponse<any>
  request<Res>(config: AxiosRequestConfig): Promise<IAxiosResponse<Res>> {
    return new Promise<IAxiosResponse<Res>>((resolve, reject) => {
      this.instance
        .request<any, IAxiosResponse<Res>>(config)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  // 封装 GET 请求方法
  get<Res>(url: string, params?: any, config?: AxiosRequestConfig): Promise<IAxiosResponse<Res>> {
    return this.request<Res>({
      url,
      params,
      ...config,
    });
  }

  // 封装 POST 请求方法
  post<Res>(url: string, params?: any, config?: AxiosRequestConfig): Promise<IAxiosResponse<Res>> {
    return this.request<Res>({
      url,
      data: params,
      method: 'POST',
      ...config,
    });
  }

  // 封装 PATCH 请求方法
  patch<Res>(url: string, params?: any, config?: AxiosRequestConfig): Promise<IAxiosResponse<Res>> {
    return this.request<Res>({
      url,
      data: params,
      method: 'PATCH',
      ...config,
    });
  }

  // 封装 DELETE 请求方法
  delete<Res>(
    url: string,
    params?: any,
    config?: AxiosRequestConfig,
  ): Promise<IAxiosResponse<Res>> {
    return this.request<Res>({
      url,
      data: params,
      method: 'DELETE',
      ...config,
    });
  }
}

const request = new Request({
  timeout: 30000,
  baseURL: isClientProd ? 'http://localhost:' + port.electronPort : undefined,
});

export default request;
