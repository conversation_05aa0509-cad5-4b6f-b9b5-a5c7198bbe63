import { clearLocalStorage, getLocalStorage, setLocalStorage } from '@arextest/arex-core';

import { ACCESS_TOKEN_KEY, EMAIL_KEY, REFRESH_TOKEN_KEY, WORKSPACE_STORAGE_KEY } from '@/constant';
import { useMenusPanes, useUserProfile, useWorkspaces } from '@/store';

import useCollections from '../store/useCollections';

const globalStoreReset = () => {
  const email = getLocalStorage<string>(EMAIL_KEY);

  console.log(
    'globalStoreReset start',
    getLocalStorage<string>(EMAIL_KEY),
    getLocalStorage<string>(ACCESS_TOKEN_KEY),
    getLocalStorage<string>(REFRESH_TOKEN_KEY),
  );

  clearLocalStorage(EMAIL_KEY);
  clearLocalStorage(ACCESS_TOKEN_KEY);
  clearLocalStorage(REFRESH_TOKEN_KEY);
  clearLocalStorage(WORKSPACE_STORAGE_KEY);
  // useCollections.getState().reset();
  // useMenusPanes.getState().reset();
  // useWorkspaces.getState().reset();
  // useUserProfile.getState().reset();

  // localStorage.clear();
  console.log(
    'globalStoreReset end',
    getLocalStorage<string>(EMAIL_KEY),
    getLocalStorage<string>(ACCESS_TOKEN_KEY),
    getLocalStorage<string>(REFRESH_TOKEN_KEY),
  );
  email?.startsWith('GUEST') && setLocalStorage(EMAIL_KEY, email);
};

export default globalStoreReset;
