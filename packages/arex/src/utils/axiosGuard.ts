import axios, { AxiosRequestConfig, ResponseType } from 'axios';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import jsonBigInt from 'json-bigint';
import xspy from 'xspy';

import { isClient, isClientProd } from '@/constant';

import port from '../../config/port.json';

// chrome插件代理
function AgentAxios<T>(params: any) {
  return new Promise<T>((resolve, reject) => {
    const tid = String(Math.random());
    window.postMessage(
      {
        type: '__AREX_EXTENSION_REQUEST__',
        tid: tid,
        payload: params,
        v: '0.3.0',
      },
      '*',
    );
    window.addEventListener('message', receiveMessage);
    function receiveMessage(ev: any) {
      if (ev.data.type === '__AREX_EXTENSION_RES__' && ev.data.tid == tid) {
        window.removeEventListener('message', receiveMessage, false);
        // 这边的err类型是真正的error，而不是401、404这种
        if (ev.data.res.type === 'error') {
          const err = new Error();
          err.message = ev.data.res.message;
          err.name = ev.data.res.name;
          err.stack = ev.data.res.stack;
          reject(err);
        } else {
          resolve(ev.data.res);
        }
      }
    }
  });
}

if (isClientProd) {
  axios.defaults.baseURL = 'http://localhost:' + port.electronPort;
}

const handleBody = (request: any) => {
  // 判断如果是FormData的话，需要转换成数组
  if (Object.prototype.toString.call(request.body) === '[object FormData]') {
    const body = [];
    for (const [key, value] of request.body.entries()) {
      body.push({
        key,
        value,
      });
    }
    return JSON.stringify(body);
  }
  return request.body;
};

// 处理流式请求的函数
const handleStreamingRequest = async (request: any, sendResponse: any, accessToken: string) => {
  try {
    const decodeURL = decodeURLQuery(request.url);
    const finalHeaders = request.headers;
    const finalBody = {
      body: request.body || ' ',
      type: 3,
    };

    if (isFormData(finalHeaders)) {
      delete finalHeaders['content-type'];
      delete finalHeaders['Content-Type'];
      delete finalHeaders['contentType'];
      delete finalHeaders['ContentType'];

      finalBody.type = 1;

      const body = handleBody(request);
      const arr = jsonBigInt.parse(body);
      const result: Record<string, any> = {};

      arr.forEach((item: { key: string; value: string }) => {
        result[item.key] = item.value;
      });

      finalBody.body = JSON.stringify(result);
    }

    // 构建流式请求的参数，格式与do_quick_run一致
    const streamingParams = {
      uri: decodeURL,
      requestType: request.method,
      addUserId: '2016022213254973f012f0',
      apiID: '909912',
      apiProtocol: 0,
      headers: finalHeaders,
      isDebug: false,
      ssl: false,
      requestBody: finalBody,
    };

    // 发送到流式代理接口
    const response = await fetch('/webApi/sse/proxy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Access-Token': accessToken,
      },
      body: JSON.stringify(streamingParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 获取响应头
    const responseHeaders: any = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });

    // 创建流式响应对象
    const streamResponse = {
      status: response.status,
      headers: Object.keys(responseHeaders).map(key => ({ key, value: responseHeaders[key] })),
      ajaxType: 'xhr',
      responseType: 'stream',
      response: {
        type: 'streaming',
        body: [],
        isComplete: false,
        reader: response.body?.getReader(),
      },
    };

    console.log('🔍 [axiosGuard] Created streaming response:', streamResponse);
    sendResponse(streamResponse);
  } catch (error) {
    console.error('Streaming request error:', error);
    const errorResponse = {
      status: 500,
      headers: [],
      ajaxType: 'xhr',
      responseType: 'arraybuffer',
      response: Buffer.from(JSON.stringify({ error: 'Streaming request failed' })),
    };
    sendResponse(errorResponse);
  }
};

if (!isClient) {
  xspy.onRequest(async (request: any, sendResponse: any) => {
    if (request.headers.shadowcookie) {
      request.headers.cookie = request.headers.shadowcookie;
      delete request.headers.shadowcookie;
    }
    if (request.headers.shadowhost) {
      request.headers.host = request.headers.shadowhost;
      delete request.headers.shadowhost;
    }
    const isScfReq = request.url.includes('http://iapi.58corp.com/iapi/scence/openapi/invokeScfV3')
      ? true
      : false;
    const isWmbReq = request.url.includes('report/wmb/sendMsg') ? true : false;
    const isWmbMockReq = request.url.includes('/webApi/wmb/key/client/mock') ? true : false;
    const isStreamingReq = request.headers.accept && request.headers.accept.includes('text/event-stream');
    const getAccessToken = () => {
      const accessToken: string = localStorage.getItem('accessToken') || '';
      return accessToken;
    };

    // 判断是否是pm发的
    if (request.headers['postman-token']) {
      if (
        !window.__AREX_EXTENSION_INSTALLED__ ||
        localStorage.getItem('agentType') === '"iapi_agent"'
      ) {
        if (isScfReq) {
          const response = await post(
            '/iapi/iapi/scence/openapi/invokeScfV3',
            {
              ...jsonBigInt.parse(request.body),
            },
            getAccessToken(),
          );
          const dummyResponse = {
            status: response.status,
            headers: [],
            ajaxType: 'xhr',
            responseType: 'arraybuffer',
            response: Buffer.from(response.rawData || ''),
          };
          sendResponse(dummyResponse);
        } else if (isWmbReq) {
          const response = await post(
            '/report/wmb/sendMsg',
            {
              // ...JSON.parse(request.body),
              ...jsonBigInt.parse(request.body),
            },
            getAccessToken(),
          );
          const dummyResponse = {
            status: response.status,
            headers: [],
            ajaxType: 'xhr',
            responseType: 'arraybuffer',
            response: Buffer.from(response.rawData || ''),
          };
          sendResponse(dummyResponse);
        } else if (isWmbMockReq) {
          //wmbmock的请求，绕开云端代理agent
          const response = await post(
            '/webApi/wmb/key/client/mock',
            {
              ...jsonBigInt.parse(request.body),
            },
            getAccessToken(),
          );
          const dummyResponse = {
            status: response.status,
            headers: [],
            ajaxType: 'xhr',
            responseType: 'arraybuffer',
            response: Buffer.from(response.rawData || ''),
          };
          sendResponse(dummyResponse);
        } else if (isStreamingReq) {
          // 处理流式请求
          await handleStreamingRequest(request, sendResponse, getAccessToken());
        } else {
          const API_BASE_URL = 'https://iapi.58corp.com/iapi/scence/run_api/do_quick_run';
          const decodeURL = decodeURLQuery(request.url);

          const finalHeaders = request.headers;
          const finalBody = {
            body: request.body || ' ',
            type: 3,
          };

          if (isFormData(finalHeaders)) {
            delete finalHeaders['content-type'];
            delete finalHeaders['Content-Type'];
            delete finalHeaders['contentType'];
            delete finalHeaders['ContentType'];

            finalBody.type = 1;

            const body = handleBody(request);
            // 解析字符串为对象数组
            const arr = jsonBigInt.parse(body);

            // 创建一个新的对象来存储结果
            const result: Record<string, any> = {};

            // 遍历对象数组，并根据key-value对填充结果对象
            arr.forEach((item: { key: string; value: string }) => {
              result[item.key] = item.value;
            });

            // 将结果对象转换为字符串，注意这里不需要对texts进行特殊处理，因为它是对象
            const resultStr = JSON.stringify(result);

            finalBody.body = resultStr;
          }

          const httpRespon = await axios
            .post(API_BASE_URL, {
              uri: decodeURL,
              requestType: request.method,
              addUserId: '2016022213254973f012f0',
              apiID: '909912',
              apiProtocol: 0,
              headers: finalHeaders,
              isDebug: false,
              // queryParam: [],
              ssl: false,
              requestBody: finalBody,
            })
            .catch((err) => {
              return {
                status: 400,
                headers: [],
                data: '',
              };
            });

          try {
            const dummyResponse2 = {
              status: httpRespon.data.data.responseCode,
              headers: httpRespon.data.data.respHeaders.reduce((p: any, c: any) => {
                if (p[c.name]) {
                  // 如果header已存在，用逗号连接值
                  p[c.name] = `${p[c.name]}; ${c.value}`;
                } else {
                  // 如果不存在，直接赋值
                  p[c.name] = c.value;
                }
                return p;
              }, {}),
              ajaxType: 'xhr',
              responseType: 'arraybuffer',
              response: Buffer.from(httpRespon.data.data.responseBody),
            };
            sendResponse(dummyResponse2);
          } catch (error) {
            const dummyResponse2 = {
              status: 400,
              headers: [],
              ajaxType: 'xhr',
              responseType: 'arraybuffer',
              response: Buffer.from(JSON.stringify(httpRespon.data)),
            };
            sendResponse(dummyResponse2);
          }
        }
      } else {
        const agentData: any = await AgentAxios({
          method: request.method,
          url: request.url,
          headers: {
            ...request.headers,
            'content-type': (request.headers['content-type'] || '').includes('application/json')
              ? 'application/json'
              : request.headers['content-type'],
          },
          body: ['GET'].includes(request.method) ? undefined : handleBody(request),
        }).catch((err) => {
          return {
            status: 400,
            headers: [],
            data: '',
          };
        });
        const dummyResponse = {
          status: agentData.status,
          headers: agentData.headers.reduce((p: any, c: { key: any; value: any }) => {
            return {
              ...p,
              [c.key]: c.value,
            };
          }, {}),
          ajaxType: 'xhr',
          responseType: 'arraybuffer',
          response: new Buffer(agentData.data),
        };
        sendResponse(dummyResponse);
      }
    } else {
      sendResponse();
    }
  });
}

const isFormData = (headers: any): boolean => {
  const contentType =
    headers['content-type'] ||
    headers['Content-Type'] ||
    headers['contentType'] ||
    headers['ContentType'];
  return contentType && contentType.toLowerCase() === 'multipart/form-data';
};

const decodeURLQuery = (urlStr: string | URL) => {
  try {
    const url = new URL(urlStr);
    const query = url.search.slice(1); // 去掉开头的'?'
    if (!query) return urlStr; // 无查询参数直接返回

    // 解码查询参数
    const decodedQuery = query
      .split('&')
      .map((param) => {
        const [key, value] = param.split('=');
        return value !== undefined ? `${key}=${decodeURIComponent(value)}` : key;
      })
      .join('&');

    // 手动拼接URL，避免自动编码
    const baseURL = `${url.origin}${url.pathname}`;
    const hash = url.hash;
    return decodedQuery ? `${baseURL}?${decodedQuery}${hash}` : `${baseURL}${hash}`;
  } catch (error) {
    console.error('无效的URL:', error);
    return urlStr;
  }
};

interface MyConfig extends AxiosRequestConfig {
  headers: {
    'Content-Type': string;
    'Access-Token'?: string | undefined;
  };
  withCredentials: boolean;
  responseType: ResponseType; // 使用 ResponseType 枚举
}

export async function post(
  url: string,
  data: object,
  accessToken?: string,
): Promise<{ data: any; status: number; rawData: string }> {
  const config = {
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Access-Token': accessToken,
    },
    withCredentials: true,
    responseType: 'text',
  };

  try {
    const response = await axios.post(url, data, config as MyConfig);
    const rawData = response.data;
    const d = jsonBigInt.parse(rawData);
    return { data: d, status: response.status, rawData: rawData };
  } catch (error) {
    console.error(error);
    if (axios.isAxiosError(error)) {
      return {
        data: null,
        status: error.response?.status || 500,
        rawData: error.response?.data || '',
      };
    } else {
      return {
        data: null,
        status: 500,
        rawData: 'An unexpected error occurred',
      };
    }
  }
}
