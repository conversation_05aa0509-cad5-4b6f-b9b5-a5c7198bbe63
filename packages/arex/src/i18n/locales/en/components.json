{"http": {"urlEmpty": "Please input url", "extensionNotInstalled": "Extension not installed", "extensionIncorrect": "Chrome Extension version is incorrect, please install", "enterRequestUrl": "Enter request URL", "importUrl": "Import URL", "showCode": "Show Code", "clearAll": "Clear All", "untitledRequest": "Untitled Request", "copyLink": "Copy Link", "viewMyLinks": "View My Links", "saveAs": "Save As", "params": "Params", "requestBody": "Body", "requestHeaders": "Headers", "responseHeaders": "Response Headers", "result": "Result", "authorization": "Authorization", "pre-requestScript": "Pre-request Script", "test": "Test", "queryParams": "QueryParams", "contentType": "ContentType", "responseBody": "Response Body", "responseNotReady": "Enter the URL and click Send to get a response", "testPassed": "test passed", "testFailed": "test failed", "javaScriptCode": "JavaScript Code", "override": "Override", "add_script_block": "Add Script Block", "compare_config": "CompareConfig", "add_tag": "Add Tag", "env": {"closeConfirm": "Current environment configuration changes are not saved"}, "selectSaveLocation": "Please select the save location", "selectInterface": "Please select an interface collection", "setContentTypeInHeaders": "Set <0>Content-Type</0> in Headers", "title": "Title", "saveLocation": "Save Location", "saveTo": "Save To", "saveError": "Save Error"}, "authorization": {"type": "Type", "type_parent": "Inherit auth from parent", "header_description": "This authorization method will be used for every request in this folder. You can override this by specifying one in the request.", "footer_description": "The authorization header will be automatically generated when you send the request."}, "collectionMenu": {"collection": "Collection", "environment": "Environment", "newCreate": "New"}, "applicationsMenu": {"createApp": "Create Application", "filterFavoriteApps": "Filter <PERSON> Apps", "appFilterPlaceholder": "Search Applications"}, "collection": {"create_new": "Create new collection", "batch_run": "Batch Run", "batch_compare": "Batch Compare", "new_collection": "New Collection", "new_request": "New Request", "new_case": "New Case", "new_folder": "New Folder", "add_folder": "Add Folder", "add_request": "Add Request", "add_case": "Add Case", "rename": "<PERSON><PERSON>", "duplicate": "Duplicate", "delete": "Delete", "import_export": "Import / Export", "import": "Import", "export": "Export", "from_postman_description": "Import from Postman collection file", "mindmap_preview": "Mindmap Preview"}, "replay": {"selectApplication": "Please select an application", "appSetting": "AppSetting", "refresh": "refresh", "clusterIntegration": "Add Cluster Integration Functionality", "searchForPlanId": "Search for planId", "startSuccess": "Started Successfully", "startFailed": "Start Failed", "startButton": "Run Test", "targetHost": "Target Host", "targetHostTooltip": "Enter the target host where you would like to replay the recorded cases", "targetEnv": "Target Env", "description": "Description", "descriptionTooltip": "Enter the name or description of your report", "descriptionEnv": "Description Env", "caseStartTime": "Start Time", "caseEndTime": "End Time", "caseRange": "Case range", "caseRangeTooltip": "Select the time range for the recorded cases, and the cases recorded within this time period will be replayed.", "emptyHost": "Target Host can't be empty", "emptyCaseRange": "Case range can't be empty", "emptyStartTime": "Start Time can't be empty", "emptyEndTime": "End Time can't be empty", "planName": "Name", "planNamePlaceholder": "Optional, defaults to app name + creation time", "paths": "Paths", "pathsTooltip": "Select the paths for which cases will be replayed. If not specified, all paths will be replayed by default.", "pathsPlaceholder": "Optional, all paths are selected by default", "caseCountLimit": "Case Count Limit", "caseCountLimitPlaceholder": "Optional, default 1000", "caseCountUnit": "(Max Count / Interface)", "filterRules": "Filter Rules", "replayReportName": " Report Name", "caseTags": "Tags", "state": "State", "all": "All", "passed": "Passed", "failed": "Failed", "invalid": "Invalid", "blocked": "Blocked", "queued": "Queued", "executor": "Executor", "replayStartTime": "Replay Start Time", "report": "Report", "moreReport": "More", "latest": "Latest", "basicInfo": "Basic Information", "replayPassRate": "Replay Pass Rate", "passRate": "Pass Rate", "apiPassRate": "API Pass Rate", "results": "Results", "reportId": "Report ID", "recordVersion": "Record version", "replayVersion": "Replay version", "totalCases": "Total Cases", "planItemID": "Plan Item ID", "api": "API", "timeConsumed": "Time consumed(s)", "cases": "Cases", "action": "Action", "denoise": "Comparative Config Recommendation", "noDenoiseRecommended": "No comparison configuration is recommended", "logs": "Execution Logs", "recompare": "Recompare", "rerun": "<PERSON><PERSON>", "rerunFailed": "Failed Case", "rerunInvalid": "Error <PERSON>", "rerunTimeout": "Timeout Case", "rerunError": "Rerun error", "rerunTip": "Are you sure to rerun?", "parameterError": "Parameter error", "abort": "Abort", "abortTheCase": "Abort the Case", "confirmAbortCase": "Are you sure to abort this case?", "terminate": "Terminate", "terminateTheReplay": "Terminate the Replay", "confirmTerminateReplay": "Are you sure to terminate this replay?", "delete": "Delete", "deleteTheReport": "Delete the Report", "shareReport": "Share", "confirmDeleteReport": "Are you sure to delete this report?", "init": "init", "running": "running", "done": "done", "interrupted": "interrupted", "rerunning": "rerunning", "caseloaded": "caseloaded", "cancelled": "cancelled", "unknownState": "Unknown State", "diffScenes": "DiffScenes", "case": "ReplayCase", "recordId": "Record ID", "replayId": "Replay ID", "status": "Status", "detail": "Detail", "save": "Save", "recordDetail": "Record Detail", "debug": "Debug", "success": "Success", "caseServiceAPI": "Main Service API", "replayReport": "ReplayReport", "viewFailedOnly": "View Failed Only", "saveCase": "Save Case", "cancel": "Cancel", "create": "Create", "saveTo": "Save to", "caseName": "Case name", "emptyCaseName": "Please input case name!", "emptyTitle": "Please input the title of collection!", "caseLabels": "Case Labels", "selectTree": "Please select", "benchmark": "Benchmark", "test": "Test", "pointOfDifference": "Point of difference", "sceneCount": "Scene Count", "caseErrorCount": "Error Count", "issues": "issue(s)", "treeMode": "Tree Mode", "valueOf": "Value of", "isDifferenceExcepted": ",excepted", "actual": ", actual", "unknown": "Unknown", "baseMissing": "Base Missing", "testMissing": "Test Missing", "leftMissing": "Left Missing", "leftMissingDesc": "is missing on the left", "rightMissing": "Right Missing", "rightMissingDesc": "is missing on the right", "differentValue": "Different Value", "ignoreNode": "Ignore Node", "escExit": "Press Esc to exit", "addIgnoreSuccess": "Ignore node successfully", "diffMatch": "Diff Match", "replayEndTime": "Replay End Time", "operationName": "Operation Name", "recordedCaseCount": "Case Count", "recordTime": "Record Time", "operationType": "Operation Type", "re-calculateReport": "Re-calculate Report", "noAppOwnerAlert": "There is no owners of this application, some operation privileges will be disabled, please ", "addOwner": "Add application owner", "noRecordCountTip": "No recording data is available at this time, try starting Agent recording with the following command", "viewAll": "View All", "markExclusion": "Check Exclusion", "marked": "Checked", "allMarked": "All Checked", "exclusionType": "ExclusionType", "bug": "Bug", "asExpectation": "As Expectation", "arexProblem": "Arex Problem", "remark": "Remark", "executionLogs": "Execution logs"}, "replayCase": {"ignore": "Ignore", "ignored": "Ignored", "conditionalIgnore": "Conditional Ignore", "selectConditionNode": "Please select condition node", "ignoreNode": "Ignore Node", "conditionNode": "Condition Node", "ignorePath": "Ignore Path", "selectConditionNodeTip": "Please select the leaf node for Condition Ignore", "conditionIgnoreError": "Condition Ignore Error"}, "jsonDiff": {"ignore": "Ignore Key", "ignoreToGlobal": "Ignore to Global", "ignoreToInterfaceOrDependency": "Ignore to Interface / Dependency", "temporaryIgnore": "Temporary Ignore(7d)", "conditionalIgnore": "Conditional Ignore", "sort": "Sort Key", "diffMatch": "Diff Match", "decode": "Node Decode", "base64DecodeContent": "Base64 decode content", "failedToDecodeBase64": "Failed to decode Base64"}, "caseDetail": {"more": "More", "caseDetail": "Case Detail", "recordId": "Record ID", "operationName": "Operation Name", "categoryType": "Operation Type", "recordVersion": "Recording Agent Version", "requestAttributes": "Request Extra Attributes", "requestBodyType": "Request Body Type", "responseAttributes": "Response Extra Attributes", "responseBodyType": "Response Body Type"}, "appSetting": {"record": "Record", "replay": "Replay", "importYaml": "Import Yaml", "compareConfig": "Compare Config", "configType": "Type", "configTarget": "Target", "nodesIgnore": "Nodes Ignore", "nodesSort": "Nodes Sort", "nodesDesensitization": "Nodes Desensitization", "categoryIgnore": "Category Ignore", "global": "Global", "interface": "Interfaces", "dependency": "Dependency", "entryPoint": "EntryPoint", "agentVersion": "Agent Version", "agentHost": "Agent Host", "debugTooltip": "Whether Agent outputs debug logs", "basic": "Basic", "duration": "Duration", "period": "Period", "frequency": "Frequency", "serializeSkip": "SerializeSkip", "fieldName": "FieldName", "advanced": "Advanced", "timeMock": "Time Mock", "dynamicClasses": "Dynamic Classes", "dynamicClassesTooltip": "To ensure consistent execution results despite cache data differences, AREX collects and mocks local cache data. This is achieved by configuring the access method of the local cache as a dynamic class, allowing custom mocking of the method. It records the data from the dynamically configured class method in the production environment and replays the corresponding matched data. In addition to cache data, various memory data can also be mocked.", "fullClassName": "Full Class Name", "fullClassNameTooltip": "The full class name of the local cache, e.g.:\ninfix: io.arex.inst.dynamic.*namicTest* \nsuffix: io.arex.inst.dynamic.*namicTestClass\nprefix: io.arex.inst.dynamic.DynamicTest*\nequals: io.arex.inst.dynamic.DynamicTestClass\nabstract class or interface: ac:io.arex.inst.dynamic.AbstractDynamicTestClass", "methodName": "Method Name", "methodNameTooltip": "The method name to be mocked.", "parameterTypes": "Parameter Types", "parameterTypesTooltip": "Parameter list (Multiple separated by '@', e.g.: <EMAIL>)", "keyFormulaTooltip": "Not required, parameter combination, multiple separated by ',' , support SpEL expression.", "keyFormula": "Key Formula", "collectCoveragePackages": "Package Prefix", "serializationType": "Serialization Type", "enableDebugIp": "Debug IP", "action": "Action", "delConfirmText": "Are you sure to delete this record?", "caseRange": "Case range", "caseRangeTooltip": "Select the time range for replaying the recorded cases of Continuous Integration (CI), unit in \"days\".", "exclusion": "Exclusion", "exclusionTooltip": "Select the paths to skip recording", "mockRuletip": "Select the configs to real-data-replaying instead of mock-replaying", "path": "Path", "value": "Value", "updateSuccess": "update success", "editArea": "Edit Area (Click interface to start)", "clickToIgnore": "click node to ignore", "dataStructure": "Data Structure", "emptyResponse": "Empty Response", "configResponse": "Config Response", "addKey": "Add Key", "editResponse": "Edit Response", "addSortKey": "Add Sort Key", "noSortNodes": "No Sort Nodes", "keys": "keys", "chooseOneNode": "choose one array node", "chooseOnekey": "choose key to sort", "arrayTree": "A<PERSON>yTree", "sortTree": "SortTree", "emptyKey": "Please enter ignored key", "expireOn": "Expire on", "expired": "Expired", "noIgnoredNodes": "No Ignored Nodes", "noDesensitizationNodes": "No Desensitization Nodes", "emptyFullClassName": "Please enter full class name", "emptyCaseRange": "Please input your case range!", "emptyValue": "Please enter value", "mockTips": "When \"body\" is a json string, it will be automatically converted into a json object, and restored when saving.", "runningStatus": "Running Status", "frequencyUnit": "times/minute", "frequencyTip": "Single-interface single-day recording: * times", "recordMachineNum": "Record Machine Count", "agentStatusTip": "SLEEPING: Agent injected but not recording\nUNSTART: Agent not injected\nWORKING: Recording in progress", "inclusion": "Inclusion", "inclusionTooltip": "Select the paths to record", "other": "Other", "dye": "Dye", "filterRule": "FilterRule", "ci/cd": "CI/CD", "dangerZone": "Danger Zone", "owners": "Owners", "appBasicSetup": "App Basic Setup", "appName": "App Name", "appNameEmptyTip": "The application name cannot be empty", "visibilityLevel": "Level", "public": "Public", "private": "Private", "deleteApp": "Delete App", "deleteThisApp": "Delete this App", "deleteTip": "Once you delete an app, there is no going back. Please be certain.", "confirmDelete": "Confirm Delete", "deleteConfirmTip": "Please enter app Id to confirm delete", "maxQPS": "Max QPS", "QPSTips": "Allowable value range 1 - 20", "emptyQPS": "Please input your max QPS", "skipMock": "<PERSON><PERSON>", "skipMockTooltip": "Configure interfaces that do not require mocks, multiple values separated by ','.", "sync": "Sync", "emptyContractTip": "No contract, please try to synchronize the contract", "contract": "Contract", "editContract": "Edit Contract", "chooseEncryptionMethodName": "Please choose encryption method name", "categoryType": "Category Type", "categoryName": "Category Name", "chooseCategoryType": "Please select the type of ignore (multiple)", "categoryTypePlaceholder": "Please select the type of category", "categoryNamePlaceholder": "Please select the name of category", "environment": "Environment", "onlyCompareEntry": "onlyCompareEntry", "onlyCompareEntryTooltip": "After activation, only compare the responses of the entry interface, not the dependent interfaces."}, "env": {"searchEnvironment": "Search Environment", "setCurrentEnv": "Set as current environment", "duplicateCurrentEnv": "Duplicate current environment", "createEnvVariable": "Create environment variable", "duplicateEnv": "Duplicate environment", "deleteEnv": "Delete environment", "delConfirmText": "Deleting this environment might cause any monitors or mock servers using it to stop functioning properly. Are you sure you want to continue?", "variable": "Variable", "key": "Key", "value": "Value", "addEnvConfig": "Add Environment Config", "standardEnv": "Standard", "multiEnv": "Multi Environment"}, "workSpace": {"workSpace": "Workspace", "noPermissionOrInvalid": "接口鉴权失效，重新获取中...", "emptySpaceName": "Please input the name of workspace!", "createSuccess": "create workspace successfully", "importSuccess": "Import success!", "importFailed": "Import Fail!", "add": "addWorkspace", "edit": "Edit Workspace", "import": "Import", "selectFile": "Select File", "importCollection": "Import collection", "collections": "Collections", "overview": "Overview", "labels": "Labels", "workspaceSettings": "Workspace settings", "name": "Name", "emptyName": "Please input your name!", "update": "Update", "noInvitedUser": "no invited user", "admin": "Admin", "editor": "Editor", "viewer": "Viewer", "notAccepted": "Not accepted", "accepted": "Accepted", "del": "Delete workspace", "delMessage": "Once deleted, a workspace is gone forever along with its data.", "delConfirmText": "Are you sure to delete this workspace?", "labelName": "LabelName", "color": "Color", "delLabelConfirmText": "Are you sure to delete this label?", "addLabelButton": "New Label", "labelId": "Id", "moveOut": "Move out", "moveOutTip": "Are you sure to move out this user?", "leave": "Leave workspace", "leaveTip": "Are you sure to leave this workspace?", "invite": "Invite", "email": "Email", "oa": "OA Account", "role": "Role", "inviteTitle": "Invite to this workspace", "sendInvitation": "Send Invitation", "enterInviteeEmail": "Enter Invitee OA Account (use Enter to split multiple OA accounts)", "inviteSuccessful": "Invite successful", "invitationFailed": "Invitation Failed", "adminPermission": "Manage workspace details and members.", "editorPermission": "Create and edit workspace resources", "viewerPermission": "View and export workspace resources"}, "dropdownMenu": {"close": "Close", "closeOther": "Close Other Tabs", "closeAll": "Close All Tabs", "closeUnmodified": "Close Unmodified Tabs", "closeLeft": "Close Tabs to the Left", "closeRight": "Close Tabs to the Right"}, "systemSetting": {"userInterface": "User Interface", "compact": "Compact", "theme": "Theme", "darkMode": "Dark Mode", "primaryColor": "Primary Color", "language": "Language", "zen": "Zen mode", "avatar": "Avatar", "dataDesensitization": "Data Desensitization", "jarFileUrlPlaceholder": "Please enter Jar file Url", "replayCallback": "<PERSON><PERSON>", "replayCallbackPlaceholder": "Please enter replay callback Url", "systemLogs": "System Logs", "openSystemLogs": "Open System Logs", "application": "Application", "version": "Version", "checkUpdate": "Check for updates", "isLatest": "It's already the latest version", "newVersionDetected": "New version detected", "startDownload": "Download about to start...", "checkFailed": "Check for updates failed, please try again", "downloadExtension": "Download Extension"}, "cicd": {"cicdSwitch": "CI/CD Switch", "executionEnvironment": "Execution Environment", "threshold": "Acceptable <PERSON><PERSON><PERSON>old", "replayRange": "Replay Range", "replayName": "Replay Name", "triggerTime": "<PERSON><PERSON>", "yunTrigger": "<PERSON> Time", "replayPath": "Replay Path", "replayCasesNum": "Number Of Replay Cases", "filterRules": "Filter Rules", "arrange": "<PERSON><PERSON><PERSON>", "upgrade": "Upgrade", "sendForTest": "Send For Test", "noTrigger": "No Trigger", "last24Hours": "Last 24 Hours", "today": "Today", "other": "Other", "recent": "Recent", "hours": "Hours", "save": "Save"}, "aiAssistant": {"welcomeMessage": "Hello! I'm an AI assistant. How can I help you?", "errorMessage": "Sorry, an error occurred. Please try again later.", "showHistory": "Show History", "historyTitle": "History Dialogue", "hideHistory": "Hide History"}}