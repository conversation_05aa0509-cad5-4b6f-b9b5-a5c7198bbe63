{"folderPage": {"tests": "Tests"}, "batchRunPage": {"selectCaseTip": "Select cases for batch execution", "batchSend": "<PERSON><PERSON> Send", "statusBlockStructure": "Status Block Structure", "requestStatus": "Request Status", "testStatus": "Test Status", "loading": "Loading", "requestSuccess": "Request Success", "requestFailed": "Request Failed", "noTestScript": "No Test Script", "allPassed": "All test scripts passed", "SomeFailed": "Some failed test scripts exist", "allFailed": "None of the test scripts passed"}, "batchComparePage": {"select_need_compare_case": "Select comparison cases to runssss", "run_compare": "Run Comparison Cases", "compare_results": "Compare results", "result": "Result", "error.count": "Error Count"}, "replay": {"saveto": "Save To"}, "oauth": {"authenticating": "Authenticating", "redirectedTip": "You will be redirected to the home page in a few seconds ..."}}