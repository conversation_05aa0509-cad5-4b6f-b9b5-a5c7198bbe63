{"http": {"urlEmpty": "请输入 URL", "extensionNotInstalled": "插件未安装", "extensionIncorrect": "Chrome Extension 版本不正确，请安装", "enterRequestUrl": "请输入请求 URL", "importUrl": "导入 URL", "showCode": "显示代码", "clearAll": "全部清除", "untitledRequest": "未命名请求", "copyLink": "复制链接", "viewMyLinks": "我的链接", "saveAs": "另存为", "params": "参数", "requestBody": "请求体", "requestHeaders": "请求头", "responseHeaders": "响应头", "result": "结果", "authorization": "授权", "pre-requestScript": "预请求脚本", "test": "测试", "queryParams": "查询参数", "contentType": "内容类型", "responseBody": "响应体", "responseNotReady": "请求未发送，当前无响应", "testPassed": "测试成功", "testFailed": "测试失败", "javaScriptCode": "JavaScript 代码", "override": "覆盖", "add_script_block": "添加脚本块", "compare_config": "比对配置", "add_tag": "添加标签", "env": {"closeConfirm": "当前环境配置修改未保存"}, "selectSaveLocation": "请选择保存位置", "selectInterface": "请选择接口集合", "setContentTypeInHeaders": "在请求头中添加 <0>Content-Type</0>", "title": "名称", "saveLocation": "保存位置", "saveTo": "保存至", "saveError": "保存失败"}, "authorization": {"type": "类型", "type_parent": "继承父类授权", "header_description": "此授权方法将用于此文件夹中的每个请求，你可以在一个具体的请求中重写。", "footer_description": "授权头将在发送请求时自动生成。"}, "collectionMenu": {"collection": "集合", "environment": "环境", "newCreate": "新建"}, "applicationsMenu": {"createApp": "新建应用", "filterFavoriteApps": "收藏应用列表", "appFilterPlaceholder": "搜索应用"}, "collection": {"create_new": "新建集合", "batch_run": "批量执行", "batch_compare": "批量比对", "new_collection": "新集合", "new_request": "新请求", "new_case": "新用例表", "new_folder": "新文件夹", "add_folder": "添加文件夹", "add_request": "添加请求", "add_case": "添加用例", "rename": "重命名", "duplicate": "拷贝", "delete": "删除", "import_export": "导入/导出", "import": "导入", "export": "导出", "from_postman_description": "从 Postman 集合中导入", "batch_delete_case": "删除所有用例", "mindmap_preview": "预览脑图"}, "replay": {"selectApplication": "请选择一个应用", "appSetting": "应用设置", "refresh": "刷新", "clusterIntegration": "集群接入", "searchForPlanId": "搜索 planId", "startSuccess": "启动成功", "startFailed": "启动失败", "startButton": "运行测试", "targetHost": "目标回放地址", "targetHostTooltip": "输入需要进行回放测试的目标端口", "targetEnv": "目标回放环境", "caseStartTime": "Case 开始时间", "caseEndTime": "Case 结束时间", "caseRange": "回放范围", "caseRangeTooltip": "选择用例的录制时间范围，回放该时间段内录制到的用例。", "emptyHost": "目标回放环境不能为空", "emptyCaseRange": "回放范围不能为空", "emptyStartTime": "Case 开始时间不能为空", "emptyEndTime": "Case 结束时间不能为空", "planName": "回放名称", "planNamePlaceholder": "可选，默认为应用名称 + 创建时间", "paths": "回放路径", "pathsTooltip": "选择需要回放的路径。如果不指定，默认情况下，所有路径下的用例都将被回放。", "pathsPlaceholder": "可选，默认选择所有路径", "caseCountLimit": "回放用例数", "caseCountLimitPlaceholder": "可选，默认为 1000", "caseCountUnit": "(最大条数 / 接口)", "filterRules": "筛选规则", "caseTags": "标签", "recordCaseEnv": "录制环境", "state": "状态", "all": "全部", "passed": "成功", "failed": "失败", "invalid": "无效", "blocked": "待执行", "queued": "队列中", "timeOut": "超时", "executor": "执行人", "replayStartTime": "开始时间", "report": "报告", "moreReport": "更多", "latest": "最新", "basicInfo": "基本信息", "replayPassRate": "回放通过率", "passRate": "Case 通过率", "apiPassRate": "API 通过率", "results": "结果", "reportId": "任务 ID", "recordVersion": "录制版本", "replayVersion": "回放版本", "totalCases": "Case 数", "planItemID": "子任务 ID", "api": "API", "timeConsumed": "耗时(秒)", "cases": "case 数", "action": "操作", "denoise": "对比配置推荐", "noDenoiseRecommended": "暂无对比配置推荐", "logs": "执行日志", "recompare": "重新比对", "rerun": "重新执行", "rerunFailed": "失败 case", "rerunInvalid": "无效 case", "rerunTimeout": "超时 case", "rerunError": "重新执行(仅错误类型)", "rerunTip": "确定重新执行该回放?", "parameterError": "参数错误", "init": "初始化", "abort": "终止", "abortTheCase": "终止任务", "confirmAbortCase": "确定终止该任务?", "terminate": "终止", "terminateTheReplay": "终止回放", "confirmTerminateReplay": "确定终止该回放?", "delete": "删除", "deleteTheReport": "删除报告", "confirmDeleteReport": "确定删除该报告?", "shareReport": "分享报告", "running": "进行中", "done": "完成", "interrupted": "异常终止", "rerunning": "重跑", "caseloaded": "Case 加载完成", "cancelled": "取消", "unknownState": "未知", "diffScenes": "差异场景", "case": "回放 Case", "recordId": "录制 ID", "replayId": "回放 ID", "status": "状态", "detail": "详情", "save": "保存", "recordDetail": "录制详情", "debug": "调试", "agent": "录制开关", "success": "成功", "caseServiceAPI": "接口名称", "replayReport": "回放报告", "viewFailedOnly": "只展示失败", "saveCase": "保存用例", "cancel": "关闭", "create": "保存", "saveTo": "保存至 ", "caseName": "Case 名称", "emptyCaseName": "请输入 Case 名称!", "emptyTitle": "请输入 Collection 名称!", "caseLabels": "Case 标签", "selectTree": "请选择保存路径", "benchmark": "录制", "test": "回放", "pointOfDifference": "差异点", "sceneCount": "差异数", "caseErrorCount": "错误数", "issues": "差异", "treeMode": "报文详情", "valueOf": "节点 ", "isDifferenceExcepted": "，期望", "actual": ", 实际", "unknown": "未知", "baseMissing": "录制缺失", "testMissing": "测试未调", "leftMissing": "录制为空", "leftMissingDesc": "录制结果为空", "rightMissing": "回放为空", "rightMissingDesc": "回放结果为空", "differentValue": "不同点", "ignoreNode": "忽略节点", "escExit": "按 ESC 退出", "addIgnoreSuccess": "添加忽略成功", "diffMatch": "差异展示", "replayEndTime": "结束时间", "operationName": "接口名称", "recordedCaseCount": "用例数", "recordTime": "录制时间", "operationType": "接口类型", "re-calculateReport": "重算报告", "noAppOwnerAlert": "当前应用无 Owner，一些操作权限将被禁用，请", "addOwner": "添加应用 Owner", "noRecordCountTip": "暂无录制数据，请尝试使用以下命令启动 Agent 录制", "viewAll": "查看全部", "markExclusion": "检查排除", "marked": "已检查", "allMarked": "已检查全部", "exclusionType": "排除类型", "bug": "代码漏洞", "asExpectation": "符合预期", "arexProblem": "AREX 问题", "remark": "备注", "analyze": "智能分析", "description": "回放描述", "executionLogs": "执行日志"}, "replayCase": {"ignore": "忽略", "ignored": "已忽略", "conditionalIgnore": "条件忽略", "selectConditionNode": "请选择条件节点", "ignoreNode": "忽略节点", "conditionNode": "条件节点", "ignorePath": "忽略路径", "selectConditionNodeTip": "请选择参考子节点进行条件忽略", "conditionIgnoreError": "条件忽略错误"}, "jsonDiff": {"ignore": "添加忽略", "ignoreToGlobal": "忽略至全局", "ignoreToInterfaceOrDependency": "忽略至接口 / 依赖", "temporaryIgnore": "临时忽略(7天)", "conditionalIgnore": "条件忽略", "sort": "添加排序", "diffMatch": "显示差异", "decode": "节点解析", "base64DecodeContent": "Base64 解码内容", "failedToDecodeBase64": "Base64 解码失败"}, "caseDetail": {"more": "详情", "caseDetail": "录制详情", "recordId": "录制ID", "operationName": "接口名", "categoryType": "接口类型", "recordVersion": "录制Agent版本", "requestAttributes": "请求额外参数", "requestBodyType": "请求体类型", "responseAttributes": "返回额外参数", "responseBodyType": "返回体类型"}, "appSetting": {"record": "录制", "replay": "回放", "importYaml": "Yaml 配置导入", "compareConfig": "对比配置", "dye": "染色", "filterRule": "过滤", "configType": "类型", "configTarget": "目标", "nodesIgnore": "忽略节点", "nodesSort": "数组节点", "nodesDesensitization": "加密节点", "categoryIgnore": "类型忽略", "global": "全局", "interface": "接口", "dependency": "依赖", "entryPoint": "主入口", "agentVersion": "Agent <PERSON>", "agentHost": "Agent 地址", "debugTooltip": "Agent 是否输出 debug 日志", "agentTooltip": "该ip是否开启流量筛选开关", "basic": "基础", "duration": "录制周期", "period": "录制时间", "frequency": "录制频率", "serializeSkip": "序列化忽略", "fieldName": "字段名", "advanced": "高级", "tcpcopyTaskList": "Tcpcopy任务列表", "timeMock": "Mo<PERSON> 时间", "dynamicClasses": "动态类", "dynamicClassesTooltip": "实际应用中会使用各式缓存来提升运行时的性能，为了避免由于缓存数据的差异导致的执行结果不一致的问题，AREX 也支持了本地缓存数据的采集和 Mock 功能。实现方法是将访问本地缓存的方法配置成动态类，相当于你自定义了这个方法进行 Mock，会在生产环境录制你配置的这个动态类方法的数据，回放相应的匹配出数据返回。除了缓存数据，也可以 Mock 各种内存数据。", "fullClassName": "全类名", "fullClassNameTooltip": "本地缓存的类名，必须为全类名, 如\n中缀: io.arex.inst.dynamic.*namicTest*\n后缀: io.arex.inst.dynamic.*namicTestClass\n前缀: io.arex.inst.dynamic.DynamicTest*\n等于: io.arex.inst.dynamic.DynamicTestClass\n抽象类或接口: ac:io.arex.inst.dynamic.AbstractDynamicTestClass", "methodName": "方法名", "methodNameTooltip": "需要 Mock 的方法名", "parameterTypes": "参数类型", "parameterTypesTooltip": "所有参数的类型，需为全类名，多以“@”分隔。如 <EMAIL>。", "keyFormulaTooltip": "非必填，参数组合，多个用’,‘隔开，支持 SpEL 表达式。", "keyFormula": "参数组合表达式", "collectCoveragePackages": "<PERSON><PERSON> 包前缀", "serializationType": "序列化方式", "enableDebugIp": "Debug IP 集合", "delConfirmText": "是否删除这条记录？", "caseRange": "Case 数量上限", "caseRangeTooltip": "常规的回放任务可以在左侧菜单栏的 Replay 中通过手动开启，这里是为 CI (continuous integration) 设置用例自动回放范围，单位为 “days”。", "exclusion": "排除规则", "tcpcopymode": "Tcp Copy模式", "tcpcopymodetip": "只回放⼊⼝流量，不Mock及⽐对内部调⽤", "exclusionTooltip": "选择要跳过录制的路径。", "mockRuletip": "选择要使用真实调用的路径配置（未配置的路径将使用录制的数据进行mock调用）", "path": "路径", "value": "值", "updateSuccess": "更新成功", "editArea": "修改区（点击接口开始）", "clickToIgnore": "点击添加忽略节点", "dataStructure": "数据结构", "emptyResponse": "响应为空", "configResponse": "配置响应", "addKey": "添加节点", "editResponse": "修改响应", "addSortKey": "添加 List 节点", "noSortNodes": "List 节点为空", "keys": "节点", "chooseOneNode": "选择一个数组", "chooseOnekey": "请选择标识节点", "arrayTree": "数组节点", "sortTree": "关键标识节点", "emptyKey": "请输入忽略节点", "expireOn": "过期时间", "expired": "已过期", "noIgnoredNodes": "忽略节点为空", "noEncryptionNodes": "加密节点为空", "emptyFullClassName": "请输入全类名", "emptyCaseRange": "请输入 Case 数量上限!", "emptyValue": "请输入值", "mockTips": "\"body\"为json字符串时会自动转换成json对象，保存时还原。", "runningStatus": "运行状态", "frequencyUnit": "次/分钟", "frequencyTip": "单接口单日录制: * 次", "recordMachineNum": "录制机器数量", "agentStatusTip": "SLEEPING: Agent已注入,但不录制\nUNSTART: Agent未注入\nWORKING: 正在录制", "inclusion": "包含规则", "inclusionTooltip": "选择要录制的路径。", "other": "其他", "owners": "所有者", "appBasicSetup": "应用基础配置", "appName": "应用名称", "appNameEmptyTip": "应用名称不能为空", "visibilityLevel": "可见性", "public": "公开", "private": "私有", "dangerZone": "危险区域", "deleteApp": "删除应用", "deleteThisApp": "删除该应用", "deleteTip": "删除应用后，将无法恢复，请谨慎操作。", "confirmDelete": "确认删除", "deleteConfirmTip": "请输入应用 ID 确认删除", "maxQPS": "最大QPS", "QPSTips": "有效范围 1 - 20", "emptyQPS": "请输入最大QPS", "skipMock": "不Mock依赖", "skipMockTooltip": "设置不需要回放的接口，多个值使用','分隔。", "sync": "同步", "emptyContractTip": "无报文契约，请尝试同步更新契约", "contract": "契约", "editContract": "编辑契约", "chooseEncryptionMethodName": "请选择选择加密方法", "categoryType": "依赖类型", "categoryName": "依赖名称", "chooseCategoryType": "请选择忽略类型（可多选）", "categoryTypePlaceholder": "请选择依赖类型", "categoryNamePlaceholder": "请输入依赖名称", "environment": "环境", "agentSetting": "agent设置", "onlyCompareEntry": "只比对入口", "onlyCompareEntryTooltip": "开启后，只比对入口接口的响应，不比对依赖接口"}, "env": {"searchEnvironment": "搜索环境", "setCurrentEnv": "默认为当前环境", "duplicateCurrentEnv": "复制当前环境配置", "createEnvVariable": "创建环境变量", "duplicateEnv": "复制环境", "deleteEnv": "删除环境", "delConfirmText": "删除此环境可能会导致使用它的任何监视器或 MOCK 服务停止工作。你确定要继续吗？", "variable": "变量名称", "key": "键", "value": "值", "addEnvConfig": "添加环境配置", "standardEnv": "标准", "multiEnv": "多环境"}, "workSpace": {"workSpace": "工作空间", "noPermissionOrInvalid": "无目标工作空间权限或目标工作空间无效", "emptySpaceName": "请输入名称!", "createSuccess": "创建成功", "importSuccess": "导入成功!", "importFailed": "导入失败!", "add": "新增工作空间", "edit": "修改工作空间", "import": "导入", "selectFile": "选择文件", "importCollection": "导入集合", "collections": "集合", "overview": "概览", "labels": "标签", "workspaceSettings": "设置", "name": "名称", "emptyName": "请输入名称!", "update": "更新", "noInvitedUser": "无受邀用户", "admin": "管理员", "editor": "编辑者", "viewer": "观察者", "notAccepted": "未被接受", "accepted": "已接受", "del": "删除工作空间", "delMessage": "一旦删除，工作区和它的数据就会永远消失.", "delConfirmText": "是否删除该工作空间？", "labelName": "标签名称", "color": "颜色", "delLabelConfirmText": "是否删除该标签", "addLabelButton": "新增标签", "labelId": "序号", "moveOut": "移出", "moveOutTip": "你确定要将该用户移出工作空间吗？", "leave": "退出工作空间", "leaveTip": "你确定要退出该工作空间吗？", "email": "邮箱", "oa": "OA账号", "role": "角色", "invite": "邀请", "inviteTitle": "邀请至该工作空间", "sendInvitation": "发送邀请", "enterInviteeEmail": "请输入受邀者OA账号 (使用回车键分隔多个邮箱)", "inviteSuccessful": "邀请成功", "invitationFailed": "邀请失败", "adminPermission": "管理工作空间和成员", "editorPermission": "创建和编辑工作空间资源", "viewerPermission": "查看和导出工作空间资源"}, "dropdownMenu": {"close": "关闭", "closeOther": "关闭其他标签页", "closeAll": "关闭所有标签页", "closeUnmodified": "关闭未修改标签页", "closeLeft": "关闭左侧标签页", "closeRight": "关闭右侧标签页"}, "systemSetting": {"userInterface": "用户界面", "compact": "紧凑", "theme": "主题", "darkMode": "深色模式", "primaryColor": "主题色", "language": "语言", "zen": "禅模式", "avatar": "头像", "dataDesensitization": "数据加密", "jarFileUrlPlaceholder": "请输入 Jar 包地址", "replayCallback": "回放回调", "replayCallbackPlaceholder": "请输入回放回调地址", "systemLogs": "系统日志", "openSystemLogs": "打开系统日志", "application": "应用", "version": "版本", "checkUpdate": "检查更新", "isLatest": "已经是最新版本", "newVersionDetected": "检测到新版本", "startDownload": "下载即将开始...", "checkFailed": "检查更新失败，请重试", "downloadExtension": "下载插件"}, "cicd": {"cicdSwitch": "CI/CD开关", "executionEnvironment": "执行环境", "threshold": "通过阈值", "replayRange": "回放范围", "replayName": "回放名称", "triggerTime": "ione触发时机", "yunTrigger": "yun触发时机", "replayPath": "回放路径", "replayCasesNum": "回放用例数", "filterRules": "筛选规则", "noTrigger": "不触发", "arrange": "部署", "upgrade": "升级", "sendForTest": "送测", "last24Hours": "最近1天", "today": "当天", "other": "其他", "recent": "最近", "hours": "小时", "save": "保存"}, "aiAssistant": {"welcomeMessage": "HI，智能客服为您服务！有任何问题可以随时咨询哦~", "errorMessage": "抱歉，发生了错误，请稍后再试。", "showHistory": "历史对话记录 点击获取", "historyTitle": "历史对话记录", "hideHistory": "关闭", "openChat": "智能助手"}}