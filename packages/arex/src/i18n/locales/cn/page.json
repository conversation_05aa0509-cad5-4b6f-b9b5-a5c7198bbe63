{"folderPage": {"tests": "测试"}, "batchRunPage": {"selectCaseTip": "请选择测试用例", "batchSend": "批量发送", "statusBlockStructure": "状态块组成", "requestStatus": "请求状态", "testStatus": "测试状态", "loading": "等待中", "requestSuccess": "请求成功", "requestFailed": "请求失败", "noTestScript": "无测试脚本", "allPassed": "所有测试脚本均通过", "SomeFailed": "存在失败测试脚本", "allFailed": "所有测试脚本均不通过", "uploadParameterizedData": "上传参数化数据", "parameterizedDataUploaded": "已上传参数化数据，共 {count} 组", "parameterizedDataLoaded": "已加载 {count} 组数据", "invalidJsonFormat": "无效的JSON格式", "parameterizedDataShouldBeArray": "参数化数据应为数组格式", "pleaseSelectCase": "请选择要执行的用例", "currentFile": "当前文件: {name}", "parameterized": "参数化 ({count})", "parameterizedFields": "参数化字段", "fields": "字段", "iteration": "迭代", "testPassed": "测试通过", "testFailed": "测试失败"}, "batchComparePage": {"select_need_compare_case": "请选择比对用例", "run_compare": "执行比对", "compare_results": "比对结果", "result": "结果", "error.count": "错误数量"}, "replay": {"saveto": "保存到"}, "oauth": {"authenticating": "正在验证", "redirectedTip": "你将于几秒之后被重定向至主页..."}}