import '@arextest/arex-core/dist/style.css';
import '@/utils/electrionOverride';
import 'dayjs/locale/zh-cn';
import './style/style.css';
import './useWorker';
import './utils/axiosGuard';

import { enableMapSet } from 'immer'; // <--- Import Immer plugin
import React from 'react';
import ReactDOM from 'react-dom/client';
import { B<PERSON>er<PERSON>outer, HashRouter } from 'react-router-dom';

import { isClientProd } from '@/constant';

import App from './App';

enableMapSet();

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  React.createElement(isClientProd ? HashRouter : BrowserRouter, {}, <App />),
);
