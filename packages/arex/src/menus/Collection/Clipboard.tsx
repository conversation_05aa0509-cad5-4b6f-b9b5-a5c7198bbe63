import { CopyOutlined } from '@ant-design/icons';
import { getLocalStorage, setLocalStorage } from '@arextest/arex-core';
import { Button } from 'antd';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

import { EMAIL_KEY } from '@/constant';
import { useNavPane } from '@/hooks';
import { addCollectionItem, AddCollectionReq, queryWorkspaceById } from '@/services/FileSystemService';
import { useMenusPanes, useWorkspaces } from '@/store';

const ClipboardApp = () => {
  const { activeWorkspaceId } = useWorkspaces();
  const [parentPath, setParentPath] = useState("0");
  const [scfId, setScfId] = useState("0");
  const [hisId, setHisId] = useState("0");
  const navPane = useNavPane();

  const readClipboard = async () => {
    // console.log("readClipboard 进来了");
    if (!document.hasFocus()) {
      console.warn('Document does not have focus. Cannot read clipboard.');
      return;
    }

    try {
      const clipboardText = await navigator.clipboard.readText();
      // console.log('Clipboard content:', clipboardText);

      const regex1 = /^https:\/\/iapi\.58corp\.com\/page\/front\/scfManager\/api\/scfTest\/(\d+)(?:\/(\d+))?$/;

      const match = clipboardText.match(regex1);

      if (match) {
        const newScfId = match[1];
        const newHisId = match[2] ? match[2] : '0'; // 如果有第二个捕获组，则使用它，否则默认为 '0'
        setScfId(newScfId);
        setHisId(newHisId);
        // console.log('Extracted scfId:', newScfId);
        // console.log('Extracted hisid:', newHisId);

        processClipboardContent(newScfId, newHisId);
      } else {
        // console.log('No match found for clipboard content:', clipboardText);
      }
    } catch (error) {
      console.error('Error reading clipboard:', error);
    }
  };

  const processClipboardContent = async (newScfId: string, newHisId: string) => {
    // console.log('processClipboardContent called with scfId:', newScfId, 'and hisId:', newHisId);
    const collectionId = await getCurlCollectionNodeId();
    setParentPath(collectionId);

    // 确保 parentPath 已经更新后再调用 getRequestId
    const rowData = await getRequestId(newHisId, newScfId, collectionId);
    // console.log('rowData:', rowData);
    const methodName = await getScfDetailByScfId(newScfId);
    // console.log("methodName is %%%", methodName);

    if (rowData) {
      navPane({
        type: `request`,
        id: `${activeWorkspaceId}-1-${rowData}`,
       // name: methodName,
      });
    }
  };

  const getCurlCollectionNodeId = async () => {
    // console.log('getCurlCollectionNodeId called');
    const res = await queryWorkspaceById({ id: activeWorkspaceId });
    // console.log('queryWorkspaceById response:', res);
    for (let i = 0; i < res.roots.length; i++) {
      if (res.roots[i].nodeName === 'ScfImport') {
        // console.log('Found ScfImport node with infoId:', res.roots[i].infoId);
        return res.roots[i].infoId;
      }
    }
    const addCollectionReq: AddCollectionReq = {
      id: activeWorkspaceId,
      userName: getLocalStorage<string>(EMAIL_KEY) || '',
      nodeName: 'ScfImport',
      nodeType: 3,
      parentPath: [],
    };
    const res2 = await addCollectionItem(addCollectionReq);
    // console.log('addCollectionItem response:', res2);
    return res2.infoId;
  };

  const getRequestId = async (hisId: string, scfId: string, collectionId: string) => {
    // console.log('getRequestId called with hisId:', hisId, 'scfId:', scfId, 'collectionId:', collectionId);
    const requestBody = {
      historyId: hisId,
      scfId: scfId,
      id: 0,
      workspaceId: activeWorkspaceId,
      parentPath: [collectionId],
    };

    const accessToken = getLocalStorage<string>('accessToken');
    const appId = getLocalStorage<string>('appId');
    try {
      const response = await axios.post(
        `/report/filesystem/addItemAndSaveHistoryInterface`,
        requestBody,
        {
          headers: {
            'access-token': accessToken,
            appId,
          },
        },
      );
      // console.log('Response from addItemAndSaveHistoryInterface:', response.data);
      // console.log('response&&&', response.data.body.infoId);
      return response.data.body.infoId;
    } catch (error) {
      console.error('Error in getRequestId:', error);
      return null;
    }
  };

  const getScfDetailByScfId = async (scfId: string): Promise<string | null> => {
    // console.log('getScfDetailByScfId called with scfId:', scfId);
    if (!scfId) {
      console.error('Invalid scfId');
      return null;
    }

    try {
      const response = await axios.get(
        `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}`
      );
      // console.log('Response from getScfDetailByScfIdV2:', response.data);
      // console.log("method name is", response.data.data.method);
      return response.data.data.method;
    } catch (error) {
      console.error('Error fetching SCF detail:', error);
      return 'Untitled';
    }
  };

  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (!document.hidden) { // 当页面重新变得可见时
        if (!document.hasFocus()) {
          console.warn('Document does not have focus. Cannot read clipboard.');
          return;
        }

        try {
          const clipboardText = await navigator.clipboard.readText();
          // console.log('Clipboard content on visibility change:', clipboardText);

          const lastClipboardContent = getLocalStorage('lastClipboardContent');
          if (clipboardText !== lastClipboardContent) {
            setLocalStorage('lastClipboardContent', clipboardText);

            const regex1 = /^https:\/\/iapi\.58corp\.com\/page\/front\/scfManager\/api\/scfTest\/(\d+)(?:\/(\d+))?$/;

            const match = clipboardText.match(regex1);

            if (match) {
              const newScfId = match[1];
              const newHisId = match[2] ? match[2] : '0'; // 如果有第二个捕获组，则使用它，否则默认为 '0'
              setScfId(newScfId);
              setHisId(newHisId);
              // console.log('Extracted scfId:', newScfId);
              // console.log('Extracted hisid:', newHisId);

              processClipboardContent(newScfId, newHisId);
            } else {
              // console.log('No match found for clipboard content:', clipboardText);
            }
          } else {
            // console.log('Clipboard content has not changed.');
          }
        } catch (error) {
          console.error('Error reading clipboard:', error);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理函数，移除事件监听器
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []); // 空数组确保该效果仅在组件挂载和卸载时运行

  return (
    <Button
      shape="circle"
      icon={<CopyOutlined />}
      onClick={readClipboard}
      style={{ width: 40, height: 40, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
    />
  );
};

export default ClipboardApp;
