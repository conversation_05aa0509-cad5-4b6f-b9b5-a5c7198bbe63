// import { words } from 'shellwords';
import * as words from 'shellwords';

// TODO -F, --form
// TODO --data-binary
// TODO --data-urlencode
// TODO -r, --range

interface Output {
  method: string;
  url?: string;
  header?: any;
  body?: any;
  query?: any;
  search?: string;
}

/**
 * Rewrite args for special cases such as -XPUT.
 */

const rewrite = (args: string[]) => {
  return args.reduce((arg: string[], a) => {
    if (a.indexOf('-X') === 0) {
      arg.push('-X');
      arg.push(a.slice(2));
    } else {
      arg.push(a);
    }
    return arg;
  }, []);
};

/**
 * Parse header field.
 */

const parseField = (s: string) => {
  // return s.split(/: (.+)/);
  const match = s.match(/^([^:]+): (.*)$/);
  // console.log(match);
  if (match) {
    return [match[1], match[2]];
  } else {
    // 返回错误或默认值，根据你的需求
    throw new Error('Invalid format');
    return s;
  }
};

/**
 * Check if `s` looks like a url.
 */

const isURL = (s: string) => {
  // TODO: others at some point
  return /^https?:\/\//.test(s);
};

/**
 * Parse query or data-raw
 */

const parseKeyValue = (s: string) => {
  const keyValue: string[][] = [];
  if (s?.length > 0) {
    const decodeStr = decodeURI(s.trim());
    const fields = decodeStr.split('&');
    fields?.forEach((item) => {
      const [key, value] = item.split('=');
      if (key) {
        keyValue.push([key, value ? decodeURIComponent(value) : '']);
      }
    });
  }
  return keyValue;
};

/**
 * Parse Url
 */
const parseUrl = (s: string): any => {
  // const [url, search] = s.split('?'); // 因为query中也可能带?号（json中的），所以要以第一个?为分界点
  const firstQuestionMark = s.indexOf('?');
  let url, search;
  if (firstQuestionMark > 0) {
    url = s.slice(0, firstQuestionMark);
    search = s.slice(firstQuestionMark + 1);
  } else {
    // 没有search的情况
    url = s;
    search = '';
  }

  return [url, parseKeyValue(search), search ? `?${search}` : ''];
};

/**
 * Attempt to parse the given curl string.
 */

export default (s: string): Output | undefined => {
  const str = s.trim();
  if (str.indexOf('curl ') !== 0) return undefined;
  let args;
  // 修复嵌套JSON字符串中的转义字符
  const fixNestedJsonString = (jsonStr: string): string => {
    // shellwords.split会不一致地处理转义字符，需要统一修复
    // 例如：{"extend":"{"insertOrder":10}"} 中 insertOrder 前面的引号没有\，后面的有\
    return jsonStr.replace(/"([^"]+)":\s*"\{([^}]+)\}"/g, (match, key, innerJson) => {
      // 移除所有现有的转义符，然后重新添加
      const cleanInnerJson = innerJson.replace(/\\"/g, '"');
      // 重新为所有双引号添加转义符
      const escapedInnerJson = cleanInnerJson.replace(/"/g, '\\"');
      return `"${key}":"{${escapedInnerJson}}"`;
    });
  };

  try {
    args = rewrite(words.split(str));

    // 修复JSON字符串中的转义字符问题
    // shellwords库会移除转义字符，需要特殊处理JSON字符串
    args = args.map(arg => {
      // 检查是否可能是JSON字符串
      if ((arg.startsWith('{') && arg.endsWith('}')) || (arg.startsWith('[') && arg.endsWith(']'))) {
        try {
          // 尝试解析JSON，如果成功说明格式正确
          JSON.parse(arg);
          return arg;
        } catch (e) {
          // 如果解析失败，尝试修复转义字符
          console.log('原始参数:', arg);

          // 特殊处理用户提供的例子
          // 例如：{"metricsList":[{"extend":"{"insertOrder":10}"}]}
          // 应该修复为：{"metricsList":[{"extend":"{\"insertOrder\":10}"}]}
          const fixedArg = fixNestedJsonString(arg);

          console.log('修复后参数:', fixedArg);

          // 尝试再次解析
          try {
            JSON.parse(fixedArg);
            return fixedArg;
          } catch (e2) {
            // 如果仍然失败，返回原始参数
            console.log('修复失败，返回原始参数');
            return arg;
          }
        }
      }
      return arg;
    });
    // console.log(args);
  } catch (error) {
    console.log("checkout your curl, make sure It's running ok on Shell instead of DOC.", error);
    return undefined;
  }

  const out: Output = { method: 'GET', header: {} };
  let state = '';
  let headerKey;
  let headerValue;

  console.log('parseCurl args', args);

  args.forEach((ar) => {
    const arg = ar.toLowerCase();
    switch (true) {
      case isURL(arg):
        [out.url, out.query, out.search] = parseUrl(ar);
        break;

      case arg === '-A'.toLowerCase() || arg === '--user-agent':
        state = 'user-agent';
        break;

      case arg === '-H'.toLowerCase() || arg === '--header' || arg === ' --header':
        state = 'header';
        break;

      case arg === '-d' || arg === '--data' || arg === '--data-ascii' || arg === ' --data':
        state = 'data';
        break;

      case arg === '--data-raw':
        state = 'data-raw';
        break;

      case arg === '--data-binary':
        state = 'data-binary';
        break;

      case arg === '-u' || arg === '--user':
        state = 'user';
        break;

      case arg === '-I'.toLowerCase() || arg === '--head':
        out.method = 'HEAD';
        break;

      case arg === '-X'.toLowerCase() || arg === '--request':
        state = 'method';
        break;

      case arg === '-b' || arg === '--cookie':
        state = 'cookie';
        break;

      case arg === '--compressed':
        out.header['Accept-Encoding'] = out.header['Accept-Encoding'] || 'deflate, gzip';
        break;

      case !!arg:
        switch (state) {
          case 'header':
            [headerKey, headerValue] = parseField(ar);
            // 这里要转下 headerKey 的格式，全部转为 Xxxx-Xxxx 首字母大写的格式
            headerKey = headerKey
              .split('-')
              .map((it) => it.slice(0, 1).toUpperCase() + it.slice(1).toLowerCase())
              .join('-');
            out.header[headerKey] = headerValue;
            state = '';
            break;
          case 'user-agent':
            out.header['User-Agent'] = ar;
            state = '';
            break;
          case 'data':
            if (out.method === 'GET' || out.method === 'HEAD') {
              out.method = 'POST';
            }
            out.header['Content-Type'] =
              out.header['Content-Type'] || 'application/x-www-form-urlencoded';
            out.body = out.body ? `${out.body}&${ar}` : ar;
            state = '';
            break;
          case 'data-raw': {
            if (out.method === 'GET' || out.method === 'HEAD') {
              out.method = 'POST';
            }
            out.header['Content-Type'] =
              out.header['Content-Type'] || 'application/x-www-form-urlencoded';

            // 处理 $'...' 格式的字符串
            let processedArg = ar;
            console.log('processedArg', ar);
            if (processedArg.startsWith('${') && processedArg.endsWith('}')) {
              // 移除 $' 前缀和 ' 后缀
              processedArg = processedArg.substring(1, processedArg.length);
              // 处理转义字符
              processedArg = processedArg.replace(/\\([\\"'nrt])/g, (match, p1) => {
                const escapeMap: { [key: string]: string } = {
                  '\\': '\\',
                  "'": "'",
                  '"': '"',
                  'n': '\n',
                  'r': '\r',
                  't': '\t'
                };
                return escapeMap[p1] || match;
              });
            }

            console.log('data-raw1', out.body);
            out.body = out.body ? `${out.body}&${processedArg}` : processedArg;
            console.log('data-raw2', out.body);
            // if (out.header['Content-Type'].indexOf('application/x-www-form-urlencoded') >= 0) {
            //   out.body = out.body ? [...out.body, ...parseKeyValue(ar)] : parseKeyValue(ar);
            // }
            // if (out.header['Content-Type'].indexOf('application/json') >= 0) {
            //   out.body = ar;
            // }
            // if (out.header['Content-Type'].indexOf('text/plain') >= 0) {
            //   out.body = ar;
            // }
            state = '';
            break;
          }
          case 'data-binary':
            if (out.method === 'GET' || out.method === 'HEAD') {
              out.method = 'POST';
            }
            out.header['Content-Type'] =
              out.header['Content-Type'] || 'application/x-www-form-urlencoded';
            if (out.header['Content-Type'].indexOf('application/json') >= 0) {
              out.body = out.body ? `${out.body}&${ar}` : ar;
              // out.body = ar;
            }
            if (out.header['Content-Type'].indexOf('text/plain') >= 0) {
              out.body = out.body ? `${out.body}&${ar}` : ar;
              // out.body = ar;
            }
            state = '';
            break;

          case 'user':
            out.header.Authorization = `Basic ${btoa(ar)}`;
            state = '';
            break;
          case 'method':
            out.method = ar;
            state = '';
            break;
          case 'cookie':
            out.header['Cookie'] = ar;
            state = '';
            break;
          default:
            break;
        }
        break;
      default:
        break;
    }
  });
  return out;
};
