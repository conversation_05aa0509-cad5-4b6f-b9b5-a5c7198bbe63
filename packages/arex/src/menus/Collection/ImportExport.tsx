import { getLocal<PERSON>torage, RequestMethod, useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { Button, Divider, Input, message, Modal, Radio, RadioChangeEvent, Spin } from 'antd';
import { FC, useState, useTransition } from 'react';
import { useParams } from 'react-router-dom';

import { CollectionNodeType, EMAIL_KEY, PanesType } from '@/constant';
import { useNavPane } from '@/hooks';
import {
  addCollectionItem,
  AddCollectionReq,
  Address,
  queryWorkspaceById,
  saveInterface,
  SaveInterfaceReq,
} from '@/services/FileSystemService';
import { exportCollection } from '@/services/FileSystemService/collection/exportCollection';
import { importCollection } from '@/services/FileSystemService/collection/importCollection';
import { useCollections } from '@/store';

import useWorkspaces from '../../store/useWorkspaces';
import parseCurl from './parseCurl';
interface CollectionsProps {
  show: boolean;
  onHideModal: () => void;
}
function download(content: string, filename: string) {
  // 创建a标签
  const eleLink = document.createElement('a');
  // 设置a标签 download 属性，以及文件名
  eleLink.download = filename;
  // a标签不显示
  eleLink.style.display = 'none';
  // 获取字符内容，转为blob地址
  const blob = new Blob([content]);
  // blob地址转为URL
  eleLink.href = URL.createObjectURL(blob);
  // a标签添加到body
  document.body.appendChild(eleLink);
  // 触发a标签点击事件，触发下载
  eleLink.click();
  // a标签从body移除
  document.body.removeChild(eleLink);
}
const Collections: FC<CollectionsProps> = ({ show, onHideModal }) => {
  const { getCollections } = useCollections();
  const { activeWorkspaceId } = useWorkspaces();
  const [fileString, setFileString] = useState('');
  const { t } = useTranslation(['components']);
  const navPane = useNavPane();
  const [curl, setCurl] = useState('');

  async function getCurlCollectionNodeId() {
    // console.log("==========进来了======");

    const res = await queryWorkspaceById({ id: activeWorkspaceId });
    for (let i = 0; i < res.roots.length; i++) {
      if (res.roots[i].nodeName === 'cURl_Import') {
        // console.log("===已有的cURLCollectionId===");
        // console.log(res.roots[i].infoId);
        return res.roots[i].infoId;
      }
    }
    const addCollectionReq: AddCollectionReq = {
      id: activeWorkspaceId,
      userName: getLocalStorage<string>(EMAIL_KEY) || '',
      nodeName: 'cURl_Import',
      nodeType: 3,
      parentPath: [],
    };
    // console.log("addCollectionReq ====" + addCollectionReq.id);
    const res2 = await addCollectionItem(addCollectionReq);
    // console.log("===新建的cURLCollectionId===");
    // console.log(res2.infoId);
    return res2.infoId;
  }

  async function getCurlNewRequestNodeId() {
    const patPath = await getCurlCollectionNodeId();
    const addRequestReq: AddCollectionReq = {
      id: activeWorkspaceId,
      userName: getLocalStorage<string>(EMAIL_KEY) || '',
      nodeName: 'New Request',
      nodeType: 1,
      parentPath: [patPath],
    };
    // console.log("parentPath====" + addRequestReq.parentPath);
    const res = await addCollectionItem(addRequestReq);
    // console.log("===cURL collection下新建的requestid===");
    // console.log(res.infoId);
    return res.infoId;
  }
  const submit = () => {
    setLoading(true);
    importCollection({
      workspaceId: activeWorkspaceId,
      type: importType,
      path: [],
      importString: fileString,
    })
      .then((res: any) => {
        message.success(t('workSpace.importSuccess'));
        setLoading(false);
        onHideModal();
        getCollections();
      })
      .catch((err) => {
        message.error(t('workSpace.importFailed'));
        setLoading(false);
      });
  };
  const [importType, setImportType] = useState(1);
  const [exportDisable, setExportDisable] = useState(false);

  const onChange = (e: RadioChangeEvent) => {
    setImportType(e.target.value);
    if (e.target.value == 3) {
      setExportDisable(true);
    } else {
      setExportDisable(false);
    }
  };
  const onCurlChange = (e: any) => {
    setCurl(e.target.value);
  };

  const onClick = (e: any) => {
    const result = parseCurl(curl);
    if (!result) {
      message.error('无法识别的Curl');
      return;
    }
    console.log('parseCurl result', result);

    // console.log(activeWorkspaceId);

    const urlStr = result?.search ? String(result.url) + String(result.search) : String(result.url);
    //method是readonly的，不能直接赋值
    let index = 0;
    for (let i = 0; i < RequestMethod.length; i++) {
      if (RequestMethod[i] === result.method) {
        index = i;
        break;
      }
    }
    // const adr: Address = {
    //   endpoint: urlStr,
    //   method: RequestMethod[index],
    // };

    // console.log( adr);

    const params = result?.query
      ? result?.query?.map(([key, value]: any) => ({ key, value, active: true }))
      : '';
    // console.log( params);

    const headers: { key: string; value: any; active: boolean }[] = [];
    if (result?.header) {
      const keys = Object.keys(result?.header);
      if (keys?.length > 0) {
        keys.forEach((item) => {
          headers.push({
            // id: String(Math.random()),
            key: item,
            value: result?.header[item],
            active: true,
          });
        });
      }
    }
    // console.log( headers);

    let interfaceBody = {};
    if (result?.body) {
      interfaceBody = {
        contentType: 'application/json',
        body: result?.body,
        wmb: null,
      };
    }

    getCurlNewRequestNodeId().then((requestId) => {
      const saveInterfaceReq: SaveInterfaceReq = {
        address: {
          method: RequestMethod[index],
          requestType: 'HTTP',
          endpoint: urlStr,
        },
        body: interfaceBody,
        headers: headers,
        params: params,
        description: null,
        id: requestId,
        workspaceId: activeWorkspaceId,
        labelIds: [],
      };
      // console.log("==========saveInterfaceReq=============");
      // console.log(saveInterfaceReq);
      saveInterface(saveInterfaceReq).then((success) => {
        if (success) {
          navPane({
            type: PanesType.REQUEST,
            id: `${activeWorkspaceId}-${CollectionNodeType.interface}-${requestId}`,
            icon: result.method,
            name: 'Untitled',
          });
          onHideModal();
          message.success('导入成功');
        }
        success && getCollections();
      });
    });
  };

  const [loading, setLoading] = useState(false);

  return (
    <Modal
      title={t('collection.import_export')}
      width={400}
      open={show}
      onCancel={onHideModal}
      footer={false}
    >
      <Spin spinning={loading}>
        <div
          css={css`
            display: flex;
            flex-direction: column;
            padding-top: 10px;
          `}
        >
          <Radio.Group
            onChange={onChange}
            value={importType}
            css={css`
              margin-bottom: 10px;
            `}
          >
            <Radio value={1}>AREX</Radio>
            <Radio value={2}>Postman</Radio>
            <Radio value={3}>书签</Radio>
            <Radio value={4}>cURL</Radio>
          </Radio.Group>
          {importType == 4 ? (
            <>
              <Input id='cURL' placeholder='Paste cURL' onChange={onCurlChange}></Input>
              <Button key='submit' type='primary' onClick={onClick}>
                OK
              </Button>
            </>
          ) : (
            <>
              <input
                css={css`
                  margin-bottom: 20px;
                `}
                type={'file'}
                onChange={(event) => {
                  if (event.target.files !== null) {
                    event.target.files[0].text().then((res) => {
                      setFileString(res);
                    });
                  }
                }}
              />
              <Button
                type={'primary'}
                onClick={() => {
                  submit();
                }}
              >
                {t('workSpace.import')}
              </Button>
              <Divider
                css={css`
                  margin: 10px 0;
                `}
              />
              <Button
                disabled={exportDisable}
                onClick={() => {
                  // 暂只支持arex导出
                  exportCollection({
                    workspaceId: activeWorkspaceId,
                    type: importType,
                    path: [],
                  }).then((res) => {
                    download(res, `${activeWorkspaceId}.json`);
                  });
                }}
              >
                {t('collection.export')}
              </Button>
            </>
          )}
        </div>
      </Spin>
    </Modal>
  );
};

export default Collections;
