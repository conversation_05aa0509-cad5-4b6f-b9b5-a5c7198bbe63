import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  ArexMenuContainer,
  ArexMenuContainerProps,
  ArexPanesContainer,
  ArexPanesContainerProps,
  useTranslation,
} from '@arextest/arex-core';
import { css } from '@emotion/react';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { Allotment } from 'allotment';
import { Button, Flex, MenuProps, Tooltip } from 'antd';
import React, { FC } from 'react';

import {
  EmptyPanePlaceholder,
  FooterExtraMenu,
  Icon,
  KeyboardShortcut,
  MacTrafficLightBackground,
  UserMenu,
} from '@/components';
import { CollectionNodeType, isClient, PanesType, URL_AREX } from '@/constant';
import { useCheckChrome, useInit, useNavPane } from '@/hooks';
import ClipboardApp from '@/menus/Collection/Clipboard';
import { useMenusPanes, useUserProfile, useWorkspaces } from '@/store';
import { generateId } from '@/utils';

const Home: FC = () => {
  useCheckChrome();
  useInit();

  const {
    menuCollapsed,
    toggleMenuCollapse,
    activeMenu,
    setActiveMenu,
    panes,
    setPanes,
    switchPane,
    removeSegmentPanes,
    activePane,
    setActivePane,
    reset: resetPane,
    removePane,
  } = useMenusPanes();
  const { activeWorkspaceId } = useWorkspaces();
  const { zen } = useUserProfile();

  const navPane = useNavPane();
  const [arexMainWrapperRef] = useAutoAnimate();

  const { t } = useTranslation(['components', 'common']);

  const dropdownItems: MenuProps['items'] = [
    {
      label: t('dropdownMenu.close'),
      key: 'close',
    },
    {
      label: t('dropdownMenu.closeOther'),
      key: 'closeOther',
    },
    {
      label: t('dropdownMenu.closeAll'),
      key: 'closeAll',
    },
    {
      label: t('dropdownMenu.closeLeft'),
      key: 'closeLeft',
    },
    {
      label: t('dropdownMenu.closeRight'),
      key: 'closeRight',
    },
  ];

  const handleMenuChange = (menuType: string) => {
    //console.log("onlick to  test  clickboard*******************", navigator.clipboard.readText());
    menuCollapsed && toggleMenuCollapse(false);
    setActiveMenu(menuType);
  };

  const handleMenuSelect: ArexMenuContainerProps['onSelect'] = (type, id, data) => {
    navPane({
      id,
      type,
      data,
    });
  };

  const handlePaneAdd: ArexPanesContainerProps['onAdd'] = () => {
    navPane({
      type: PanesType.REQUEST,
      id: `${activeWorkspaceId}-${CollectionNodeType.interface}-${generateId(12)}`,
      icon: 'Get',
      name: 'Untitled',
    });
  };

  const handleDropdownClick = (e: { key: string }, key: React.Key | null) => {
    if (!key) return;
    const paneKey = key.toString();

    switch (e.key) {
      case 'close': {
        removePane(undefined);
        break;
      }
      case 'closeOther': {
        const pane = panes.find((pane) => pane.key === paneKey);
        if (pane) setPanes([pane]);
        break;
      }
      case 'closeAll': {
        resetPane();
        break;
      }
      case 'closeLeft': {
        removeSegmentPanes(paneKey, 'left');
        break;
      }
      case 'closeRight': {
        removeSegmentPanes(paneKey, 'right');
        break;
      }
    }
  };

  const handleDragEnd: ArexPanesContainerProps['onDragEnd'] = ({ active, over }) => {
    if (active?.id && over?.id && active.id !== over?.id) {
      switchPane(String(active.id), String(over.id));
    }
  };

  return (
    <div ref={arexMainWrapperRef}>
      {/* {!zen && <ArexHeader logo={{ href: URL_AREX }} extra={<UserMenu />} />} */}

      <Allotment
        css={css`
          height: ${zen ? '100vh' : 'calc(100vh - 30px)'};
        `}
      >
        <Allotment.Pane
          preferredSize={300}
          minSize={zen ? 0 : menuCollapsed ? 70 : 300}
          maxSize={zen ? 0 : menuCollapsed ? 70 : 600}
        >
          <ArexMenuContainer
            value={activePane?.id}
            activeKey={activeMenu}
            collapsed={menuCollapsed}
            onChange={handleMenuChange}
            onSelect={handleMenuSelect}
            header={<ArexHeader logo={{ href: URL_AREX }} extra={<UserMenu />} />}
          />
        </Allotment.Pane>
        <Allotment.Pane>
          <ArexPanesContainer
            height={`calc(100vh - ${zen ? 43 : 70}px)`}
            activeKey={activePane?.key}
            panes={panes}
            emptyNode={<EmptyPanePlaceholder />}
            dropdownMenu={{
              items: dropdownItems,
              onClick: handleDropdownClick,
            }}
            onDragEnd={handleDragEnd}
            onChange={setActivePane}
            onAdd={handlePaneAdd}
            onRemove={removePane}
          />
        </Allotment.Pane>
      </Allotment>
      {!zen && (
        <ArexFooter
          leftRender={(console) => (
            <Flex align='center' style={{ height: '100%' }}>
              <Tooltip
                placement={'topLeft'}
                title={t(menuCollapsed ? 'expandSidebar' : 'collapseSidebar', { ns: 'arex-menu' })}
              >
                <Button size='small' type='link' onClick={() => toggleMenuCollapse()}>
                  <Icon
                    name='PanelLeft'
                    style={{ transform: `rotate(${menuCollapsed ? 180 : 0}deg)` }}
                  />
                </Button>
              </Tooltip>
              {process.env.NODE_ENV === 'production' && console}
            </Flex>
          )}
          rightRender={(agent) => (
            <>
              {!isClient && agent}
              <FooterExtraMenu />
            </>
          )}
        />
      )}
      <MacTrafficLightBackground />
      <KeyboardShortcut />

      {/* ClipboardApp 组件容器 */}
      <div
        style={{
          position: 'fixed',
          bottom: 90,
          right: 25,
          display: 'flex',
          flexDirection: 'column',
          gap: 10,
          zIndex: 1000, // 确保悬浮按钮在其他内容之上
        }}
      >
        <ClipboardApp />
      </div>
    </div>
  );
};

export default Home;
