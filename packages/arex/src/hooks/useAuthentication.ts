import { clearLocalStorage, getLocalStorage, setLocalStorage } from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import axios from 'axios';
import { match } from 'path-to-regexp';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import {
  ACCESS_TOKEN_KEY,
  EMAIL_KEY,
  PRODUCT_HOSTS,
  REFRESH_TOKEN_KEY,
  WORKSPACE_STORAGE_KEY,
} from '@/constant';
import { FreePath } from '@/router';
import { UserService } from '@/services';
import { REDIRECT_58_PASSPORT } from '@/services/LoginService/constants';
import { CurrentUser } from '@/services/LoginService/data';
import { toLoginVerify58 } from '@/services/LoginService/getOauthClientId';
// checkout if the user is logged in

const useAuthentication = async () => {
  // console.log('useAuthentication start');
  const nav = useNavigate();
  const location = useLocation();

  const accessToken = getLocalStorage<string>(ACCESS_TOKEN_KEY);
  const email = getLocalStorage<string>(EMAIL_KEY);

  // console.log('useAuthentication', accessToken, email);

  const { run: loginAsGuest } = useRequest(
    () => UserService.loginAsGuest({ userName: getLocalStorage<string>(EMAIL_KEY) }),
    {
      manual: true,
      onSuccess(res) {
        // console.log('loginAsGuest.onSuccess', res);
        setLocalStorage(EMAIL_KEY, res.userName);
        setLocalStorage(ACCESS_TOKEN_KEY, res.accessToken);
        setLocalStorage(REFRESH_TOKEN_KEY, res.refreshToken);
        handleLoginSuccess(res.userName);
      },
    },
  );

  const handleLoginSuccess = (email?: string) => {
    // console.log('handleLoginSuccess start', email, getLocalStorage(EMAIL_KEY));
    const query = new URLSearchParams(location.search);
    const redirect = query.get('redirect');

    setLocalStorage(EMAIL_KEY, email);
    // console.log('handleLoginSuccess end', email, getLocalStorage(EMAIL_KEY));

    nav(redirect || '/');
  };

  const handleGotoLogin = (key: string) => {
    // key用于调试发生错误的节点
    console.log('handleGotoLogin', key);
    // window.location.href = `${REDIRECT_58_PASSPORT}&u=${encodeURIComponent(window.location.href)}`;
  };

  const handleTestLogin = () => {
    // console.log('handleTestLogin');
    // 测试环境login
    loginAsGuest();
  };

  const handleSetAccessToken = async (userName: string) => {
    // console.log('handleSetAccessToken');
    const verify58_res = await toLoginVerify58({ userName: userName });
    // console.log(verify58_res);
    if (verify58_res) {
      // console.log('handleSetAccessToken.verify58_res');
      setLocalStorage(ACCESS_TOKEN_KEY, verify58_res?.accessToken);
      setLocalStorage(REFRESH_TOKEN_KEY, verify58_res?.refreshToken);
      // 刷新页面
      window.location.reload();
    } else {
      handleGotoLogin('get access token error');
    }
  };

  const handleReleaseLogin = () => {
    // console.log('handleReleaseLogin');
    const url = `https://${window.location.hostname}/server/index.php?g=Web&c=User&o=getUserInfo`;
    axios
      .post(
        url,
        {},
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
        },
      )
      .then((response) => {
        const res = response.data;
        // console.log('handleReleaseLogin.then', res);
        if (res && res.statusCode && res.statusCode === '000000') {
          // console.log('handleReleaseLogin.then.if', res);
          const userInfo: CurrentUser = {
            realName: res.realName,
            userName: res.userInfo.userName,
            currentUserID: res.currentUserID,
          };
          setLocalStorage(EMAIL_KEY, res?.userInfo?.userName);
          handleSetAccessToken(userInfo.userName || '');
        } else {
          // console.log('handleReleaseLogin.then.else', res);
          handleGotoLogin('USERINFO');
        }
      })
      .catch(() => {
        handleGotoLogin(' catch GET USERINFO ERROR');
      });
  };

  useEffect(() => {
    // console.log('useAuthentication.useEffect start accessToken:', accessToken);
    // console.log('useAuthentication.useEffect start email:', email);

    const isFreePathname = FreePath.some((path) => {
      const fn = match(path, { decode: decodeURIComponent });
      return fn(location.pathname);
    });

    const hasLoginInfo = accessToken && email;
    const isRelease = PRODUCT_HOSTS.includes(window.location.hostname);

    // 未登录
    if (!isFreePathname && !hasLoginInfo) {
      // console.log('useAuthentication.useEffect#!isFreePathname && !hasLoginInfo');
      console.warn('没有登陆信息');
      if (!isRelease) {
        // console.log('useAuthentication.useEffect#!isFreePathname#!isRelease && !hasLoginInfo');
        //非线上域名，本地调试
        // console.log(window.location.hostname, '登陆guest账号');
        handleTestLogin();
      } else if (isRelease) {
        // console.log('useAuthentication.useEffect#!isFreePathname#isRelease && !hasLoginInfo');
        handleReleaseLogin();
      }
    } else {
      // console.log('useAuthentication.useEffect#else');
      if (String(getLocalStorage<string>(EMAIL_KEY)).startsWith('GUEST_') && isRelease) {
        // console.log('登陆了guest，需清除');
        clearLocalStorage(EMAIL_KEY);
        clearLocalStorage(ACCESS_TOKEN_KEY);
        clearLocalStorage(REFRESH_TOKEN_KEY);
        clearLocalStorage(WORKSPACE_STORAGE_KEY);
        handleReleaseLogin();
      }
    }
  }, []);
};

export default useAuthentication;
