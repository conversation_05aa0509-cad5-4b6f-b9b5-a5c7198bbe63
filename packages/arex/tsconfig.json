{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "jsxImportSource": "@emotion/react", "baseUrl": ".", "paths": {"@": ["src"], "@/*": ["src/*"], "@arextest/arex-core/dist": ["../arex-core/src"], "@arextest/arex-core": ["../arex-core/src"], "@arextest/arex-common": ["../arex-common/src"], "@arextest/arex-request": ["../arex-request/src"]}}, "include": ["src", "electron"], "references": [{"path": "./tsconfig.node.json"}]}