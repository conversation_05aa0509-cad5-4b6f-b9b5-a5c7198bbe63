{"name": "@arextest/arex-core", "version": "0.4.1", "homepage": "https://github.com/arextest/arex", "main": "dist/arex-core.js", "module": "dist/arex-core.js", "type": "module", "scripts": {"build:code": "vite build", "build:decl": "tsc --project tsconfig.decl.json", "do-build": "pnpm run build:code && pnpm run build:decl"}, "types": "dist/index.d.ts", "files": ["dist/*"], "dependencies": {"@ant-design/icons": "^5.2.6", "@arextest/vanilla-jsoneditor": "0.21.4-beta.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@emotion/serialize": "^1.1.1", "@formkit/auto-animate": "^0.7.0", "@hpcc-js/wasm": "^2.14.1", "ahooks": "^3.7.5", "dayjs": "^1.11.7", "diff-match-patch": "^1.0.5", "i18next": "^22.4.11", "immer": "^9.0.19", "js-base64": "^3.7.5", "lodash": "^4.17.21", "lossless-json": "^4.0.1", "path-to-regexp": "^6.2.1", "qs": "^6.11.1", "react-i18next": "^12.2.0", "react-transition-group": "^4.4.5", "use-immer": "^0.8.1", "vconsole": "^3.15.0"}, "devDependencies": {"@types/diff-match-patch": "^1.0.32", "@types/lodash": "^4.14.194", "@types/qs": "^6.9.7", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-transition-group": "^4.4.6", "@vitejs/plugin-react-swc": "^3.3.0", "typescript": "^5.3.0", "vite": "^4.2.0", "vite-plugin-svgr": "^3.2.0"}, "peerDependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "antd": "^5.11.0", "react": "^18.2.0", "react-dom": "^18.2.0"}}