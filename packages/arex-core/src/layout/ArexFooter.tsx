import { CodeOutlined } from '@ant-design/icons';
import styled from '@emotion/styled';
import type { RadioChangeEvent } from 'antd';
import { Button, Form, Modal, Radio, Space, Switch, theme, Tooltip, Typography } from 'antd';
import React, { FC, ReactNode, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import VConsole from 'vconsole';

import { CheckOrCloseIcon, SmallTextButton } from '../components';
import { AGENT_TYPE, SYSTEM_AUTO } from '../constant';
import { useArexCoreConfig } from '../hooks';
import { getLocalStorage, setLocalStorage } from '../utils';
import { clearLocalStorage } from '../utils';
const { Text } = Typography;

const FooterWrapper = styled.div`
  height: 26px;
  width: 100%;
  padding: 0 8px;
  display: flex;
  justify-content: space-between;
  z-index: 1000;
  border-top: 1px solid ${(props) => props.theme.colorBorder};
  .ant-typography {
    line-height: 24px;
    font-size: 12px;
  }
  .ant-btn-icon {
    font-size: 12px;
    margin-inline-end: 6px;
  }
  .ant-btn-link {
    .ant-typography-secondary {
      transition: color 0.25s ease;
      &:hover {
        color: ${(props) => props.theme.colorTextSecondary};
      }
    }
  }
`;

const Console: FC = () => {
  const vConsole = useRef<VConsole>();
  const { theme } = useArexCoreConfig();
  const { t } = useTranslation();

  useEffect(() => {
    vConsole.current = new VConsole({
      theme,
      log: {
        showTimestamps: true,
      },
      defaultPlugins: [],
      pluginOrder: ['logs'],
    });
    vConsole.current.hideSwitch();

    return () => vConsole.current?.destroy();
  }, []);

  return (
    <Tooltip title={t('console')}>
      <Button
        id='arex-console-btn'
        size='small'
        type='link'
        onClick={() => vConsole.current?.show()}
      >
        <CodeOutlined />
      </Button>
    </Tooltip>
  );
};

const Agent: FC = () => {
  const { t } = useTranslation();
  const { token } = theme.useToken();
  const [open, setOpen] = useState(false);
  // const [loading, setLoading] = useState(false);
  const [disabledRadio, setDisabledRadio] = useState(
    typeof getLocalStorage(SYSTEM_AUTO) === 'boolean' ? getLocalStorage(SYSTEM_AUTO) : true,
  );
  const [disabledRadioChrome, setDisabledRadioChrome] = useState(
    disabledRadio || !window.__AREX_EXTENSION_INSTALLED__,
  );
  const showModal = () => {
    setOpen(true);
  };
  const handleCancel = () => {
    setOpen(false);
  };
  // const handleOk = () => {
  //   setLoading(true);
  //   setTimeout(() => {
  //     setLoading(false);
  //     setOpen(false);
  //   }, 3000);
  // };
  const onChangeSwitch = (checked: boolean) => {
    if (window.__AREX_EXTENSION_INSTALLED__) {
      setDisabledRadioChrome(!disabledRadioChrome);
    }
    console.log(`switch to ${checked}`);
    setLocalStorage(SYSTEM_AUTO, checked);
    setDisabledRadio(!disabledRadio);
    if (getLocalStorage(SYSTEM_AUTO) == true) {
      clearLocalStorage(AGENT_TYPE);
      setLocalStorage(AGENT_TYPE, 'system_auto');
    }
  };

  const [value, setValue] = useState(1);
  const onChangeRadio = (e: RadioChangeEvent) => {
    console.log('radio checked', e.target.value);
    setValue(e.target.value);
    setLocalStorage(AGENT_TYPE, e.target.value);
  };

  return (
    <>
      <SmallTextButton
        type='link'
        // Change 'color' prop to 'textType'
        textType='secondary'
        title={t('browserAgent')}
        icon={<CheckOrCloseIcon size={12} checked={window.__AREX_EXTENSION_INSTALLED__} />}
        onClick={showModal}
      ></SmallTextButton>
      <Modal
        open={open}
        title='选择接口请求Agent'
        onCancel={handleCancel}
        footer={
          [
            // <Button key="back" onClick={handleCancel}>
            //     Return
            // </Button>,
            // <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
            //     Submit
            // </Button>
          ]
        }
      >
        <Form>
          <br></br>
          <Text>
            自动选择
            <Switch
              defaultChecked
              size='default'
              onChange={onChangeSwitch}
              value={getLocalStorage(SYSTEM_AUTO)}
              style={{ marginLeft: '300px' }}
            ></Switch>
          </Text>
          <br></br>
          <span style={{ color: 'gray' }}>系统将自动为您的请求选择最佳Agent</span>
          <br></br>
          <br></br>
          <Radio.Group
            disabled={disabledRadio}
            onChange={onChangeRadio}
            value={getLocalStorage(AGENT_TYPE)}
          >
            <Space direction='vertical'>
              <Radio disabled={disabledRadioChrome} value='chrome_agent'>
                浏览器扩展
              </Radio>
              <Text style={{ color: 'gray' }}>
                通过浏览器扩展发送请求，
                <span style={{ color: token.colorPrimary, fontWeight: 'bold' }}>注意：</span>
                有调用数据库/本地代码、发送Cookie、部分header等限制
                <br></br>
                未安装 Google Chrome 扩展
                <Button
                  ghost
                  shape='round'
                  type='primary'
                  size='small'
                  background-color='blue'
                  href='https://chromewebstore.google.com/detail/arex-chrome-extension/jmmficadjneeekafmnheppeoehlgjdjj'
                  target='_blank'
                  style={{ marginTop: '8px', marginLeft: '10px' }}
                >
                  安装
                </Button>
              </Text>
              <br></br>
              <br></br>
              <Radio value='iapi_agent'>云端 Agent</Radio>
              <Text style={{ color: 'gray' }}>
                通过云端服务器发送请求，
                <span style={{ color: token.colorPrimary, fontWeight: 'bold' }}>注意：</span>
                不能访问内网
              </Text>
            </Space>
          </Radio.Group>
        </Form>
      </Modal>
    </>
  );
};

export type ArexFooterProps = {
  leftRender?: (console: React.ReactNode) => React.ReactNode;
  rightRender?: (agent: React.ReactNode) => React.ReactNode;
};

const ArexFooter: FC<ArexFooterProps> = (props) => {
  const { leftRender = (console) => console, rightRender = (agent) => agent } = props;

  return (
    <FooterWrapper>
      {/* left */}
      <div>{leftRender(<Console />)}</div>

      {/* right */}
      <div>{rightRender(<Agent />)}</div>
    </FooterWrapper>
  );
};

export default ArexFooter;
