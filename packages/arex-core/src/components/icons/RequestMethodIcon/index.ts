import { QuestionOutlined } from '@ant-design/icons';
import { FC, ReactElement } from 'react';

import ArexIcon from './ArexIcon';
import CaseIcon from './CaseIcon';
import DeleteIcon from './DeleteIcon';
import GetIcon from './GetIcon';
import PatchIcon from './PatchIcon';
import PostIcon from './PostIcon';
import PutIcon from './PutIcon';
import ScfIcon from './ScfIcon';
import WmbIcon from './WmbIcon';
import WMB_MOCKIcon from './WMB_MOCKIcon';

const RequestMethodIcon: { [method: string]: FC } = {
  scf: ScfIcon,
  Scf: ScfIcon,
  SCF: ScfIcon,

  get: GetIcon,
  Get: GetIcon,
  GET: GetIcon,

  post: PostIcon,
  Post: PostIcon,
  POST: PostIcon,

  put: PutIcon,
  Put: PutIcon,
  PUT: PutIcon,

  delete: DeleteIcon,
  Delete: DeleteIcon,
  DELETE: DeleteIcon,

  patch: PatchIcon,
  Patch: PatchIcon,
  PATCH: PatchIcon,

  wmb: WmbIcon,
  Wmb: WmbIcon,
  WMB: WmbIcon,

  wmb_mock: WMB_MOCKIcon,
  Wmb_mock: WMB_MOCKIcon,
  WMB_MOCK: WMB_MOCKIcon,

  arex: ArexIcon,
  Arex: ArexIcon,
  AREX: ArexIcon,

  case: CaseIcon,
  Case: CaseIcon,
  CASE: CaseIcon,

  unknown: QuestionOutlined,
  Unknown: QuestionOutlined,
  UNKNOWN: QuestionOutlined,
};

export default RequestMethodIcon;
