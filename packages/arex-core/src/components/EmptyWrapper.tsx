import { Card, Empty, EmptyProps } from 'antd';
import React, { FC } from 'react'; // Removed FunctionComponent import as it's not needed with JSX

import FlexCenterWrapper from './FlexCenterWrapper';
import { FullHeightSpin } from './index';

export type EmptyWrapperProps = {
  empty?: boolean;
  loading?: boolean;
  bordered?: boolean;
  loadingTip?: React.ReactNode;
  className?: string; // Added className explicitly as it's used
} & EmptyProps;

const EmptyWrapper: FC<EmptyWrapperProps> = (props) => {
  const {
    empty = true,
    loading = false,
    bordered = false,
    loadingTip,
    children,
    className, // Destructure className
    ...emptyProps
  } = props;
  return (
    <FullHeightSpin spinning={loading} tip={loadingTip} className={className}>
      {empty ? (
        bordered ? (
          // Render Card when bordered is true
          <Card bordered>
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} {...emptyProps} />
          </Card>
        ) : (
          // Render FlexCenterWrapper when bordered is false
          <FlexCenterWrapper>
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} {...emptyProps} />
          </FlexCenterWrapper>
        )
      ) : (
        <>{children}</>
      )}
    </FullHeightSpin>
  );
};

export default EmptyWrapper;
