import { Button, ButtonProps, theme, Tooltip, TooltipProps, Typography } from 'antd';
import { Breakpoint } from 'antd/es/_util/responsiveObserver';
import useBreakpoint from 'antd/es/grid/hooks/useBreakpoint';
import { TextProps } from 'antd/es/typography/Text';
import React, { FC, ReactNode, useMemo } from 'react';

export interface TooltipButtonProps extends Omit<ButtonProps, 'title'> {
  title?: ReactNode;
  placement?: TooltipProps['placement'];
  tooltipProps?: TooltipProps;
  textProps?: TextProps;
  breakpoint?: Breakpoint;
  // Rename 'color' to 'textColor'
  textColor?: 'primary' | 'text' | 'secondary' | 'disabled' | string;
  onClick?: (newParams: any) => void;
}

const TooltipButton: FC<TooltipButtonProps> = (props) => {
  // Destructure textColor instead of color
  const { title, placement, tooltipProps, textProps, style, textColor, ...restProps } = props;
  const breakpoint = useBreakpoint();

  const { token } = theme.useToken();
  // Calculate colorValue based on textColor
  const colorValue = useMemo(() => {
    const colorMap: Record<string, string> = {
      primary: token.colorPrimary,
      text: token.colorText,
      secondary: token.colorTextSecondary,
      disabled: token.colorTextDisabled,
    };
    // Use textColor prop here
    return props.disabled ? colorMap['disabled'] : textColor ? colorMap[textColor] : undefined;
    // Update dependency array
  }, [token, textColor, props.disabled]);

  return props.breakpoint && breakpoint[props.breakpoint] ? (
    <Button
      type='text'
      size='small'
      style={{
        color: colorValue, // Use calculated colorValue
        ...style,
      }}
      {...restProps}
    >
      <Typography.Text
        {...textProps}
        style={{
          color: colorValue, // Use calculated colorValue
          ...style, // Spread style from props here as well if intended
        }}
      >
        {title}
      </Typography.Text>
    </Button>
  ) : (
    <Tooltip title={title} placement={placement} {...tooltipProps}>
      <Button
        type='text'
        size='small'
        style={{
          color: colorValue, // Use calculated colorValue
          ...style,
        }}
        {...restProps}
      />
    </Tooltip>
  );
};

export default TooltipButton;
