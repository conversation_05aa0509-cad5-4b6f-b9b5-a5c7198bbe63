import { Button, ButtonProps, theme, Typography } from 'antd';
import { TextProps } from 'antd/es/typography/Text';
import React, { ReactNode } from 'react';

const SmallTextButton = React.forwardRef<
  HTMLElement,
  // Rename 'color' prop to 'textType' and use TextProps['type']
  Omit<ButtonProps, 'title'> & {
    textType?: TextProps['type'];
    title?: ReactNode;
  }
>((props, ref: any) => {
  // Destructure textType instead of color
  const { title, textType, ...restProps } = props;
  const { token } = theme.useToken();

  return (
    <Button
      ref={ref}
      type={'text'}
      size='small'
      {...restProps}
      onClick={(e) => {
        e.stopPropagation();
        props.onClick?.(e);
      }}
    >
      {title && (
        <Typography.Text
          // Use textType, prioritize danger prop
          type={props.danger ? 'danger' : textType}
        >
          {title}
        </Typography.Text>
      )}
    </Button>
  );
});

export default SmallTextButton;
