import { App, ConfigProvider, Empty, Layout, message, theme } from 'antd';
import { ThemeConfig } from 'antd/es/config-provider/context';
import { MappingAlgorithm } from 'antd/lib/theme/interface';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import i18n from 'i18next';
import React, { createContext, FC, PropsWithChildren, useEffect, useMemo } from 'react';

import { I18nextLng } from '../i18n';
import { ColorPrimary, generateToken, Theme } from '../theme';
import EmotionThemeProvider from './EmotionThemeProvider';

const { Content } = Layout;
const { darkAlgorithm, compactAlgorithm, defaultAlgorithm } = theme;
const localeMap = {
  [I18nextLng.en]: enUS,
  [I18nextLng.cn]: zhCN,
};

// 添加调试日志函数
function logI18nDebug(message: string, data?: any) {
  // console.log(`[I18N DEBUG] ${message}`, data || '');
  // 可选：将日志存储到localStorage以便后续分析
  try {
    const logs = JSON.parse(localStorage.getItem('i18n_debug_logs') || '[]');
    logs.push({
      time: new Date().toISOString(),
      message,
      data,
    });
    // 只保留最近的20条记录
    if (logs.length > 20) logs.shift();
    localStorage.setItem('i18n_debug_logs', JSON.stringify(logs));
  } catch (e) {
    console.error('Error saving i18n debug log:', e);
  }
}

// 添加语言修复函数
function fixLanguageStorage() {
  try {
    const storedLng = localStorage.getItem('i18nextLng');
    logI18nDebug('检查存储的语言值', { storedLng });

    // 检查是否存储了不正确的语言值
    if (storedLng === 'zh') {
      // 修正为正确的枚举值
      localStorage.setItem('i18nextLng', I18nextLng.cn);
      logI18nDebug('修正语言存储值', { from: 'zh', to: I18nextLng.cn });
    }

    // 检查是否为无效的 JSON
    try {
      JSON.parse(storedLng || '""');
    } catch (e) {
      // 如果不是有效的 JSON，重置为默认值
      localStorage.setItem('i18nextLng', I18nextLng.cn);
      logI18nDebug('重置无效的语言存储值', { invalid: storedLng, reset: I18nextLng.cn });
    }
  } catch (e) {
    console.error('修复语言存储时出错:', e);
  }
}

export type ArexCoreProviderProps = {
  theme: Theme;
  componentsConfig?: ThemeConfig['components'];
  compact: boolean;
  colorPrimary: ColorPrimary;
  language: I18nextLng;
  localeResources?: Record<I18nextLng, { [ns: string]: object }>;
};

export const ArexCoreContext = createContext<ArexCoreProviderProps>({
  theme: Theme.light,
  compact: false,
  colorPrimary: ColorPrimary.green,
  language: I18nextLng.cn,
});

const ArexCoreProvider: FC<PropsWithChildren<Partial<ArexCoreProviderProps>>> = (props) => {
  const {
    theme = Theme.light,
    compact = false,
    colorPrimary = ColorPrimary.green,
    language = I18nextLng.cn,
    localeResources,
    componentsConfig: components,
  } = props;

  // 在组件初始化时修复语言存储
  fixLanguageStorage();

  // 记录组件初始化时的语言设置
  logI18nDebug('ArexCoreProvider initialized', {
    propsLanguage: language,
    i18nLanguage: i18n.language,
    storedLanguage: localStorage.getItem('i18nextLng'),
    sessionCount: parseInt(localStorage.getItem('session_refresh_count') || '0')
  });

  // 跟踪页面刷新次数
  useEffect(() => {
    const count = parseInt(localStorage.getItem('session_refresh_count') || '0');
    localStorage.setItem('session_refresh_count', (count + 1).toString());
    logI18nDebug('Page refresh count updated', { count: count + 1 });
  }, []);

  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    // 记录资源加载前的状态
    logI18nDebug('Before adding locale resources', {
      localeResources: localeResources ? Object.keys(localeResources) : 'none',
      i18nLanguage: i18n.language,
      i18nStore: i18n.store.data
    });

    // add locale resources
    if (localeResources) {
      for (const lng in localeResources) {
        for (const ns in localeResources[lng as I18nextLng]) {
          i18n.addResourceBundle(lng, ns, localeResources[lng as I18nextLng][ns], true);
          logI18nDebug(`Added resource bundle`, { lng, ns });
        }
      }
    }

    // 确保 i18n 使用正确的语言
    if (i18n.language !== language) {
      logI18nDebug('语言不匹配，正在同步', { current: i18n.language, expected: language });
      i18n.changeLanguage(language);
    }

    // 记录资源加载后的状态
    logI18nDebug('After adding locale resources', {
      i18nLanguage: i18n.language,
      i18nStore: i18n.store.data
    });

    // set message api
    window.message = messageApi;
  }, []);

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      logI18nDebug('Language changed', {
        newLanguage: lng,
        prevPropsLanguage: language,
        storedLanguage: localStorage.getItem('i18nextLng')
      });
    };

    i18n.on('languageChanged', handleLanguageChanged);

    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [language]);

  const algorithm = useMemo<MappingAlgorithm[]>(() => {
    const _algorithm = [defaultAlgorithm];
    theme === Theme.dark && _algorithm.push(darkAlgorithm);
    compact && _algorithm.push(compactAlgorithm);
    return _algorithm;
  }, [theme, compact]);

  // 记录渲染时使用的语言
  logI18nDebug('Rendering with language', {
    configProviderLocale: localeMap[language] ? language : 'unknown',
    contextLanguage: language,
    i18nLanguage: i18n.language
  });

  return (
    <ConfigProvider
      theme={{
        token: generateToken(theme, colorPrimary),
        algorithm,
        components,
      }}
      locale={localeMap[language]}
      renderEmpty={() => Empty.PRESENTED_IMAGE_SIMPLE}
    >
      <EmotionThemeProvider>
        <App>
          {contextHolder}
          <Layout>
            <Content>
              <ArexCoreContext.Provider value={{ theme, compact, colorPrimary, language }}>
                {props.children}
              </ArexCoreContext.Provider>
            </Content>
          </Layout>
        </App>
      </EmotionThemeProvider>
    </ConfigProvider>
  );
};
export default ArexCoreProvider;
