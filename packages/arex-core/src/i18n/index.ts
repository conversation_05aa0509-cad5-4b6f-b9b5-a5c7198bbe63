import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import { ArexMenuNamespace, ArexPaneNamespace, I18_KEY } from '../constant';
import arexMenuCn from './locales/cn/arex-menu.json';
import arexPaneCn from './locales/cn/arex-pane.json';
import commonCn from './locales/cn/common.json';
import arexPaneEn from './locales/en/arex-pane.json';
import arexMenuEn from './locales/en/arex-pane.json';
import commonEn from './locales/en/common.json';

const resources = {
  cn: {
    translation: commonCn,
    [ArexPaneNamespace]: arexPaneCn,
    [ArexMenuNamespace]: arexMenuCn,
  },
  en: {
    translation: commonEn,
    [ArexPaneNamespace]: arexPaneEn,
    [ArexMenuNamespace]: arexMenuEn,
  },
};

export enum I18nextLng {
  'en' = 'en',
  'cn' = 'cn',
}

i18n.use(initReactI18next).init({
  resources,
  partialBundledLanguages: true,
  lng: localStorage.getItem(I18_KEY) || I18nextLng.cn,
  fallbackLng: I18nextLng.cn,
  detection: {
    caches: ['localStorage'], // 'sessionStorage', 'cookie'
  },
  interpolation: {
    escapeValue: false,
    prefix: '{',
    suffix: '}',
    skipOnVariables: false,
  },
  // debug: process.env.NODE_ENV === 'development',
});

// 添加一个辅助函数，用于在应用中检查i18n配置
export function checkI18nConfig() {
  console.log('I18n configuration:', {
    interpolation: i18n.options.interpolation,
    languages: i18n.languages,
    resources: i18n.options.resources,
  });

  // 测试插值是否正常工作
  const testKey = 'test';
  i18n.addResource('cn', 'translation', testKey, 'Test with {count}');
  const result = i18n.t(testKey, { count: 5 });
  console.log('Interpolation test:', result);

  return result === 'Test with 5';
}

export const local: { key: `${I18nextLng}`; name: string }[] = [
  { key: I18nextLng.cn, name: '简体中文' },
  { key: I18nextLng.en, name: 'English' },
];

export { i18n };
export { getI18n, useTranslation } from 'react-i18next';
export default i18n;
