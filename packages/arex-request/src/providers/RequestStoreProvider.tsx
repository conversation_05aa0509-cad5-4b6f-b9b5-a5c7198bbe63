import React, { create<PERSON>ontex<PERSON>, Dispatch, FC, PropsWithChildren, useEffect } from 'react';
import { useImmer } from 'use-immer';

import { useArexRequestProps } from '../hooks';
import {
  ArexEnvironment,
  ArexRESTRequest,
  ArexRESTResponse,
  ArexTestResult,
  ArexVisualizer,
} from '../types';

export interface RequestStore {
  state: any;
  request: ArexRESTRequest;
  edited?: boolean;
  environmentId?: string;
  environment?: ArexEnvironment;
  response?: ArexRESTResponse;
  testResult?: ArexTestResult[];
  consoles?: any[];
  visualizer?: ArexVisualizer;
}

const defaultState: RequestStore = {
  request: {
    id: '',
    preRequestScript: '',
    v: '',
    headers: [],
    name: '',
    body: {
      contentType: 'application/json',
      body: '',
      wmb: {},
      wmb_mock: '',
      scfRequest: {},
      formData: [],
    },
    auth: {
      authActive: false,
      authType: 'none',
    },
    testScript: '',
    endpoint: '',
    method: '',
    params: [],
    inherited: false,
    inheritedEndpoint: '',
    inheritedMethod: '',
    description: '',
    requestType: 'HTTP',
    // 添加默认的 contract 字段
    contract: [],
    // Add missing properties
    parentPreRequestScripts: '',
    parentTestScripts: '',

    // SCF字段
    scfID: '',
    serviceNameValue: '',
    interfaceNameValue: '',
    functionNameValue: '',
    // scfEnvType: 'IP',
    clusterName: '',
    serviceID: '',
  },
  edited: false,
  response: undefined,
  consoles: [],
  environment: undefined,
  testResult: undefined,
  visualizer: {
    error: null,
    data: null,
    processedTemplate: '',
  },
  state: undefined,
};

export const RequestStoreContext = createContext<{
  store: RequestStore;
  dispatch: Dispatch<(state: RequestStore) => void>;
}>({ store: defaultState, dispatch: () => { } });

const RequestStoreProvider: FC<PropsWithChildren> = (props) => {
  const { data, environmentProps } = useArexRequestProps();
  const [store, dispatch] = useImmer(defaultState);

  useEffect(() => {
    console.log('----->>>>>>useArexRequestProps data = ', data)
    data &&
      dispatch((state) => {
        state.request = data;
      });
  }, [data]);

  useEffect(() => {
    dispatch((state) => {
      state.environment = environmentProps?.options?.find(
        (env) => env.id === environmentProps?.value,
      );
    });
  }, [environmentProps]);

  return (
    <RequestStoreContext.Provider
      value={{
        store,
        dispatch,
      }}
    >
      {props.children}
    </RequestStoreContext.Provider>
  );
};

export default RequestStoreProvider;
