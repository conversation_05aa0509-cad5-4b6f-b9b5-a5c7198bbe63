import axios from 'axios';
import jsonBigInt from 'json-bigint';

export async function get(url: string): Promise<any> {
  try {
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function postForm(url: string, data: any): Promise<any> {
  const config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    },
  };

  try {
    const response = await axios.post(url, new URLSearchParams(data), config);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function postFormRawBody(url: string, data: string): Promise<any> {
  const config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      // Cookie: `PHPSESSID=${PHPSESSID}`,
    },
    withCredentials: true,
  };

  try {
    const response = await axios.post(url, data, config);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

const transformations = [
  (data: string) => {
    return jsonBigInt.parse(data);
  },
];

export async function post(url: string, data: object, accessToken?: string): Promise<any> {
  const config = {
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Access-Token': accessToken,
    },
    withCredentials: true,
    transformResponse: transformations,
  };

  try {
    const response = await axios.post(url, data, config);
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
}
