import { useCallback, useEffect, useState } from 'react';

import { ChildComponentPropsHis } from '../components/Request/InterfaceHistory/InterfaceHistoryList';
import { getFunctionOptions } from '../components/SCF/Request/helpers';
import { AutoCompLabel } from '../components/SCF/Request/index-bak';
import { get } from '../service/scfInfo';
import { ArexRESTReqBody } from '../types';
import { useArexRequestStore } from './useArexRequestStore';

// 定义更具体的函数类型来替代Function
type SetInterfaceHistoryListParamFn = React.Dispatch<React.SetStateAction<ChildComponentPropsHis>>;
type SetInterfaceDetailParamFn = (param: { scfID: string }) => void;

export const useInterfaceNameHandler = (
  serviceID: string | undefined,
  interfaceNameOptions: AutoCompLabel[],
  setInterfaceNameOptions: React.Dispatch<React.SetStateAction<AutoCompLabel[]>>,
  setNewFunctionOptions: React.Dispatch<React.SetStateAction<any[]>>,
  setOriginFunctionOptions: React.Dispatch<React.SetStateAction<any[]>>,
  setFunctionNameValue: React.Dispatch<React.SetStateAction<string | undefined>>,
  // 添加interfaceNameValue和setInterfaceNameValue作为参数
  interfaceNameValue: string | undefined,
  setInterfaceNameValue: React.Dispatch<React.SetStateAction<string | undefined>>,
  operatorUser?: string,
  clusterName?: string,
  setInterfaceHistoryListParam?: SetInterfaceHistoryListParamFn,
  setInterfaceDetailParam?: SetInterfaceDetailParamFn,
  extractParamsFromBody?: (body?: ArexRESTReqBody, requestType?: string) => any[],  // 添加参数提取函数参数
) => {
  const { dispatch } = useArexRequestStore();
  // 移除interfaceNameValue状态
  // const [interfaceNameValue, setInterfaceNameValue] = useState<string | undefined>(
  //   initialInterfaceName,
  // );
  const [selectInterfaceName, isSelectInterfaceName] = useState(false);
  // 添加originFunctionOptions状态
  const [originFunctionOptions, setLocalOriginFunctionOptions] = useState<any[]>([]);

  // 同步外部和内部的originFunctionOptions
  useEffect(() => {
    setOriginFunctionOptions(originFunctionOptions);
  }, [originFunctionOptions, setOriginFunctionOptions]);

  // Effect to fetch all functions when interface name is cleared
  useEffect(() => {
    const fetchAllFunctions = async () => {
      if (serviceID) {
        try {
          const functionData = await get(
            `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceID}`,
          );
          const data = getFunctionOptions(functionData.data) as any;
          setNewFunctionOptions(data);
          setLocalOriginFunctionOptions(data); // 使用本地状态
          setOriginFunctionOptions(data); // 同时更新外部状态
        } catch (error) {
          console.error('Failed to fetch all functions for service:', error);
          setNewFunctionOptions([]);
          setLocalOriginFunctionOptions([]); // 使用本地状态
          setOriginFunctionOptions([]); // 同时更新外部状态
        }
      }
    };

    if (!interfaceNameValue && serviceID) {
      fetchAllFunctions();
    }
  }, [interfaceNameValue, serviceID, setNewFunctionOptions, setOriginFunctionOptions]);

  const handleInterfaceNameSelect = useCallback(
    async (value: string) => {
      setInterfaceNameValue(value);
      isSelectInterfaceName(true);
      // 添加这行代码，确保更新interfaceName
      dispatch((state) => {
        state.request.body.scfRequest.interfaceName = value;
      });

      if (!serviceID) return; // Need serviceID to fetch functions

      try {
        // Fetch function list based on serviceID and selected interfaceName
        const functionData = await get(
          `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceID}&interfcClsName=${value}`,
        );
        const data = getFunctionOptions(functionData.data) as any;
        setNewFunctionOptions(data);
        setLocalOriginFunctionOptions(data); // 使用本地状态
        setOriginFunctionOptions(data); // 同时更新外部状态

        // Clear function name selection when interface changes
        setFunctionNameValue('');
      } catch (error) {
        console.error('Failed to fetch functions for interface:', error);
        setNewFunctionOptions([]);
        setLocalOriginFunctionOptions([]); // 使用本地状态
        setOriginFunctionOptions([]); // 同时更新外部状态
        setFunctionNameValue('');
      }
    },
    [serviceID, setNewFunctionOptions, setOriginFunctionOptions, setFunctionNameValue],
  );

  // 处理函数名搜索
  const handleFunctionSearch = useCallback(
    (value: string) => {
      const filteredOptions = originFunctionOptions.flatMap((category: any) =>
        category.options.filter(
          (item: any) => item.functionName.toLowerCase().indexOf(value.toLowerCase()) !== -1,
        ),
      );

      const transformedData = filteredOptions.reduce((acc, curr) => {
        if (!acc[curr.parent]) {
          acc[curr.parent] = [];
        }

        acc[curr.parent].push({
          scfID: curr.key,
          scf_function_name: curr.functionName,
        });

        return acc;
      }, {});
      const newOptions = getFunctionOptions(transformedData) as any;
      setNewFunctionOptions(newOptions);
    },
    [originFunctionOptions, setNewFunctionOptions],
  );

  // 处理函数名选择
  const handleFunctionNameSelect = useCallback(
    async (value: string, option: AutoCompLabel) => {
      setFunctionNameValue(option.functionName);

      // 添加这行代码，确保更新methodName
      dispatch((state) => {
        state.request.body.scfRequest.methodName = option.functionName;
      });

      const scfId = option.key;
      const scfDetail = await get(
        `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}&operatorUser=${operatorUser}`,
      );
      const { implClass, interfaceClass, paramList, serviceName, servicePort, version } =
        scfDetail.data;
      const resp = await get(`/iapi/iapi/scence/getBranchList?scfId=${scfId}`);
      const branchList = resp.data;
      const scfRequest = {
        scfId,
        implClassName: implClass,
        interfaceName: interfaceClass,
        params: paramList,
        scfServiceName: serviceName,
        scfServiceId: serviceID,
        clusterName,
        port: servicePort,
        branchVersion: version,
        branchList,
        connectType: '1',
        serializedVersion: '0',
        envID: '3',
        operatorUser,
      };

      dispatch((state) => {
        state.request.body.scfRequest = { ...state.request.body.scfRequest, ...scfRequest };
      });

      if (setInterfaceHistoryListParam) {
        setInterfaceHistoryListParam((prevState) => ({
          hisProps: {
            ...prevState.hisProps,
            scfId: scfId,
          },
        }));
      }

      if (setInterfaceDetailParam) {
        setInterfaceDetailParam({ scfID: scfId });
      }

      // 添加自动提取参数的逻辑
      if (extractParamsFromBody) {
        try {
          // 保存当前的contract数据，以便后续恢复描述信息
          dispatch((state) => {
            const currentContract = state.request.contract ? [...state.request.contract] : [];
            const currentDescriptions: Record<string, string> = {};

            // 提取当前的描述信息
            if (currentContract && currentContract.length > 0) {
              const extractDescriptions = (params: any[]) => {
                params.forEach((param) => {
                  const key = param.parentKey ? `${param.parentKey}.${param.name}` : param.name;
                  if (param.desc) {
                    currentDescriptions[key] = param.desc;
                  }
                  if (param.children && param.children.length > 0) {
                    extractDescriptions(param.children);
                  }
                });
              };

              extractDescriptions(currentContract);
            }

            // 提取新的参数
            const params = extractParamsFromBody(state.request.body, 'SCF');
            if (params && params.length > 0) {
              // 恢复描述信息到新提取的参数
              const restoreDescriptions = (paramList: any[]): any[] => {
                return paramList.map((param) => {
                  const key = param.parentKey ? `${param.parentKey}.${param.name}` : param.name;
                  const savedDesc = currentDescriptions[key];

                  const updatedParam = { ...param };
                  if (savedDesc) {
                    updatedParam.desc = savedDesc;
                  }

                  if (param.children && param.children.length > 0) {
                    updatedParam.children = restoreDescriptions(param.children);
                  }

                  return updatedParam;
                });
              };

              const paramsWithDesc = restoreDescriptions(params);
              state.request.contract = paramsWithDesc;
            }
          });
        } catch (error) {
          console.error('自动提取参数失败:', error);
        }
      }
    },
    [serviceID, clusterName, operatorUser, dispatch, setFunctionNameValue, setInterfaceHistoryListParam, setInterfaceDetailParam, extractParamsFromBody],
  );

  // 过滤选项的通用处理函数
  const handleFilterOption = useCallback((inputValue: string, option: any) => {
    return option?.value?.toLocaleLowerCase().includes(inputValue.toLocaleLowerCase());
  }, []);

  return {
    interfaceNameValue,
    selectInterfaceName,
    setInterfaceNameValue,
    isSelectInterfaceName,
    handleInterfaceNameSelect,
    handleFunctionSearch,
    handleFunctionNameSelect,
    handleFilterOption,
  };
};
