import jsonBigInt from 'json-bigint';
import React, { useCallback,useEffect, useState } from 'react';

import { IpOptions } from '../types/SCFRequest';
import { useArexRequestStore } from './useArexRequestStore';

type EnvCode = '0' | '1' | '2' | '3' | '4';
type EnvValueCode = 'test' | 'stable' | 'sandbox' | 'online' | 'others';
const EnvMapping = {
  '0': 'test',
  '1': 'stable',
  '2': 'sandbox',
  '3': 'online',
  '4': 'others',
};

export const useIpHandler = () => {
  const { store, dispatch } = useArexRequestStore();
  const [originIpData, setOriginIpData] = useState<IpOptions>();
  const [ipOptions, setIpOptions] = useState<any[]>();
  const [originIpOptions, setOriginIpOptions] = useState<any[]>();

  useEffect(() => {
    if (!originIpData) {
      setIpOptions([]);
      setOriginIpOptions([]);
      return;
    }

    const scfContext = store.request.body.scfRequest;
    // Add null check for scfContext
    if (!scfContext) {
      setIpOptions([]);
      setOriginIpOptions([]);
      return;
    }

    const env = (scfContext.envID || '3') as EnvCode; // Default to '3' (Online) if envID is not set
    const envVal = EnvMapping[env] as EnvValueCode;
    const ipInfos = originIpData[envVal];

    if (!ipInfos) {
      setIpOptions([]);
      setOriginIpOptions([]);
      dispatch((state) => {
        state.request.body.scfRequest.ip = ''; // Clear IP if no options for the env
      });
      return;
    }

    if (Array.isArray(ipInfos)) {
      const ipOpts = ipInfos.map((ip) => ({ value: ip, label: ip }));
      setIpOptions(ipOpts);
      setOriginIpOptions(ipOpts);
      if (!scfContext.ip || !ipInfos.includes(scfContext.ip)) {
        dispatch((state) => {
          state.request.body.scfRequest.ip = ipOpts[0]?.value || '';
        });
      }
      return;
    }

    const allIPs: { value: string }[] = [];
    const transformedData = Object.keys(ipInfos).map((key) => {
      const options = ipInfos[key].map((ip) => {
        allIPs.push({ value: ip });
        return {
          label: <span>{ip}</span>,
          value: ip,
        };
      });
      return {
        label: <span>{key}</span>,
        title: key,
        options,
      };
    });

    setIpOptions(transformedData);
    setOriginIpOptions(transformedData);

    const currentIP = scfContext.ip;
    const isIpInList = allIPs.some((item) => item.value === currentIP);

    if (!currentIP || !isIpInList) {
      dispatch((state) => {
        state.request.body.scfRequest.ip = allIPs[0]?.value || '';
      });
    }
  }, [originIpData, store.request.body.scfRequest, store.request.body.scfRequest?.envID, dispatch]); // Add envID dependency

  const onEnvChange = useCallback(
    (v: EnvCode) => {
      dispatch((state) => {
        // Reset IP when environment changes
        state.request.body.scfRequest.ip = '';
        state.request.body.scfRequest.envID = v;
      });
      // The useEffect hook above will handle updating ipOptions and setting the default IP
    },
    [dispatch],
  );

  const onIpChange = useCallback(
    (v: any) => {
      dispatch((state) => {
        state.request.body.scfRequest.ip = v;
      });
    },
    [dispatch],
  );

  const handleIpSearch = useCallback(
    (value: string) => {
      if (!originIpOptions) return;

      const currentEnv = store.request.body.scfRequest.envID as EnvCode;
      const envVal = EnvMapping[currentEnv] as EnvValueCode;
      const ipDataForEnv = originIpData ? originIpData[envVal] : undefined;

      if (!ipDataForEnv) {
        setIpOptions([]);
        return;
      }

      // Handle grouped options (Stable/Online)
      if (!Array.isArray(ipDataForEnv)) {
        const filteredIpOption = originIpOptions
          .map((group) => ({
            ...group,
            options: group.options.filter((option: { value: string }) =>
              option.value.toLowerCase().includes(value.toLowerCase()),
            ),
          }))
          .filter((group) => group.options.length > 0); // Remove groups with no matching options
        setIpOptions(filteredIpOption);
      } else {
        // Handle flat array options (Test/Sandbox/Others)
        const filteredIpOption = originIpOptions.filter((item) =>
          item.value.toLowerCase().includes(value.toLowerCase()),
        );
        setIpOptions(filteredIpOption);
      }
    },
    [originIpOptions, originIpData, store.request.body.scfRequest?.envID], // Add optional chaining here
  );

  return {
    originIpData, // Keep returning this if needed elsewhere, otherwise remove
    setOriginIpData,
    ipOptions,
    setIpOptions,
    onEnvChange,
    onIpChange,
    handleIpSearch,
  };
};
