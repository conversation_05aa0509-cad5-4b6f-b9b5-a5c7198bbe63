import { useCallback, useEffect, useState } from 'react';

import { getFunctionOptions } from '../components/SCF/Request/helpers';
import { AutoCompLabel } from '../components/SCF/Request/index-bak';
import { get } from '../service/scfInfo';
import { IpOptions } from '../types/SCFRequest';
import { useArexRequestStore } from './useArexRequestStore';

export const useServiceNameHandler = (
  setInterfaceNameOptions: React.Dispatch<React.SetStateAction<AutoCompLabel[]>>,
  setNewFunctionOptions: React.Dispatch<React.SetStateAction<any[]>>,
  setOriginFunctionOptions: React.Dispatch<React.SetStateAction<any[]>>,
  setOriginIpData: React.Dispatch<React.SetStateAction<IpOptions | undefined>>,
  initialServiceName?: string,
  setInterfaceNameValue?: React.Dispatch<React.SetStateAction<string | undefined>>,
  setFunctionNameValue?: React.Dispatch<React.SetStateAction<string | undefined>>,
) => {
  const { store, dispatch } = useArexRequestStore();
  const [serviceNameOptions, setServiceNameOptions] = useState<AutoCompLabel[]>([]);
  const [serviceFilterOption, setServiceFilterOption] = useState<AutoCompLabel[]>([]);
  const [serviceNameValue, setServiceNameValue] = useState<string | undefined>(initialServiceName);
  const [serviceID, setServiceID] = useState<string>();
  const [clusterName, setClusterName] = useState<string | undefined>('');
  const [selectServiceName, isSelectServiceName] = useState(false);

  useEffect(() => {
    const getAllServiceName = async () => {
      try {
        const fetchedData = await get('/iapi/iapi/scence/getAllServiceInfo');
        const serviceOpt = fetchedData.data;
        setServiceNameOptions(serviceOpt);
        setServiceFilterOption(serviceOpt); // Initialize filter options
      } catch (error) {
        console.error('Failed to fetch service names:', error);
      }
    };
    getAllServiceName();
  }, []);

  useEffect(() => {
    const getEnvInfo = async (cName: string) => {
      try {
        return await get(`/iapi/iapi/scence/getEnvInfosByClusterName?clusterName=${cName}`);
      } catch (error) {
        console.error(error);
      }
    };

    const handleEnvInfo = async (cName: string) => {
      const ipResp = await getEnvInfo(cName);
      setOriginIpData(ipResp.data);
      dispatch((state) => {
        state.request.body.scfRequest.ipOptions = ipResp.data as IpOptions;
        if (!state.request.body.scfRequest.envID) {
          state.request.body.scfRequest.envID = '3'; // Default envID
        }
      });
    };

    if (store.request.body.scfRequest?.clusterName) {
      const cName = store.request.body.scfRequest.clusterName;
      console.log('clusterName changed:', store.request.body.scfRequest);
      handleEnvInfo(cName);
    }
  }, [store.request.body.scfRequest?.clusterName]);

  useEffect(() => {
    const handleInterfaceInfo = async (serviceId: string) => {
      try {
        const interfaceData = await get(
          `/iapi/iapi/scence/getInterfaceClassByServiceName?serviceId=${serviceId}`,
        );
        if (Array.isArray(interfaceData.data)) {
          const iOpt = interfaceData.data.map((v: any) => ({ value: v.value, key: v.value })); // Ensure key is set if needed later
          setInterfaceNameOptions(iOpt);
        } else {
          setInterfaceNameOptions([]);
        }
      } catch (error) {
        console.error(error);
        setInterfaceNameOptions([]);
      }
    };

    const handleFunctionInfo = async (serviceId: string) => {
      try {
        const functionData = await get(
          `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceId}`,
        );
        const data = getFunctionOptions(functionData.data) as any;
        setNewFunctionOptions(data);
        setOriginFunctionOptions(data);
      } catch (error) {
        console.error('Failed to fetch functions:', error);
        setNewFunctionOptions([]);
        setOriginFunctionOptions([]);
      }
    };

    if (store.request.body.scfRequest?.scfServiceId) {
      const serviceId = store.request.body.scfRequest.scfServiceId;
      handleInterfaceInfo(serviceId);
      handleFunctionInfo(serviceId);
    }
  }, [store.request.body.scfRequest?.scfServiceId]);

  const handleSearchServiceName = useCallback(
    (value: string) => {
      const filtered = serviceNameOptions.filter((option) =>
        option?.value?.toLocaleLowerCase().includes(value.toLocaleLowerCase()),
      );
      setServiceFilterOption(filtered);
    },
    [serviceNameOptions],
  );

  const handleServiceNameSelect = useCallback(
    async (value: string, option: AutoCompLabel) => {
      // 使用 setTimeout 确保状态更新在其他操作之后
      setTimeout(() => {
        setServiceNameValue(option.value);
        if (setInterfaceNameValue) setInterfaceNameValue('');
        if (setFunctionNameValue) setFunctionNameValue('');
      }, 0);

      setServiceID(option.key);
      setClusterName(option?.clusterName);
      isSelectServiceName(true);

      // 添加这行代码，确保更新scfServiceName并清除接口类和函数名
      dispatch((state) => {
        state.request.body.scfRequest.scfServiceName = option.value;
        state.request.body.scfRequest.interfaceName = undefined;
        state.request.body.scfRequest.methodName = undefined;
      });

      if (!option.key) return;

      try {
        // Fetch interface list
        const interfaceData = await get(
          `/iapi/iapi/scence/getInterfaceClassByServiceName?serviceId=${option.key}`,
        );
        if (Array.isArray(interfaceData.data)) {
          const iOpt = interfaceData.data.map((v: any) => ({ value: v.value, key: v.value })); // Ensure key is set if needed later
          setInterfaceNameOptions(iOpt);
        } else {
          setInterfaceNameOptions([]);
        }

        // Fetch function list
        const functionData = await get(
          `/iapi/iapi/scence/openapi/getFunctions?serviceId=${option.key}`,
        );
        const data = getFunctionOptions(functionData.data) as any;
        setNewFunctionOptions(data);
        setOriginFunctionOptions(data);

        // Fetch IP list
        const cName = option.clusterName || clusterName;
        if (cName) {
          const ipResp = await get(
            `/iapi/iapi/scence/getEnvInfosByClusterName?clusterName=${cName}`,
          );
          setOriginIpData(ipResp.data);
          dispatch((state) => {
            state.request.body.scfRequest.ipOptions = ipResp.data as IpOptions;
            if (!state.request.body.scfRequest.envID) {
              state.request.body.scfRequest.envID = '3'; // Default envID
            }
          });
        } else {
          setOriginIpData(undefined);
        }
      } catch (error) {
        console.error('获取数据失败', error);
        // Reset dependent options on error
        setInterfaceNameOptions([]);
        setNewFunctionOptions([]);
        setOriginFunctionOptions([]);
        setOriginIpData(undefined);
      }
    },
    [
      clusterName,
      dispatch,
      setInterfaceNameOptions,
      setNewFunctionOptions,
      setOriginFunctionOptions,
      setOriginIpData,
      setInterfaceNameValue, // 添加到依赖数组
    ],
  );

  return {
    serviceNameOptions,
    serviceFilterOption,
    serviceNameValue,
    serviceID,
    clusterName,
    selectServiceName,
    setServiceNameValue, // Expose setter if needed externally
    setServiceID, // Expose setter if needed externally
    setClusterName, // Expose setter if needed externally
    isSelectServiceName,
    handleSearchServiceName,
    handleServiceNameSelect,
  };
};
