import { SaveOutlined } from '@ant-design/icons';
import { useTranslation } from '@arextest/arex-core';
import { css } from '@emotion/react';
import { Allotment } from 'allotment';
import { Divider, Select, SelectProps, Space, Spin, TabPaneProps, Tooltip } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';

// httocompare
import HttpCompareRequest from './components/HttpCompare/Request';
import HttpCompareResponse from './components/HttpCompare/Response';
import NavigationBar, { NavigationBarRef } from './components/NavigationBar';
import EnvironmentSelect from './components/NavigationBar/EnvironmentSelect';
// http
import Request, { RequestProps } from './components/Request';
import Response from './components/Response';
// scf
import SCFRequest from './components/SCF/Request';
import SCFResponse from './components/SCF/Response';
import SCFCompareRequest from './components/SCFCompare/Request';
import SCFCompareResponse from './components/SCFCompare/Response';
// wmb
import WMBRequest from './components/WMB/Request';
import WMBResponse from './components/WMB/Response';
import WMBRequestNew from './components/WMB_NEW/Request';
import WMBResponseNew from './components/WMB_NEW/Response';
import { useArexRequestStore } from './hooks';
import styles from './index.module.css';
import { RequestPropsProvider, RequestStoreProvider } from './providers';
import { ArexRESTRequest } from './types';

const options: SelectProps['options'] = [
  {
    label: '🌐',
    value: 'HTTP',
  },
  {
    label: '♨',
    value: 'SCF',
  },
  {
    label: '🧭',
    // value: 'WMB(Producer)',
    value: 'WMB',
  },
  {
    label: '✴️',
    //value: 'WMB_Consumer',
    value: 'WMB_MOCK',
  },
  {
    label: '🌐',
    //value: 'WMB_Consumer',
    value: 'HTTP_COMPARE',
  },
  {
    label: '🧭',
    //value: 'WMB_Consumer',
    value: 'SCF_COMPARE',
  },
];

export interface Tab extends Omit<TabPaneProps, 'tab'> {
  key: string;
  label: React.ReactNode;
  hidden?: boolean;
}

export type TabConfig = {
  extra?: Tab[];
  filter?: (key: string) => boolean;
};

export type HttpConfig = {
  requestTabs?: TabConfig;
  responseTabs?: TabConfig;
};

export interface ArexRequestProps extends RequestProps {
  loading?: boolean;
  height?: string;
  data?: ArexRESTRequest;
  config?: HttpConfig;
  navPane?: any;
  diff?: any;
  getCollections?: () => void; // 添加getCollections属性
}

export type RequestRef = {
  onSave: () => void;
  onSaveAs: () => void;
};

const ArexRequestChilren = forwardRef<RequestRef, ArexRequestProps>((props, ref) => {
  const { loading = false, height } = props;
  const { store, dispatch } = useArexRequestStore();
  const navigationBarRef = useRef<NavigationBarRef>(null);
  const [caseItemId, setCaseItemId] = useState<string>('');

  //const handleChange = (value: 'HTTP' | 'WMB' | 'SCF' | 'WMB_Consumer') => {
  const handleChange = (
    value: 'HTTP' | 'WMB' | 'SCF' | 'WMB_MOCK' | 'HTTP_COMPARE' | 'SCF_COMPARE',
  ) => {
    dispatch((state) => {
      state.request.requestType = value;
    });
  };

  useImperativeHandle(
    ref,
    () => ({
      save: (key?: string) => navigationBarRef.current?.save(key),
      onSave: () => props.onSave?.(store.request, store.response),
      onSaveAs: () => props.onSaveAs?.(store.request, store.response),
    }),
    [props, store.request, store.response],
  );

  const { t } = useTranslation();

  const buttonsItems = useMemo(
    () => [
      {
        key: 'saveAs',
        label: t('request.save_as'),
        icon: <SaveOutlined />,
      },
    ],
    [t],
  );

  const AllotmentCSS = useMemo(
    () => css`
      height: ${height};
      .ant-tabs-content {
        height: 100%;
      }
      .ant-tabs-tabpane {
        height: inherit;
        overflow: auto;
      }
    `,
    [height],
  );
  return (
    <div className={styles.areaRequestContent}>
      <Spin spinning={loading}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {/* 添加protocal */}
          <Tooltip title='Switch request type'>
            <Select
              id='protocal-select'
              // style={{ width: 100 }}
              defaultValue='SCF'
              dropdownStyle={{ width: 170 }}
              allowClear={false}
              value={store.request.requestType || 'SCF'}
              // placeholder='Select a protocal'
              bordered={false}
              options={options}
              suffixIcon={null}
              onChange={handleChange}
              optionRender={(option) => (
                <Space>
                  {option.data.label}
                  {option.data.value}
                </Space>
              )}
            />
          </Tooltip>
          <div style={{ width: '100%' }}>
            <NavigationBar ref={navigationBarRef} />
          </div>
        </div>
        {/* <NavigationBar /> */}
        <Divider style={{ width: '100%', margin: '0 0 8px 0' }} />
        {store.request.requestType === 'HTTP' ? (
          <Allotment vertical css={AllotmentCSS}>
            <Allotment.Pane preferredSize='50%'>
              {/* Pass onSave prop to Request component */}
              <Request onSave={props.onSave} />
            </Allotment.Pane>
            <Allotment.Pane>
              <Response />
            </Allotment.Pane>
          </Allotment>
        ) : store.request.requestType === 'WMB' ? (
          <Allotment vertical css={AllotmentCSS}>
            <Allotment.Pane preferredSize='50%'>
              {/* Pass onSave prop to WMBRequest component if needed */}
              <WMBRequest />
            </Allotment.Pane>
            <Allotment.Pane>
              <WMBResponse />
            </Allotment.Pane>
          </Allotment>
        ) : store.request.requestType === 'WMB_MOCK' ? (
          <Allotment vertical css={AllotmentCSS}>
            <Allotment.Pane preferredSize='50%'>
              {/* Pass onSave prop to WMBRequestNew component if needed */}
              <WMBRequestNew />
            </Allotment.Pane>
            <Allotment.Pane>
              <WMBResponseNew />
            </Allotment.Pane>
          </Allotment>
        ) : store.request.requestType === 'HTTP_COMPARE' ? (
          <Allotment vertical css={AllotmentCSS}>
            <Allotment.Pane preferredSize='50%'>
              {/* Pass onSave prop to HttpCompareRequest component if needed */}
              <HttpCompareRequest setCaseItemId={setCaseItemId} />
            </Allotment.Pane>
            <Allotment.Pane>
              <HttpCompareResponse caseItemId={caseItemId} diff={props?.diff} />
            </Allotment.Pane>
          </Allotment>
        ) : store.request.requestType === 'SCF_COMPARE' ? (
          <Allotment vertical css={AllotmentCSS}>
            <Allotment.Pane preferredSize='50%'>
              {/* Pass onSave prop to SCFCompareRequest component if needed */}
              <SCFCompareRequest setCaseItemId={setCaseItemId} navPane={props.navPane} />
            </Allotment.Pane>
            <Allotment.Pane>
              <SCFCompareResponse caseItemId={caseItemId} diff={props?.diff} />
            </Allotment.Pane>
          </Allotment>
        ) : (
          <Allotment vertical css={AllotmentCSS}>
            <Allotment.Pane preferredSize='50%'>
              {/* Pass onSave prop to SCFRequest component if needed */}
              <SCFRequest navPane={props.navPane} onSave={props.onSave} />
            </Allotment.Pane>
            <Allotment.Pane>
              <SCFResponse />
            </Allotment.Pane>
          </Allotment>
        )}
      </Spin>
    </div>
  );
});

const ArexRequest = forwardRef<RequestRef, ArexRequestProps>((props, ref) => {
  const { store } = useArexRequestStore();
  const childRef = useRef<any>(null);

  useImperativeHandle(
    ref,
    () => ({
      save: (key?: string) => childRef.current?.save(key),
      onSave: () => props.onSave?.(store.request, store.response),
      onSaveAs: () => props.onSaveAs?.(),
    }),
    [props, store.request, store.response],
  );

  return (
    <RequestPropsProvider {...props}>
      <RequestStoreProvider>
        <ArexRequestChilren {...props} ref={childRef} />
      </RequestStoreProvider>
    </RequestPropsProvider>
  );
});

export default ArexRequest;
