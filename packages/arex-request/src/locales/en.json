{"action": {"autoscroll": "Autoscroll", "cancel": "Cancel", "choose_file": "Choose a file", "clear": "Clear", "clear_all": "Clear all", "close": "Close", "connect": "Connect", "connecting": "Connecting", "copy": "Copy", "delete": "Delete", "disconnect": "Disconnect", "dismiss": "<PERSON><PERSON><PERSON>", "dont_save": "Don't save", "download_file": "Download file", "drag_to_reorder": "Drag to reorder", "duplicate": "Duplicate", "edit": "Edit", "filter": "Filter", "go_back": "Go back", "group_by": "Group by", "label": "Label", "learn_more": "Learn more", "less": "Less", "more": "More", "new": "New", "no": "No", "open_workspace": "Open workspace", "paste": "Paste", "prettify": "Prettify", "remove": "Remove", "restore": "Rest<PERSON>", "save": "Save", "scroll_to_bottom": "Scroll to bottom", "scroll_to_top": "Scroll to top", "search": "Search", "send": "Send", "start": "Start", "starting": "Starting", "stop": "Stop", "to_close": "to close", "to_navigate": "to navigate", "to_select": "to select", "turn_off": "Turn off", "turn_on": "Turn on", "undo": "Undo", "yes": "Yes"}, "add": {"new": "Add new", "star": "Add star", "env": "Add Env"}, "error": {"browser_support_sse": "This browser doesn't seems to have Server Sent Events support.", "check_console_details": "Check console log for details.", "curl_invalid_format": "cURL is not formatted properly", "empty_req_name": "Empty Request Name", "emptyEndpoint": "Endpoint can't be empty", "f12_details": "(F12 for details)", "gql_prettify_invalid_query": "Couldn't prettify an invalid query, solve query syntax errors and try again", "incomplete_config_urls": "Incomplete configuration URLs", "incorrect_email": "Incorrect email", "invalid_link": "Invalid link", "invalid_link_description": "The link you clicked is invalid or expired.", "json_parsing_failed": "Invalid JSON", "json_prettify_invalid_body": "Couldn't prettify an invalid body, solve json syntax errors and try again", "network_error": "There seems to be a network error. Please try again.", "network_fail": "Could not send request", "no_duration": "No duration", "no_results_found": "No matches found", "page_not_found": "This page could not be found", "script_fail": "Could not execute pre-request script", "something_went_wrong": "Something went wrong", "test_script_fail": "Could not execute post-request script", "extensionNotInstalled": "Arex Chrome Extension is not installed. Install it from Chrome Web Store.", "downloadExtension": "Download Extension"}, "preRequest": {"javascript_code": "JavaScript Code", "learn": "Read documentation", "script": "Pre-Request Script", "snippets": "Snippets"}, "request": {"added": "Request added", "authorization": "Authorization", "body": "Request Body", "choose_language": "Choose language", "content_type": "Content Type", "content_type_titles": {"others": "Others", "structured": "Structured", "text": "Text"}, "copy_link": "Copy link", "duration": "Duration", "enter_curl": "Enter cURL command", "generate_code": "Generate code", "generated_code": "Generated code", "headers": "Request Headers", "invalid_name": "Please provide a name for the request", "method": "Method", "name": "Request Name", "description": "Request Description", "new": "New Request", "override": "Override", "override_help": "Set <kbd>Content-Type</kbd> in Headers", "overriden": "Overridden", "parameter_list": "Query Parameters", "key_info": "密钥基础信息", "parameters": "Parameters", "path": "Path", "payload": "Payload", "query": "Query", "raw_body": "Raw Request Body", "renamed": "Request renamed", "run": "Run", "save": "Save", "save_as": "Save as", "saved": "Request saved", "share": "Share", "share_description": "Share Hoppscotch with your friends", "title": "Request", "type": "Request type", "url": "URL", "variables": "Variables", "view_my_links": "View my links"}, "env": {"environment": "Environment", "selectEnv": "Please select environment", "noEnv": "No environment", "enterEnv": "Enter new environment"}, "response": {"body": "Response Body", "filter_response_body": "Filter JSON response body (uses JSONPath syntax)", "headers": "Headers", "html": "HTML", "image": "Image", "json": "JSON", "pdf": "PDF", "preview_html": "Preview HTML", "raw": "Raw", "size": "Size", "status": "Status", "time": "Time", "title": "Response", "waiting_for_connection": "waiting for connection", "xml": "XML", "sendRequestTip": "Enter the URL and click Send to get a Response"}, "tab": {"authorization": "Authorization", "body": "Body", "collections": "Collections", "documentation": "Documentation", "headers": "Headers", "key": "秘钥信息", "mock": "mock消息体", "history": "History", "mqtt": "MQTT", "parameters": "Parameters", "pre_request_script": "Pre-request Script", "queries": "Queries", "query": "Query", "schema": "<PERSON><PERSON><PERSON>", "socketio": "Socket.IO", "sse": "SSE", "tests": "Tests", "types": "Types", "variables": "Variables", "websocket": "WebSocket"}, "test": {"passed": "Passed", "failed": "Failed", "javascript_code": "JavaScript Code", "learn": "Read documentation", "report": "Test Report", "results": "Test Results", "script": "<PERSON><PERSON><PERSON>", "snippets": "Snippets"}, "scf": {"tab": {"env": "Env", "envHeader": "Environment Config", "param": "Parameters", "context": "Context"}, "body": {"noParam": "This method has no params"}}}