{"action": {"autoscroll": "Autoscroll", "cancel": "取消", "choose_file": "选择文件", "clear": "清除", "clear_all": "全部清除", "close": "关闭", "connect": "连接", "connecting": "Connecting", "copy": "复制", "delete": "删除", "disconnect": "断开连接", "dismiss": "忽略", "dont_save": "不保存", "download_file": "下载文件", "drag_to_reorder": "Drag to reorder", "duplicate": "复制", "edit": "编辑", "filter": "Filter", "go_back": "返回", "group_by": "Group by", "label": "标签", "learn_more": "了解更多", "less": "更少", "more": "更多", "new": "新增", "no": "否", "open_workspace": "Open workspace", "paste": "粘贴", "prettify": "美化", "remove": "移除", "restore": "恢复", "save": "保存", "scroll_to_bottom": "Scroll to bottom", "scroll_to_top": "Scroll to top", "search": "搜索", "send": "发送", "start": "开始", "starting": "Starting", "stop": "停止", "to_close": "以关闭", "to_navigate": "以定位", "to_select": "以选择", "turn_off": "关闭", "turn_on": "开启", "undo": "撤消", "yes": "是"}, "add": {"new": "新增", "star": "添加星标", "env": "新增环境"}, "error": {"browser_support_sse": "该浏览器似乎不支持 SSE。", "check_console_details": "检查控制台日志以获悉详情", "curl_invalid_format": "cURL 格式不正确", "empty_req_name": "空请求名称", "emptyEndpoint": "请求路径不能为空", "f12_details": "（F12 详情）", "gql_prettify_invalid_query": "无法美化无效的查询，处理查询语法错误并重试", "incomplete_config_urls": "配置文件中的 URL 无效", "incorrect_email": "电子邮箱错误", "invalid_link": "无效链接", "invalid_link_description": "你点击的链接无效或已过期。", "json_parsing_failed": "Invalid JSON", "json_prettify_invalid_body": "无法美化无效的请求头，处理 JSON 语法错误并重试", "network_error": "好像发生了网络错误，请重试。", "network_fail": "无法发送请求", "no_duration": "无持续时间", "no_results_found": "No matches found", "page_not_found": "This page could not be found", "script_fail": "无法执行预请求脚本", "something_went_wrong": "发生了一些错误", "test_script_fail": "无法执行请求脚本", "extensionNotInstalled": "Arex Chrome 插件未安装, 请至谷歌网页商店下载安装插件.", "downloadExtension": "下载插件"}, "preRequest": {"javascript_code": "JavaScript 代码", "learn": "阅读文档", "script": "预请求脚本", "snippets": "代码片段"}, "request": {"added": "已添加请求", "authorization": "授权", "body": "请求体", "choose_language": "选择语言", "content_type": "内容类型", "content_type_titles": {"others": "Others", "structured": "Structured", "text": "Text"}, "copy_link": "复制链接", "duration": "持续时间", "enter_curl": "输入 cURL", "generate_code": "生成代码", "generated_code": "已生成代码", "headers": "请求头列表", "invalid_name": "请提供请求名称", "method": "方法", "name": "请求名称", "description": "请求描述", "new": "新请求", "override": "覆盖", "overriden": "覆盖", "parameter_list": "查询参数", "parameters": "参数", "path": "路径", "payload": "负载", "query": "查询", "raw_body": "原始请求体", "renamed": "请求重命名", "run": "运行", "save": "保存", "save_as": "另存为", "saved": "请求已保存", "share": "分享", "title": "请求", "type": "请求类型", "url": "URL", "variables": "变量", "view_my_links": "View my links"}, "env": {"environment": "环境", "selectEnv": "请选择环境", "noEnv": "无环境", "enterEnv": "请输入新环境名称"}, "response": {"body": "响应体", "filter_response_body": "Filter JSON response body (uses JSONPath syntax)", "headers": "响应头", "html": "HTML", "image": "图像", "json": "JSON", "pdf": "PDF", "preview_html": "预览 HTML", "raw": "原始内容", "size": "大小", "status": "状态", "time": "时间", "title": "响应", "waiting_for_connection": "等待连接", "xml": "XML", "sendRequestTip": "输入 URL 并点击发送以获取响应"}, "tab": {"authorization": "授权", "body": "请求体", "collections": "集合", "documentation": "帮助文档", "headers": "请求头", "history": "历史记录", "mqtt": "MQTT", "parameters": "参数", "pre_request_script": "预请求脚本", "queries": "查询", "query": "查询", "schema": "<PERSON><PERSON><PERSON>", "socketio": "Socket.IO", "sse": "SSE", "tests": "测试", "types": "类型", "variables": "变量", "websocket": "WebSocket"}, "test": {"passed": "通过", "failed": "失败", "javascript_code": "JavaScript 代码", "learn": "阅读文档", "report": "测试报告", "results": "测试结果", "script": "脚本", "snippets": "代码片段"}, "scf": {"tab": {"env": "环境", "envHeader": "环境设置", "param": "参数", "context": "隐式传参"}, "body": {"noParam": "此方法没有参数"}}}