import sdk from 'postman-collection';

import { ArexEnvironment, ArexRESTRequest } from '../../types';
import { ArexResponse } from '../../types/ArexResponse';
import { convertToPmBody } from './convertToPmBody';

export async function sendSCFRequest(
  request: ArexRESTRequest,
  environment?: ArexEnvironment,
): Promise<ArexResponse> {
  console.log('sendSCFRequest-请求参数', request);
  // @ts-ignore
  const runner = new window.ArexRuntime.Runner();

  // 创建请求对象的副本，而不是直接修改原始对象
  const requestCopy = { ...request };

  const parentPreRequestScripts = requestCopy.parentPreRequestScripts || '';
  const preRequestScript = requestCopy.preRequestScript || '';
  const combinedpreScript = [parentPreRequestScripts, preRequestScript].filter(Boolean).join('');
  requestCopy.preRequestScript = combinedpreScript;

  const parentTestScript = requestCopy.parentTestScripts || ''; // 获取父节点的 testScript
  const currentTestScript = requestCopy.testScript || ''; // 获取当前节点的 testScript
  // 合并脚本，父节点脚本在前，子节点脚本在后，用换行符分隔
  // 使用 filter(Boolean) 移除空脚本，避免多余的换行符
  // const combinedTestScript = [parentTestScript, currentTestScript].filter(Boolean).join('');
  const finalTestScript = currentTestScript.trim() || parentTestScript;

  // 更新 requestCopy 中的 testScript
  requestCopy.testScript = finalTestScript;

  // 替换 pm.response.responseTime 为 pm.response.json().cost
  let updatedTestScript = finalTestScript.replace(/pm\.response\.responseTime/g, 'pm.response.json().cost');

  // 替换 pm.response.json() 为 pm.response.json().result，但要避免影响已替换的 pm.response.json().cost
  updatedTestScript = updatedTestScript.replace(/pm\.response\.json\(\)(?!\.cost)/g, 'pm.response.json().result');

  // 更新 requestCopy 中的 testScript
  requestCopy.testScript = updatedTestScript;

  // 使用修改后的副本继续处理
  // arex数据接口转postman数据结构
  const rawCollection = {
    info: {
      _postman_id: '7b650e98-a5d2-4925-b23c-a4fb33a14832',
      name: 'test',
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      _exporter_id: '14623557',
    },
    item: [
      {
        name: requestCopy.name,
        event: [
          {
            listen: 'prerequest',
            script: {
              exec: [requestCopy.preRequestScript],
              type: 'text/javascript',
            },
          },
          {
            listen: 'test',
            script: {
              exec: [requestCopy.testScript],
              type: 'text/javascript',
            },
          },
        ],
        request: {
          // method: request.inherited ? request.inheritedMethod : request.method,
          method: 'POST',
          header: requestCopy.headers.filter((i) => i.active),
          body: {
            mode: 'raw',
            raw: {
              // ...(requestCopy?.body?.scfRequest || {}),
              ...(() => {
                const originalScfRequest = requestCopy?.body?.scfRequest || {};
                const convertEmoji = (obj: any): any => {
                  if (typeof obj === 'string') {
                    // 递归处理对象中的所有字符串值，将emoji转换为其Unicode表示
                    return obj.replace(/[\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F000}-\u{1F02F}]|[\u{1F0A0}-\u{1F0FF}]|[\u{1F100}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F900}-\u{1F9FF}]/gu, (match) =>
                      `[emoji:${match.codePointAt(0)?.toString(16)}]`
                    );
                  }
                  if (Array.isArray(obj)) {
                    return obj.map(convertEmoji);
                  }
                  if (obj && typeof obj === 'object') {
                    const result: any = {};
                    for (const key in obj) {
                      result[key] = convertEmoji(obj[key]);
                    }
                    return result;
                  }
                  return obj;
                };
                // 创建一个新的对象，包含转换后的 params
                return {
                  ...originalScfRequest,
                  params: originalScfRequest.params ? convertEmoji(originalScfRequest.params) : undefined
                };
              })()
            },
            options: {
              raw: {
                language: 'json',
              },
            },
          },
          // url: 'http://10.192.6.231:8001/iapi/scence/openapi/invokeScfV3',
          url: 'http://iapi.58corp.com/iapi/scence/openapi/invokeScfV3',
        },
        response: [],
      },
    ],
  };

  const collection = new sdk.Collection(rawCollection);
  let assertionsBox: any = [];
  const consolesBox: any = [];
  let res: any = {};
  return new Promise((resolve, reject) => {
    runner.run(
      collection,
      {
        environment: new sdk.VariableScope({
          name: environment?.name,
          values: environment?.variables,
        }),
        fileResolver: {
          stat(src: any, cb: any) {
            cb(null, {
              isFile: function () {
                return true;
              },
              mode: 33188, //权限
            });
          },
          createReadStream(base64: string) {
            return base64;
          },
        },
      },
      function (err: any, run: any) {
        run.start({
          assertion: function (cursor: any, assertions: any) {
            assertionsBox = [...assertionsBox, ...assertions];
          },
          console: function (cursor: any, level: any, ...logs: any) {
            consolesBox.push(logs);
          },
          prerequest: function (err: any, cursor: any, results: any, item: any) {
            // console.log('');
          },
          responseData: function (cursor: any, data: any) {
            // console.log('sendSCFRequest-responseData', data);
          },
          test: function (err: any, cursor: any, results: any, item: any) {
            // results: Array of objects. Each object looks like this:
            //  {
            //      error: Error,
            //      event: sdk.Event,
            //      script: sdk.Script,
            //      result: {
            //          target: 'test'
            //
            //          -- Updated environment
            //          environment: <VariableScope>
            //
            //          -- Updated globals
            //          globals: <VariableScope>
            //
            //          response: <sdk.Response>
            //          request: <sdk.Request>
            //          types: <Object of types variables>
            //          cookies: <Array of "sdk.Cookie" objects>
            //          tests: <Object>
            //          return: <Object, contains set next request params, etc>
            //      }
            //  }
          },
          item: function (err: any, cursor: any, item: any, visualizer: any) {
            resolve({
              response: res,
              testResult: assertionsBox,
              consoles: consolesBox,
              visualizer: visualizer,
            });
          },
          //调用一次，并对集合中的每个请求进行响应
          response: function (
            err: any,
            cursor: any,
            response: any,
            request: any,
            item: any,
            cookies: any,
            history: any,
          ) {
            if (!response.responseSize) {
              response.responseSize = String(response?.stream).length;
            }

            let responseTime = undefined;
            try {
              responseTime = JSON.parse(String(response?.stream)).cost;
            } catch (e) {
              responseTime = undefined;
            }

            res = {
              type: 'success', // TODO check response status
              headers: response?.headers.members,
              statusCode: response?.code,
              body: String(response?.stream),
              meta: {
                responseSize: response.responseSize, // in bytes
                responseDuration: responseTime || response.responseTime, // in millis
              },
            };
          },
        });
      },
    );
  });
}
