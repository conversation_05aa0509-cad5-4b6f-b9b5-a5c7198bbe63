import sdk from 'postman-collection';

import { TIMEOUT_REQUEST, TIMEOUT_SCRIPT } from '../../constant';
import { ArexEnvironment, ArexRESTRequest } from '../../types';
import { ArexResponse } from '../../types';
import { convertToPmBody } from './convertToPmBody';

// 处理流式响应的函数
function handleStreamingResponse(
  response: any,
  res: any,
  resolve: (value: ArexResponse) => void,
  additionalData: any,
  originalResponse?: any,
) {
  const reader = response.response?.reader;
  console.log('🔍 [Stream] Starting stream processing...');

  if (!reader || typeof reader.read !== 'function') {
    console.error('🔍 [handleStreamingResponse] Invalid reader, cannot process stream');
    // 处理无效reader的情况，将streaming类型转换为普通响应
    if (res && res.type === 'streaming') {
      // 尝试从原始响应中获取实际的响应体
      let actualBody = '';

      // 检查是否有原始响应数据可以使用
      console.log('🔍 [handleStreamingResponse] Original response data:', {
        hasOriginalResponse: !!originalResponse,
        originalResponseStream: originalResponse?.stream,
        originalResponseStreamType: typeof originalResponse?.stream,
        resBody: res.body,
        resBodyType: typeof res.body,
      });

      if (originalResponse && originalResponse.stream) {
        actualBody = String(originalResponse.stream);
      } else if (Array.isArray(res.body) && res.body.length > 0) {
        actualBody = res.body.join('');
      } else if (res.body && typeof res.body === 'string') {
        actualBody = res.body;
      }

      console.log(
        '🔍 [handleStreamingResponse] Converting to regular response with body:',
        actualBody,
      );

      // 将streaming类型转换为普通响应
      res = {
        type: 'success',
        headers: res.headers,
        statusCode: res.statusCode || 200,
        body: actualBody,
        meta: {
          responseSize: actualBody.length,
          responseDuration: res.startTime ? Date.now() - res.startTime : 0,
        },
      };
    }
    // 由于还没有定义resolveOnce，这里先直接resolve
    resolve({
      response: res,
      testResult: additionalData.testResult,
      consoles: additionalData.consoles,
      visualizer: additionalData.visualizer,
    });
    return;
  }

  const decoder = new TextDecoder();
  let buffer = '';

  // 不立即resolve，等待流处理完成后再resolve
  let isResolved = false;

  function resolveOnce(result: ArexResponse) {
    if (!isResolved) {
      isResolved = true;
      resolve(result);
    }
  }

  function processStream() {
    reader
      .read()
      .then(({ done, value }: { done: boolean; value?: Uint8Array }) => {
        if (done) {
          // 流式传输完成
          if (res && res.type === 'streaming') {
            res.isComplete = true;
          }
          console.log('🔍 [Stream] Completed with', res.body.length, 'chunks');
          // let parsedBody = res.body;
          if (res && res.type === 'streaming') {
            // parsedBody = res.body.map((chunk: string) => {
            //   console.log('🔍 [Stream] forEach Chunk:', chunk);
            //   if (chunk && chunk.startsWith('data:')) {
            //     chunk = chunk.substring(5);
            //     console.log('🔍 [Stream] forEach Chunk substring:', atob(chunk));
            //     return atob(chunk);
            //   }
            //   return chunk;
            // });
            // // res.body = JSON.parse(parsedBody);
            // res.body = parsedBody;
            // let concatRes = '';
            // res.body.forEach((element: string) => {
            //   concatRes +=
            //     JSON.parse(element).data.streamResp == null
            //       ? ''
            //       : JSON.parse(element).data.streamResp;
            // });
            // console.log('🔍 [Stream] Completed with', concatRes, 'concatRes');
            res.isComplete = true;
            // console.log('🔍 [Stream] Completed with', res.body, 'parsedBody res.body');

            // 调用resolveOnce返回最终结果
            resolveOnce({
              response: {
                type: 'success',
                body: res.body,
                statusCode: res.statusCode,
                headers: res.headers,
                meta: {
                  responseSize: JSON.stringify(res.body).length,
                  responseDuration: Date.now() - (res.startTime || Date.now()),
                },
              },
              testResult: additionalData.testResult || [],
              consoles: additionalData.consoles || [],
              visualizer: {
                data: res.body,
                error: null,
                processedTemplate: '',
              },
            });
          }
          return;
        }

        if (value) {
          // 解码数据块
          const chunk = decoder.decode(value, { stream: true });
          // console.log('🔍 [Stream Debug] Raw chunk:', chunk);
          buffer += chunk;

          // 按行处理数据
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留最后一个不完整的行

          lines.forEach((line) => {
            if (line.trim()) {
              // 处理分割的数据：如果行只是'data:'，跳过等待下一个chunk
              if (line.trim() === 'data:') {
                return;
              }

              if (line.startsWith('data:')) {
                const rawData = line.substring(5); // 移除 'data:' 前缀
                // console.log('🔍 [Stream Debug] Raw data:', rawData);

                // 正确处理中文编码：先base64解码，再UTF-8解码
                let content: string;
                try {
                  const binaryString = atob(rawData);
                  // 将二进制字符串转换为Uint8Array
                  const bytes = new Uint8Array(binaryString.length);
                  for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                  }
                  // 使用TextDecoder正确解码UTF-8
                  content = new TextDecoder('utf-8').decode(bytes);
                  // console.log('🔍 [Stream Debug] Decoded data:', content);
                } catch (error) {
                  console.error('🔍 [Stream Debug] Decode error:', error);
                  content = atob(rawData); // 降级到原始方法
                  // console.log('🔍 [Stream Debug] Fallback decoded data:', content);
                }

                // 安全地添加到 body 数组
                if (Object.isExtensible(res.body)) {
                  res.body.push(content);
                } else {
                  // console.warn('🔍 [Stream] Body array not extensible, recreating');
                  res.body = [...res.body, content];
                }
              }
            }
          });

          // 继续读取下一个数据块
          processStream();
        }
      })
      .catch((error: any) => {
        console.error('Stream reading error:', error);
        if (res && res.type === 'streaming') {
          res.isComplete = true;
        }
        // 使用resolveOnce处理错误情况
        console.error('🔍 [Stream Error] Stream processing failed:', error.message);
        resolveOnce({
          response: {
            type: 'fail',
            body: res?.body || [`Stream processing error: ${error.message}`],
            statusCode: res?.statusCode || 500,
            headers: res?.headers || [],
            meta: {
              responseSize: res?.body ? JSON.stringify(res.body).length : 0,
              responseDuration: res?.startTime ? Date.now() - res.startTime : 0,
            },
          },
          testResult: additionalData.testResult || [],
          consoles: additionalData.consoles || [],
          visualizer: {
            data: res?.body || null,
            error: error.message,
            processedTemplate: '',
          },
        });
      });
  }

  // 开始处理流
  processStream();
}

export async function sendRequest(
  request: ArexRESTRequest,
  environment?: ArexEnvironment,
): Promise<ArexResponse> {
  // @ts-ignore
  const runner = new window.ArexRuntime.Runner();

  const parentPreRequestScripts = request.parentPreRequestScripts || '';
  const preRequestScript = request.preRequestScript || '';
  const combinedpreScript = [parentPreRequestScripts, preRequestScript]
    .filter(Boolean)
    .join('\n\n');
  request.preRequestScript = combinedpreScript;

  const parentTestScript = request.parentTestScripts || ''; // 获取父节点的 testScript
  const currentTestScript = request.testScript || ''; // 获取当前节点的 testScript

  // const combinedTestScript = [parentTestScript, currentTestScript].filter(Boolean).join('\n\n');
  // 如果当前测试脚本为空，使用父测试脚本，否则使用当前测试脚本
  const finalTestScript = currentTestScript.trim() || parentTestScript;

  // 更新 requestTmp 中的 testScript
  request.testScript = finalTestScript;

  // 处理 shadowcookie 和 shadowhost
  let shadowcookie = '';
  let shadowhost = '';

  request?.headers.forEach((header: any) => {
    if (header.key.toLowerCase() === 'cookie') {
      shadowcookie = `${header.value};${shadowcookie}`;
    }
    if (header.key.toLowerCase() === 'host') {
      shadowhost = `${header.value}`;
    }
  });

  if (shadowcookie.length > 0) {
    const newHeaders = [...request.headers];
    newHeaders.push({
      key: 'shadowcookie',
      value: shadowcookie,
      active: true,
    });
    request.headers = newHeaders;
  }

  if (shadowhost.length > 0) {
    const newHeaders = [...request.headers];
    newHeaders.push({
      key: 'shadowhost',
      value: shadowhost,
      active: true,
    });
    request.headers = newHeaders;
  }

  // arex数据接口转postman数据结构
  const rawCollection = {
    info: {
      _postman_id: '7b650e98-a5d2-4925-b23c-a4fb33a14832',
      name: 'test',
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      _exporter_id: '14623557',
    },
    item: [
      {
        name: request.name,
        event: [
          {
            listen: 'prerequest',
            script: {
              exec: [request.preRequestScript],
              type: 'text/javascript',
            },
          },
          {
            listen: 'test',
            script: {
              exec: [request.testScript],
              type: 'text/javascript',
            },
          },
        ],
        request: {
          method: request.inherited ? request.inheritedMethod : request.method,
          header: request.headers.filter((i) => i.active),
          body: convertToPmBody(request.body),
          url: sdk.Url.parse(request.inherited ? request.inheritedEndpoint : request.endpoint),
        },
        response: [],
      },
    ],
  };

  const collection = new sdk.Collection(rawCollection);
  let assertionsBox: any = [];
  const consolesBox: any = [];
  let res: any = {};
  return new Promise((resolve, reject) => {
    runner.run(
      collection,
      {
        timeout: {
          request: TIMEOUT_REQUEST,
          script: TIMEOUT_SCRIPT,
        },
        environment: new sdk.VariableScope({
          name: environment?.name,
          values: environment?.variables,
        }),
        fileResolver: {
          stat(src: any, cb: any) {
            cb(null, {
              isFile: function () {
                return true;
              },
              mode: 33188, //权限
            });
          },
          createReadStream(base64: string) {
            return base64;
          },
        },
      },
      function (err: any, run: any) {
        run.start({
          assertion: function (cursor: any, assertions: any) {
            assertionsBox = [...assertionsBox, ...assertions];
          },
          console: function (cursor: any, level: any, ...logs: any) {
            consolesBox.push(logs);
          },
          prerequest: function (err: any, cursor: any, results: any, item: any) {
            // console.log('');
          },
          responseData: function (cursor: any, data: any) {
            // console.log('');
          },
          test: function (err: any, cursor: any, results: any, item: any) {
            // results: Array of objects. Each object looks like this:
            //  {
            //      error: Error,
            //      event: sdk.Event,
            //      script: sdk.Script,
            //      result: {
            //          target: 'test'
            //
            //          -- Updated environment
            //          environment: <VariableScope>
            //
            //          -- Updated globals
            //          globals: <VariableScope>
            //
            //          response: <sdk.Response>
            //          request: <sdk.Request>
            //          types: <Object of types variables>
            //          cookies: <Array of "sdk.Cookie" objects>
            //          tests: <Object>
            //          return: <Object, contains set next request params, etc>
            //      }
            //  }
          },
          item: function (err: any, cursor: any, item: any, visualizer: any) {
            resolve({
              response: res,
              testResult: assertionsBox,
              consoles: consolesBox,
              visualizer: visualizer,
            });
          },
          //调用一次，并对集合中的每个请求进行响应
          response: function (
            err: any,
            cursor: any,
            response: any,
            request: any,
            item: any,
            cookies: any,
            history: any,
          ) {
            // 检查是否是流式请求
            const isStreamingRequest = request?.headers?.members?.some(
              (header: any) =>
                header.key.toLowerCase() === 'accept' && header.value.includes('text/event-stream'),
            );

            // 检查响应是否有stream属性，并且stream对象有reader属性
            const hasValidStreamProperty =
              response &&
              'stream' in response &&
              response.stream &&
              response.stream.reader &&
              typeof response.stream.reader.read === 'function';

            console.log('🔍 [sendRequest] Request analysis:', {
              isStreamingRequest,
              hasValidStreamProperty,
              responseStream: response?.stream,
              responseType: typeof response?.stream,
            });

            if (isStreamingRequest && hasValidStreamProperty) {
              console.log('🔍 [sendRequest] Processing streaming response...');

              // 创建可扩展的数组
              const bodyArray: any[] = [];

              // 处理流式响应 - 从axiosGuard的新结构中提取数据
              res = {
                type: 'streaming',
                headers: response?.headers || [],
                statusCode: response?.status || 200,
                body: bodyArray, // 直接使用创建的数组
                isComplete: false,
                startTime: Date.now(),
                meta: {
                  responseSize: 0, // 流式响应大小待计算
                  responseDuration: 0, // 流式响应时间待计算
                },
              };

              // 验证数组可扩展性
              if (!Object.isExtensible(res.body)) {
                console.warn('🔍 [sendRequest] Body array not extensible, recreating');
                res.body = [];
                // 强制设置为可扩展
                Object.defineProperty(res, 'body', {
                  value: [],
                  writable: true,
                  enumerable: true,
                  configurable: true,
                });
              }

              // 创建一个包装对象，模拟 axiosGuard 的结构
              const streamWrapper = {
                response: {
                  // 直接使用 stream 对象中的 reader，而不是调用 getReader()
                  reader: response?.stream?.reader || null,
                },
              };

              handleStreamingResponse(
                streamWrapper,
                res,
                resolve,
                {
                  testResult: assertionsBox,
                  consoles: consolesBox,
                  visualizer: null,
                },
                response,
              );
            } else if (isStreamingRequest && !hasValidStreamProperty) {
              // 处理流式请求但响应不是流式的情况
              console.log(
                '🔍 [sendRequest] Stream request but non-stream response, converting to regular response',
              );
              res = {
                type: 'success',
                headers: response?.headers.members,
                statusCode: response?.code,
                body: String(response?.stream),
                meta: {
                  responseSize: response?.responseSize, // in bytes
                  responseDuration: response?.responseTime, // in millis
                },
              };
            } else {
              res = {
                type: 'success', // TODO check response status
                headers: response?.headers.members,
                statusCode: response?.code,
                body: String(response?.stream),
                meta: {
                  responseSize: response?.responseSize, // in bytes
                  responseDuration: response?.responseTime, // in millis
                },
              };
            }
          },
        });
      },
    );
  });
}
