import { getLocalStorage } from '@arextest/arex-core';
import axios from 'axios';

import { ACCESS_TOKEN_KEY, EMAIL_KEY } from '../../components/Request/InterfaceHistory/constrants';

const USER_ACTION_URL = '/webApi/event/addLog';

export enum UserActionEnum {
  SEND_HTTP = 0,
  SEND_SCF = 1,
  SEND_WMB_PRODUCER = 2,
  SEND_WMB_CONSUMER = 3,
}

export async function addUserAction(eventId: number, params?: object) {
  const accessToken = getLocalStorage<string>(ACCESS_TOKEN_KEY);
  const config = {
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Access-Token': accessToken,
    },
  };
  const operatorOA = getLocalStorage(EMAIL_KEY) as string;
  try {
    await axios.post(USER_ACTION_URL, { eventId, operatorOA, params }, config);
  } catch (error) {
    console.warn(error);
  }
}
