import sdk from 'postman-collection';

import { ArexEnvironment, ArexRESTRequest } from '../../types';
import { ArexResponse } from '../../types/ArexResponse';
import { IWmb_Consumer } from '../../types/ArexRESTReqBody';
import { convertToPmBody } from './convertToPmBody';

export async function sendWmbConsumerRequest(
  request: ArexRESTRequest,
  environment?: ArexEnvironment,
): Promise<ArexResponse> {
  console.log('sendWmbConsumerRequest.tx-->request', request);
  console.log("request?.body--->", request?.body);
  // @ts-ignore
 
  const runner = new window.ArexRuntime.Runner();
  

  let wmb_mock_JsonObject: IWmb_Consumer;

  if (request?.body?.wmb_mock) {
    wmb_mock_JsonObject = JSON.parse(request?.body?.wmb_mock) as IWmb_Consumer;
  }else{
    wmb_mock_JsonObject = {};
  }
  const msgBody = wmb_mock_JsonObject?.msgBody;
  const msgBodyArray = Array.isArray(msgBody) ? msgBody : [msgBody];
  console.log('msgBody--->', msgBody );
  console.log('msgBodyArray--->', msgBodyArray );



  // const clientId_first = wmb_mock_JsonObject?.clientId;
  // var clientId;
  // // 使用空格分割字符串
  // if(clientId_first != undefined && clientId_first !=""){
  //   var parts = clientId_first.split(' - ');
  //   // 提取结果
  //   clientId = parts[0];
  // }
  // else{
  //   clientId = undefined
  // }




  

  const Wmb_Mock_requestParams = {
    userName: wmb_mock_JsonObject?.userName,
    subject: wmb_mock_JsonObject?.subject,
    clientId: wmb_mock_JsonObject?.clientId,   //提取结果 字符串分割
    clientType: wmb_mock_JsonObject?.clientType,
    keyName: wmb_mock_JsonObject?.keyName,
    colorId: wmb_mock_JsonObject?.colorId,
    tags: wmb_mock_JsonObject?.tags,
    consumeIp: wmb_mock_JsonObject?.consumeIp,
    msgBody: msgBodyArray,   //msgBody需要转换成数组
    env: wmb_mock_JsonObject?.env
  }
  
  console.log("请求/api/wmb/key/client/mock--最终转换参数--->", Wmb_Mock_requestParams);



  // const { msgBody } = request?.body?.wmb_Consumer?.msgBody;
  // const msgBodyArray = Array.isArray(msgBody) ? msgBody : [msgBody];

  // if (request && request.body && request.body.wmb_Consumer) {
  //   request.body.wmb_Consumer.msgBody = msgBodyArray;
  // } else {
  //   console.error('Unable to set msgBody: Missing parts of the request object.');
  // }



  // arex数据接口转postman数据结构
  const rawCollection = {
    info: {
      _postman_id: '7b650e98-a5d2-4925-b23c-a4fb33a14832',
      name: 'test',
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      _exporter_id: '14623557',
    },
    item: [
      {
        name: request.name,
        event: [
          {
            listen: 'prerequest',
            script: {
              exec: [request.preRequestScript],
              type: 'text/javascript',
            },
          },
          {
            listen: 'test',
            script: {
              exec: [request.testScript],
              type: 'text/javascript',
            },
          },
        ],
        request: {
          // method: request.inherited ? request.inheritedMethod : request.method,
          method: 'POST',
          header: request.headers.filter((i) => i.active),
          body: {
            mode: 'raw',
            //raw: request?.body?.wmb_Consumer || {},
            raw: Wmb_Mock_requestParams || {},
            options: {
              raw: {
                language: 'json',
              },
            },
          }, 
          url: `http://${window.location.hostname}/webApi/wmb/key/client/mock`,
          //url: `http://arex-api.58dns.org/api/wmb/key/client/mock`,
          //url: `http://*************:8090/api/wmb/key/client/mock`,   //调试
        },
        response: [],
      },
    ],
  };

  const collection = new sdk.Collection(rawCollection);
  let assertionsBox: any = [];
  const consolesBox: any = [];
  let res: any = {};
  return new Promise((resolve, reject) => {
    runner.run(
      collection,
      {
        environment: new sdk.VariableScope({
          name: environment?.name,
          values: environment?.variables,
        }),
        fileResolver: {
          stat(src: any, cb: any) {
            cb(null, {
              isFile: function () {
                return true;
              },
              mode: 33188, //权限
            });
          },
          createReadStream(base64: string) {
            return base64;
          },
        },
      },
      function (err: any, run: any) {
        run.start({
          assertion: function (cursor: any, assertions: any) {
            assertionsBox = [...assertionsBox, ...assertions];
          },
          console: function (cursor: any, level: any, ...logs: any) {
            consolesBox.push(logs);
          },
          prerequest: function (err: any, cursor: any, results: any, item: any) {
            // console.log('');
          },
          responseData: function (cursor: any, data: any) {
            // console.log('');
          },
          test: function (err: any, cursor: any, results: any, item: any) {
            // results: Array of objects. Each object looks like this:
            //  {
            //      error: Error,
            //      event: sdk.Event,
            //      script: sdk.Script,
            //      result: {
            //          target: 'test'
            //
            //          -- Updated environment
            //          environment: <VariableScope>
            //
            //          -- Updated globals
            //          globals: <VariableScope>
            //
            //          response: <sdk.Response>
            //          request: <sdk.Request>
            //          types: <Object of types variables>
            //          cookies: <Array of "sdk.Cookie" objects>
            //          tests: <Object>
            //          return: <Object, contains set next request params, etc>
            //      }
            //  }
          },
          item: function (err: any, cursor: any, item: any, visualizer: any) {
            resolve({
              response: res,
              testResult: assertionsBox,
              consoles: consolesBox,
              visualizer: visualizer,
            });
          },
          //调用一次，并对集合中的每个请求进行响应
          response: function (
            err: any,
            cursor: any,
            response: any,
            request: any,
            item: any,
            cookies: any,
            history: any,
          ) {
            res = {
              type: 'success', // TODO check response status
              headers: response?.headers.members,
              statusCode: response?.code,
              body: String(response?.stream),
              meta: {
                responseSize: response.responseSize, // in bytes
                responseDuration: response.responseTime, // in millis
              },
            };
          },
        });
      },
    );
  });
}
