export type SCFRequest = {
  // serviceID: any;
  // serviceID: any;
  scfId?: string;
  clusterName?: string;
  scfServiceId?: string;
  scfServiceName?: string;
  interfaceName?: string;
  implClassName?: string;
  methodName?: string;
  branchVersion?: string;
  branchList?: scfInvisibleParams[];
  connectType?: string;
  envID?: string;
  clusterGroup?: string;
  ip?: string;
  ipOptions?: IpOptions;
  port?: string;
  isOnlineScfKey?: string;
  scfKey?: string;
  protocolType?: string;
  serializedVersion?: string;
  params?: scfParams[];
  invisibleParams?: scfInvisibleParams[];
  operatorUser?: string;
};

export type scfParams = { paramKey: string; exampleValue: string };

export type scfInvisibleParams = { key: string; value: string };

export type IpOptions = {
  test: string[];
  stable: { [key: string]: string[] };
  sandbox: string[];
  online: { [key: string]: string[] };
  others: string[];
};
