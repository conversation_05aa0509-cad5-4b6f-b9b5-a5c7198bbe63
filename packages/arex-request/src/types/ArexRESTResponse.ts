// 修改点
// 1.删除了 req ArexRESTRequest
// 2.body 为any
import { ArexRESTHeader } from './ArexRESTHeader';

export type ArexRESTResponse =

  | {
    type: 'loading';
    headers: undefined;
    body?: string;
  }
  | { type: 'extensionNotInstalled'; headers: undefined; body?: string }
  | {
    type: 'fail';
    headers: ArexRESTHeader[];
    body: any;
    statusCode: number;
    meta: {
      responseSize: number; // in bytes
      responseDuration: number; // in millis
    };
  }
  | {
    type: 'network_fail';
    headers: undefined;
    error: Error;
    body?: string
  }
  | {
    type: 'script_fail';
    headers: undefined;
    error: Error;
    body?: string;
  }
  | {
    type: 'success';
    headers: ArexRESTHeader[];
    body: any;
    statusCode: number;
    meta: {
      responseSize: number; // in bytes
      responseDuration: number; // in millis
    };
  }
  | {
    type: 'streaming';
    headers: ArexRESTHeader[];
    body: string[];
    statusCode: number;
    meta: {
      responseSize: number; // in bytes
      responseDuration: number; // in millis
    };
    isComplete?: boolean;
  };
