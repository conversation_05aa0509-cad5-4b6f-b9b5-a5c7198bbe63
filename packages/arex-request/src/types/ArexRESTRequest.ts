import { ArexRESTAuth } from './ArexRESTAuth';
import { ArexRESTHeader } from './ArexRESTHeader';
import { ArexRESTParam } from './ArexRESTParam';
import { ArexRESTReqBody, IWmb, IWmb_Consumer } from './ArexRESTReqBody';

// 添加参数契约类型定义
export interface ParamContract {
  name: string;
  type: string;
  desc: string;
  key: string;
  children?: ParamContract[];
  parentKey?: string;
}

export interface ArexRESTRequest {
  clusterName: any;
  serviceID: any;
  v?: string;
  id: string; // Firebase Firestore ID
  name: string;
  method: string;
  //requestType: 'HTTP' | 'WMB' | 'SCF' | 'WMB_Consumer';
  requestType: 'HTTP' | 'WMB' | 'SCF' | 'WMB_MOCK' | 'HTTP_COMPARE' | 'SCF_COMPARE';
  endpoint: string;
  params: ArexRESTParam[];
  headers: ArexRESTHeader[];
  preRequestScript: string;
  testScript: string;
  parentPreRequestScripts: string;
  parentTestScripts: string;
  auth: ArexRESTAuth;
  body: ArexRESTReqBody;
  // add for arex
  inherited: boolean;
  inheritedEndpoint: string;
  inheritedMethod: string;
  description: string;
  // 添加参数契约字段
  contract?: ParamContract[];
  // scf
  // scfEnvType?: string;
  // scfIP?: string;

  scfID?: string;
  serviceNameValue?: string;
  interfaceNameValue?: string;
  functionNameValue?: string;

  wmb?: IWmb;
  wmb_Consumer?: IWmb_Consumer;
  needUpdate?: boolean;
  recordId?: string;
}
