import { ArexContentTypes } from './ArexContentTypes';
import { ArexRESTReqBodyFormData } from './ArexRESTReqBodyFormData';
import { SCFRequest } from './SCFRequest';

export interface IWmb {
  subject?: number;
  key?: string;
  registry_server_ip?: string;
  registry_server_port?: number;
}

export interface IWmb_Consumer {
  userName?: string;
  subject?: number;
  clientId?: number;
  clientType?: string;
  keyName?: string;
  colorId?: number;
  tags?: string;
  consumeIp?: string;
  msgBody?: any;
  env?: string;
}

export type ArexRESTReqBody =
  | {
      contentType: ArexContentTypes;
      body: string;
      wmb?: IWmb;
      //wmb_mock?: IWmb_Consumer;
      //wmb_mock?: JSON;
      wmb_mock?: string;
      scf?: string;
      scfRequest: SCFRequest;
      wmb_Consumer?: IWmb_Consumer;
      formData: { key: string; value: string }[];
    }
  | (ArexRESTReqBodyFormData & { wmb?: IWmb } & {
      scf?: string;
    } & { scfRequest: SCFRequest } & {
      wmb_Consumer?: IWmb_Consumer;
    } & { scfRequest: SCFRequest } & {
      wmb_mock?: string;
    } & { formData: { key: string; value: string }[] })
  | {
      contentType: undefined;
      body: undefined;
      wmb?: IWmb;
      scf?: string;
      //wmb_mock?: JSON;
      wmb_mock?: string;
      //wmb_mock?: string;
      scfRequest: SCFRequest;
      wmb_Consumer?: IWmb_Consumer;
      formData: { key: string; value: string }[];
    };
