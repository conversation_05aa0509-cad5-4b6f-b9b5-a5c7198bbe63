import { BorderOuterOutlined } from '@ant-design/icons';
import { copyToClipboard, css, styled } from '@arextest/arex-core';
import { getLocalStorage } from '@arextest/arex-core';
import { App, AutoComplete, Form, Input, InputNumber, Radio, Select, Space } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import axios from 'axios';
import { time } from 'console';
import { values } from 'lodash';
import PM from 'postman-collection';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { sendWmbConsumerRequest } from '../../../helpers';
import { useArexRequestStore } from '../../../hooks';
import { ArexRESTParam } from '../../../types';
import { IWmb_Consumer } from '../../../types/ArexRESTReqBody';
const { Option } = AutoComplete;


const RequestParameters = () => {
  const { t } = useTranslation();
  const { message } = App.useApp();
  const { store, dispatch } = useArexRequestStore();
  const { endpoint, params } = store.request;
  const [form] = Form.useForm<IWmb_Consumer>();

  const setParams = (params: ArexRESTParam[]) => {
    dispatch((state) => {
      state.request.params = params;
    });
  };

  const handleWmbConsumerChange = () => {
    dispatch((state) => {
      const newVal = form.getFieldsValue() || {};
      state.request.body.wmb_Consumer = newVal;
      console.log("handleWmbConsumerChange的newVal---->",newVal);
      //let wmbMockObjChange: IWmb_Consumer = state.request?.body?.wmb_mock as IWmb_Consumer;
      //wmbMockObjChange = newVal;
    });
  };

  const handleWmbMockConsumerChange = () => {
    dispatch((state) => {
      const newVal = form.getFieldsValue() || {};
      if (store.request?.body?.wmb_mock) {
        let wmbMockObj: IWmb_Consumer = JSON.parse(store.request?.body?.wmb_mock) as IWmb_Consumer;
        wmbMockObj = newVal
        //state.request.body.wmb_mock = newVal;
      }
    });
  };

  interface ClientInfoOption {
    value: string;
    keyName: string;
    clientType: string;
    label: string;
  }

  interface ClientByKeyInfoOption {
    value: string;
    keyName: string;
    subject: number;
    clientId: number;
    clientType: string;
    label: string;
  }


  const [result, setResult] = useState();
  const [subjectArray, setSubjectArray] = useState();

  const [parentOptions, setParentOptions] = useState<string[]>([]);
  const [parentInfoOptions, setParentInfoOptions] = useState([{}]);
  const [wmbKeysInfoOptions, setWmbKeysInfoOptions] = useState([{}]);
  const [childOptions, setChildOptions] = useState([]);
  const [clientInfoOptions, setClientInfoOptions] = useState<ClientInfoOption[]>([]);
  const [wmbClientByKeyInfoOptions, setWmbClientByKeyInfoOptions] = useState<ClientByKeyInfoOption[]>([]);
  const [selectedParent, setSelectedParent] = useState(null);
  const [selectedEnv, setSelectedEnv] = useState();

  const [subjectIdChoose, setSubjectIdChoose] = useState(Number);
  const [consumeIpStatus, setConsumeIpStatus] = useState(Boolean);
  const [consumeIpDisplayStatus, setConsumeIpDisplayStatus] = useState("none");

  //const email = 'taozhen';
  const email = getLocalStorage<string>('email');

  //初始赋值
  useEffect(() => {
    console.log("store.request----->", store.request)
    console.log("store.request.body.wmb_mock----->", store.request.body.wmb_mock)
    if (store.request?.body?.wmb_mock) {
      const wmbMockObj: IWmb_Consumer = JSON.parse(store.request?.body?.wmb_mock) as IWmb_Consumer;
      store.request?.body?.wmb_mock
        ? form.setFieldsValue(wmbMockObj)
        : form.resetFields();
        console.log("FieldsValue----->", form.getFieldsValue());  
      //handleWmbMockConsumerChange();
    } else {
      const wmbMockObj = {};
    }

    //初始获取subject、WmbKeys的值
    if (form.getFieldValue("env")) {
      getSubjectId(form.getFieldValue("env"));
      getWmbKeys(form.getFieldValue("env"));
    } else {
      getSubjectId("ONLINE");
      getWmbKeys("ONLINE");
    }
  }, [store.request?.body?.wmb_mock]);



  //获取主题下拉列表
  const getSubjectId: (env: string) => Promise<any> = async (env) => {
    const response = await axios.post(`/webApi/wmb/query/getWmbSubjects`,
    { "userName": email, "env": env, "subject": null, "key": null, "pageNum": 1, "pageSize": 100 },
    {headers: {'Content-Type': 'application/json;charset=UTF-8'}});

    console.log('getSubjectId返回值data----->', response.data);
    const res_pageList = response.data?.data?.pageList
    setResult(res_pageList);
    // 使用map方法提取subject属性
    const subjectArray = res_pageList?.map((item: { subject: any; }) => item.subject);
    console.log("subjectArray--->", subjectArray);
    setParentOptions(subjectArray);

    const parentInfoOptions = res_pageList.map((item: { subject: any; orgName: any; description: any; }) => ({
      value: item.subject,
      label: `${item.subject} - ${item.orgName} - ${item.description}`,
    }));

    console.log("parentInfoOptions--->", parentInfoOptions);
    setParentInfoOptions(parentInfoOptions);
    form.setFieldsValue({ env: env, userName: email });   //设置env环境
  };


  //获取ClientId下拉列表
  const getClientId: (subject: number, env: string) => Promise<any> = async (subject, env) => {
    const response = await axios.post(`/webApi/wmb/query/getWmbClientBySubject`,
      { "userName": email, "env": env, "subject": subject, "key": null, "pageNum": 1, "pageSize": 100 },
      {headers: {'Content-Type': 'application/json;charset=UTF-8'}});

    console.log('getClientId返回值data----->', response.data);
    const res_client_pageList = response.data?.data?.pageList

    const clientIdArray = res_client_pageList?.map((item: { clientId: any; }) => item.clientId);
    console.log("clientIdArray--->", clientIdArray);
    setChildOptions(clientIdArray);

    //map报错
    const clientInfoOptions = res_client_pageList.map((item: { clientId: any; clientType: any; key: any; orgName: any; description: any; }) => ({
      value: `${item.clientId} - ${item.clientType}`,
      clientType: item.clientType,
      keyName: item.key,
      label: `${item.clientId} - ${item.clientType} - ${item.orgName} - ${item.description}`,
    }));

    console.log("clientInfoOptions--->", clientInfoOptions);
    setClientInfoOptions(clientInfoOptions);
  };


  //通过OA账号获取wmb密钥
  const getWmbKeys: (env: string) => Promise<any> = async (env) => {
    const response = await axios.post(`/webApi/wmb/query/getWmbKeys`,
      { "userName": email, "env": env, "subject": null, "key": null, "pageNum": 1, "pageSize": 100 },
      {headers: {'Content-Type': 'application/json;charset=UTF-8'}});

    console.log('getWmbKeys返回值data----->', response.data);
    const res_wmbKey_pageList = response.data?.data?.pageList

    const wmbKeysInfoOptions = res_wmbKey_pageList.map((item: { key: any; orgName: any; description: any; }) => ({
      value: item.key,
      label: `${item.key} - ${item.orgName} - ${item.description}`,
    }));

    console.log("wmbKeysInfoOptions--->", wmbKeysInfoOptions);
    setWmbKeysInfoOptions(wmbKeysInfoOptions);
    form.setFieldsValue({ env: env, userName: email });   //设置env环境
  };


  //通过密钥key获取WmbClientId相关信息下拉列表
  const getWmbClientByKey: (key: string, env: string) => Promise<any> = async (key, env) => {
    const response = await axios.post(`/webApi/wmb/query/getWmbClientByKey`,
      { "userName": email, "env": env, "key": key, "subject": null, "pageNum": 1, "pageSize": 100 },
      {headers: {'Content-Type': 'application/json;charset=UTF-8'}});

    console.log('getWmbClientByKey返回值data----->', response.data);
    const res_WmbClient_pageList = response.data?.data?.pageList

    const wmbClientByKeyInfoOptions = res_WmbClient_pageList.map((item: { subject: any; clientId: any; clientType: any; key: any; orgName: any; description: any; }) => ({
      value: `主题：${item.subject} - 客户端ID：${item.clientId}(${item.clientType})`,
      clientType: item.clientType,
      clientId: item.clientId,
      keyName: item.key,
      subject: item.subject,
      label: `主题：${item.subject} - 客户端ID：${item.clientId}(${item.clientType}) - 用途描述：${item.description}`,
    }));

    console.log("wmbClientByKeyInfoOptions--->", wmbClientByKeyInfoOptions);
    setWmbClientByKeyInfoOptions(wmbClientByKeyInfoOptions);
  };



  //更新环境，触发subject更新
  const handleEnvSelect = (value: any) => {
    setSelectedEnv(value);
    //切换环境就清空
    form.setFieldsValue({ clientId: undefined, subject: undefined });
    getSubjectId(form.getFieldValue("env"));
    getWmbKeys(form.getFieldValue("env"));
  };


  //SubjectId和ClientId联动，选择subjectId后，set对应的ClientId选项
  const handleSubjectIdSelect = (value: any) => {
    setSelectedParent(value);
    getClientId(form.getFieldValue("subject"), form.getFieldValue("env"));
  };

  //wmbKey和WmbClient信息联动，选择wmbKey后，set对应的ClientId选项
  const handleWmbKeySelect = (value: any) => {
    setSelectedParent(value);
    getWmbClientByKey(form.getFieldValue("keyName"), form.getFieldValue("env"));
  };


  //SubjectId下拉框获取option
  const renderSubjectsOptions = (parentInfoOptions: any) =>
    parentInfoOptions.map((parentInfo: { value: React.Key | null | undefined; label: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; }) => (
      <Option key={parentInfo.value} value={parentInfo.value}>
        {parentInfo.label}
      </Option>
    ));

  //wmbKey下拉框获取option
  const renderWmbKeysOptions = (wmbKeysInfoOptions: any) =>
  wmbKeysInfoOptions.map((parentInfo: { value: React.Key | null | undefined; label: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; }) => (
    <Option key={parentInfo.value} value={parentInfo.value}>
      {parentInfo.label}
    </Option>
  ));  


  //ClientId下拉框获取option
  const renderClientOptions = (clientInfoOptions: any) =>
    clientInfoOptions.map((clientInfoOption: { value: React.Key | null | undefined; label: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; }) => (
      <Option key={clientInfoOption.value} value={clientInfoOption.value}>
        {clientInfoOption.label}
      </Option>
    ));

  
  //WmbClient信息下拉框获取option
  const renderWmbClientByKeyOptions = (wmbClientByKeyOptions: any) =>
    wmbClientByKeyOptions.map((wmbClientByKeyOption: { value: React.Key | null | undefined; label: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; }) => (
      <Option key={wmbClientByKeyOption.value} value={wmbClientByKeyOption.value}>
        {wmbClientByKeyOption.label}
      </Option>
  ));



  //判断消费方IP是否  disable 
  const handleConsumeIpStatus = (value: any) => {
    const selectedOption = clientInfoOptions.find(option => option.value === value);
    // 如果找到了对应的对象，则获取其 label
    if (selectedOption) {
      const selectedkeyName = selectedOption.keyName;
      const selectedclientType = selectedOption.clientType;
      //客户端是消费者，消费者IP是可输入的，否则disable
      if (selectedclientType == "消费者") {
        setConsumeIpStatus(false);
      }
      else {
        setConsumeIpStatus(true);
      }

      form.setFieldsValue({ env: form.getFieldValue("env"), userName: email, keyName: selectedkeyName, clientType: selectedclientType });
      console.log("form.getFieldsValue--->", form.getFieldsValue());
    } else {
      console.log('No option found for value:', value);
    }
  };


  //重要-->更新消费方IP可用 && 更新各field状态 
  const handleConsumeIpAndWmbKeyInfoField = (value: any) => {
    const selectedOption = wmbClientByKeyInfoOptions.find(option => option.value === value);
    // 如果找到了对应的对象，则获取其 label
    if (selectedOption) {
      const selectedsubject = selectedOption.subject;
      const selectedclientId = selectedOption.clientId;
      const selectedkeyName = selectedOption.keyName;
      const selectedclientType = selectedOption.clientType;
      //客户端是消费者，消费者IP是可输入的，否则disable
      if (selectedclientType == "消费者") {
        setConsumeIpStatus(false);
        setConsumeIpDisplayStatus("");
      }
      else {
        setConsumeIpStatus(true);
        setConsumeIpDisplayStatus("none");
      }
      //更新各field状态
      form.setFieldsValue({ env: form.getFieldValue("env"), 
            userName: email, 
            keyName: selectedkeyName, 
            subject: selectedsubject,
            clientType: selectedclientType,
            clientId: selectedclientId });
      console.log("form.getFieldsValue--->", form.getFieldsValue());
    } else {
      console.log('No option found for value:', value);
    }
  };

  // //搜索
  // const [options, setOptions] = useState(dataSource);

  // // 处理搜索的回调函数
  // const handleSearch = (value:string) => {
  //   // 根据输入值进行过滤
  //   const filteredOptions = dataSource.filter(item =>
  //     item.label.toLowerCase().includes(value.toLowerCase()) ||
  //     item.value.toLowerCase().includes(value.toLowerCase())
  //   );
  //   // 更新状态以反映过滤后的选项
  //   setParentInfoOptions(filteredOptions);
  // };



  // TODO: Optimize dependency change
  useEffect(() => {
    dispatch((state) => {
      const query = PM.Url.parse(state.request.endpoint).query || [];
      // console.log('------query', state.request);
      if (
        JSON.stringify(query) !== JSON.stringify(params.map(({ key, value }) => ({ key, value })))
      ) {
        if (typeof query !== 'string') {
          // @ts-ignore
          state.request.params = query.map(({ id, key, value }, index) => ({
            key,
            value: value || '',
            active: true,
            id: id || String(Math.random()),
          }));
        }
      }
    });
  }, [endpoint]);


  useEffect(() => {
    dispatch((state) => {
      state.request.endpoint = new PM.Url({
        ...PM.Url.parse(endpoint),
        query: state.request.params,
      }).toString();
    });
  }, [params]);

  const handleCopyParameters = () => {
    copyToClipboard(JSON.stringify(params.map((i) => ({ key: i.key, value: i.value }))));
    message.success('copy success🎉');
  };



  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
      `}
    >
      <br></br>
      {/* <div>
        <Tooltip title='可从wmb云平台上对应【下载密钥tab】获取！'>
          <Button type='primary' style={{ marginTop: '8px' }}>
            如何获取密钥信息？
          </Button>
        </Tooltip>
      </div> */}

      <Form
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 80 }}
        layout='horizontal'
        form={form}
        // initialValues={TaskInfo}
        onFieldsChange={handleWmbConsumerChange}
        // disabled={componentDisabled}
        style={{ maxWidth: 1000, marginTop: 10 }}
        initialValues={{ ...form.getFieldsValue, colorId: 0 }}
      >
        <Form.Item
          label='环境'
          name='env'
          rules={[{ required: true, message: '必填项' }]}
        >
          <Select onChange={handleEnvSelect} id='env' style={{ width: '100%' }} >
            <option value='ONLINE' key='ONLINE'>
              ONLINE
            </option>
            <option value='OFFLINE' key='OFFLINE'>
              OFFLINE
            </option>
            {/* <option value='SANDBOX' key='SANDBOX'>
              SANDBOX
            </option>
            <option value='STABLE' key='STABLE'>
              STABLE
            </option>
            <option value='ALL' key='ALL'>
              ALL
            </option> */}
          </Select>
        </Form.Item>
        {/* defaultValue={['ONLINE']} */}

        <Form.Item label='密钥名' name='keyName' rules={[{ required: true, message: '必填项' }]}>
          <AutoComplete
            placeholder='密钥名'
            style={{ width: '100%' }}
            allowClear
            onSelect={handleWmbKeySelect}
          >
            {renderWmbKeysOptions(wmbKeysInfoOptions)}
          </AutoComplete>
        </Form.Item>


        <Form.Item 
          label='subjectId' 
          name='subject'
          rules={[{ required: true, message: '必填项' }]} 
          style={{ display: 'none' }}>
          <AutoComplete
            placeholder='subjectId'
            style={{ width: '100%' }}
            allowClear
            onSelect={handleSubjectIdSelect}
            onSearch={(value) => {
              console.log('搜索过滤', result, value);
              if (!value) {
                setParentOptions(Object.keys(result ? result : []));
              } else {
                const filtered = Object.keys(result ? result : []).filter((key) =>
                  key.includes(value),
                );
                setParentOptions(filtered);
              }
            }}
          //options={parentOptions.map((key) => ({ value: key }))}
          >
            {renderSubjectsOptions(parentInfoOptions)}
          </AutoComplete>
        </Form.Item>


        <Form.Item label='clientId' name='clientId' rules={[{ required: true, message: '必填项' }]} style={{ display: 'none' }}>
          <AutoComplete
            placeholder='clientId'
            style={{ width: '100%' }}
            onSelect={handleConsumeIpStatus}
            disabled={!selectedParent}
            onSearch={(value) => {
              if (selectedParent) {
                const finalResult = result ? result : {};
                const children = finalResult[selectedParent] || [];
                if (!value) {
                  setChildOptions(children);
                } else {
                  console.log('children', children);
                  const finalChildren = children ? children : [];
                  const filtered = finalChildren.filter((item: string) => {
                    return item.includes(value);
                  });
                  setChildOptions(filtered);
                }
              }
            }}
            allowClear
          >
            {renderClientOptions(clientInfoOptions)}
          </AutoComplete>
        </Form.Item>



        <Form.Item label='客户端Client' name='wmbClientInfo' rules={[{ required: true, message: '必填项' }]}>
          <AutoComplete
            placeholder='clientId'
            style={{ width: '100%' }}
            onSelect={handleConsumeIpAndWmbKeyInfoField}
            disabled={!selectedParent}
            allowClear
          >
            {renderWmbClientByKeyOptions(wmbClientByKeyInfoOptions)}
          </AutoComplete>
        </Form.Item>
        {/* <Form.Item
          label='密钥名'
          name='keyName'
          style={{ display: 'none' }}
        >
          <Input placeholder='密钥名' style={{ width: '100%' }} />
        </Form.Item> */}


        <Form.Item
          label='客户端类型'
          name='clientType'
          style={{ display: 'none' }}
        >
          <Input placeholder='客户端类型' style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          label='userName'
          name='userName'
          style={{ display: 'none' }}
        >
          <Input placeholder='userName' style={{ width: '100%' }} />
        </Form.Item>


        <Form.Item
          label='染色属性'
          name='colorId'
          rules={[{ required: true, message: '必填项' }]}
        >
          <Select id='colorid' style={{ width: '100%' }} defaultValue={['0']}>
            <option value={0} key='NORMAL'>
              正常
            </option>
            <option value={1} key='RED'>
              红色
            </option>
            <option value={2} key='YELLOW'>
              黄色
            </option>
            <option value={3} key='BLUE'>
              蓝色
            </option>
            <option value={4} key='GREEN'>
              绿色
            </option>
          </Select>
        </Form.Item>

        <Form.Item label='消息tag' name='tags'>
          <Input placeholder='可为空，多个消息tag以英文逗号分隔' style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item label='消费方IP' name='consumeIp' style={{ display: consumeIpDisplayStatus }}>
          <Input
            placeholder='ClientId类型为消费者时生效，指定IP消费，IP不存在则随机'
            style={{ width: '100%' }}
            disabled={consumeIpStatus}
          />
        </Form.Item>

        <Form.Item
          label='消息体'
          name='msgBody'
          rules={[{ required: true, message: '必填项' }]}
        >
          <TextArea placeholder='请输入待发送消息内容' style={{ width: '100%' }} rows={8} />
        </Form.Item>
      </Form>
    </div>
  );
};

export default RequestParameters;
