import { SendOutlined } from '@ant-design/icons';
import { css, getLocalStorage, Label, RequestMethod, styled } from '@arextest/arex-core';
import { Button, Checkbox, Form, Input, Select } from 'antd';
import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { addUserAction, sendRequest, sendWmbConsumerRequest, UserActionEnum } from '../../../helpers';
import { useArexRequestProps, useArexRequestStore } from '../../../hooks';
import { get, post } from '../../../service/scfInfo';
import { ArexEnvironment, ArexRESTRequest, ArexRESTResponse } from '../../../types';
import { EnvironmentSelectProps } from '../../NavigationBar/EnvironmentSelect';
import { InfoSummaryProps } from '../../NavigationBar/InfoSummary';
import EnvInput from './EnvInput';
import HttpRequestOptions from './RequestOptions';

const HeaderWrapper = styled.div`
  padding: 0 8px;
  display: flex;
  .ant-select-selector {
    border-radius: 6px 0 0 6px;
  }
`;

export type RequestProps = {
  disableSave?: boolean;
  onBeforeRequest?: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest;
  onRequest?: (
    reqData: { request: ArexRESTRequest; environment?: ArexEnvironment },
    resData: Awaited<ReturnType<typeof sendRequest>>,
  ) => void;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
  onSaveAs?: () => void;
} & InfoSummaryProps & {
    environmentProps?: EnvironmentSelectProps;
  };

export interface IFormData {
  subject?: number;
  key?: string;
  registry_server_ip?: string;
  registry_server_port?: number;
}

const getAccessToken = () => {
  const accessToken: string = getLocalStorage('accessToken') || '';
  return accessToken;
};

const Request: FC<RequestProps> = () => {
  const { onBeforeRequest = (request: ArexRESTRequest) => request, onRequest } =
    useArexRequestProps();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();

  const handleRequest = async () => {
    void addUserAction(UserActionEnum.SEND_WMB_CONSUMER);
    dispatch((state) => {
      state.response = {
        type: 'loading',
        headers: undefined,
      };
    });

    const res = await sendWmbConsumerRequest(onBeforeRequest(store.request), store.environment);
    onRequest?.({ request: store.request, environment: store.environment }, res);
    console.log('state.request.body.contentType', store.request.body.contentType);
    console.log('body', store.request.body.body);

    // if (window.__AREX_EXTENSION_INSTALLED__) {
    //   console.log('已安装插件');
    //   const res = await sendWmbRequest(onBeforeRequest(store.request), store.environment);

    //   console.log('Call Wmb Request Info', store.request);

    //   onRequest?.({ request: store.request, environment: store.environment }, res);
    //   dispatch((state) => {
    //     state.response = res.response;
    //     state.consoles = res.consoles;
    //     state.visualizer = res.visualizer;
    //     state.testResult = res.testResult;
    //   });
    //   return;
    // }

    // console.log('未安装插件');
    // let res = {
    //   response: {
    //     type: 'success',
    //     statusCode: 200,
    //     body: '',
    //     meta: {
    //       responseSize: 0,
    //       responseDuration: 10,
    //     },
    //     headers: [],
    //   },
    //   testResult: [],
    //   consoles: [],
    //   visualizer: null,
    // };

    // const start = Date.now();
    // const response = await post(
    //   '/report/wmb/sendMsg',
    //   {
    //     msg: store.request?.body?.body,
    //     ...(store.request?.body?.wmb || {}),
    //   },
    //   getAccessToken(),
    // );
    // console.log('Call Wmb origin response Info', response);
    // const end = Date.now();
    // const resStr = JSON.stringify(response.data);
    // const httpCode = response.status;
    // res.response.meta.responseDuration = end - start;
    // res.response.body = resStr;
    // res.response.statusCode = httpCode;
    // res.response.meta.responseSize = resStr.length;

    dispatch((state) => {
      state.response = res.response;
      state.consoles = res.consoles;
      state.visualizer = res.visualizer;
      state.testResult = res.testResult;
    });
  };

  return (
    <div style={{ height: '100%' }}>
      <HeaderWrapper style={{ display: 'block' }}>
        <div style={{ float: 'right' }}>
          <Button
            id='arex-request-send-btn'
            type='primary'
            loading={store.response?.type === 'loading'}
            icon={<SendOutlined />}
            onClick={handleRequest}
          >
            {t('action.send')}
          </Button>
        </div>

        {store.request.inherited && (
          <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
            <Label type='secondary'>{t('request.inherit')}</Label>
            <Checkbox
              checked={store.request.inherited}
              onChange={(val) => {
                dispatch((state) => {
                  state.request.inherited = val.target.checked;
                });
              }}
            />
          </div>
        )}
      </HeaderWrapper>

      <HttpRequestOptions />
    </div>
  );
};

export default Request;
