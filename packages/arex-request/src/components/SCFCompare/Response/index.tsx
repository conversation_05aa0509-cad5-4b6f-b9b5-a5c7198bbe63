import Icon, { StopOutlined } from '@ant-design/icons';
import { EmptyWrapper, FlexCenterWrapper } from '@arextest/arex-core';
import { Button, message, Spin, Typography } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

const Response = (props: { caseItemId: string; diff?: any }) => {
  const { t } = useTranslation();
  // 修改状态类型为对象
  const [apiData, setApiData] = useState<{
    entrance?: any;
    infoItemList: any[];
  }>({ infoItemList: [] });

  // 添加一个强制刷新的状态
  const [forceRefresh, setForceRefresh] = useState(0);
  // 添加一个引用来存储上一次的 caseItemId
  const prevCaseItemIdRef = useRef<string>('');
  // 添加加载状态
  const [loading, setLoading] = useState(false);
  // 添加一个状态来控制组件的显示/隐藏
  const [showComponent, setShowComponent] = useState(true);

  // 新增接口调用逻辑
  useEffect(() => {
    // 如果 caseItemId 变化了，先隐藏组件，然后再显示
    if (props.caseItemId && props.caseItemId !== prevCaseItemIdRef.current) {
      prevCaseItemIdRef.current = props.caseItemId;
      setLoading(true);
      setShowComponent(false);

      // 延迟显示组件，确保DOM完全重建
      setTimeout(() => {
        setShowComponent(true);
        setForceRefresh((prev) => prev + 1);
        setLoading(false);
      }, 300);
    }

    const fetchData = async () => {
      try {
        const caseItemId = props.caseItemId; // 应从store/props获取实际值

        if (!caseItemId) {
          return;
        }

        setLoading(true);

        const response = await fetch(`/webApi/report/caseQueryFullLinkInfo/${caseItemId}`, {
          // eslint-disable-next-line prettier/prettier
          method: 'GET',
          headers: {
            'Access-token': localStorage?.getItem('accessToken')?.slice(1, -1),
          } as any,
        });
        const data = await response.json();

        // 直接使用新数据转换，避免异步状态问题
        const transformedData = [
          ...(data.body.entrance ? [{ ...data.body.entrance, isEntry: true }] : []),
          ...data.body.infoItemList,
        ].filter(Boolean);

        setApiData({
          entrance: data.body.entrance,
          infoItemList: data.body.infoItemList,
        });

        const diffCount = transformedData.filter((item) => item?.code == 1).length;
        if (diffCount == 0) {
          message.success('数据一致，比对成功！');
        } else {
          message.error('比对失败，请检查差异项！');
        }

        setLoading(false);
      } catch (error) {
        console.error('API请求失败:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, [props.caseItemId]);

  // 准备传递给 diff 函数的数据
  const diffData = useMemo(() => {
    const result = [
      ...(apiData.entrance ? [{ ...apiData.entrance, isEntry: true }] : []),
      ...apiData.infoItemList,
    ];
    return result;
  }, [apiData]);

  // 如果正在加载，显示加载中
  if (loading) {
    return (
      <FlexCenterWrapper style={{ height: '100%' }}>
        <Spin tip='加载中...' />
      </FlexCenterWrapper>
    );
  }

  // 如果没有数据，显示空状态
  if (diffData.length === 0 && props.caseItemId) {
    return <FlexCenterWrapper>暂无数据</FlexCenterWrapper>;
  }

  // 如果组件被隐藏，返回空
  if (!showComponent) {
    return <FlexCenterWrapper>正在准备数据...</FlexCenterWrapper>;
  }

  return (
    props?.diff &&
    props?.diff({
      appId: 'planItemData.appId',
      operationId: 'planItemData.operationId',
      data: diffData,
      // 使用 forceRefresh 和随机数作为 key，确保每次都是全新的组件
      key: `${props.caseItemId || ''}-${forceRefresh}-${Math.random()}`,
      // 添加容器样式确保滚动正常
      containerStyle: { height: '100%', overflow: 'auto' },
      // 默认只显示失败项设为 false，确保所有项都显示
      defaultOnlyFailed: false,
    })
  );
};

export default Response;
