import { ExclamationOutlined, HistoryOutlined, SendOutlined, TruckOutlined } from '@ant-design/icons';
import { css, getLocalStorage, Label, styled } from '@arextest/arex-core';
import {
  AutoComplete,
  Button,
  Checkbox,
  Divider,
  Drawer,
  FloatButton,
  Input,
  Select,
  Tabs,
  TabsProps,
  Tour,
  TourProps,
} from 'antd';
import jsonBigInt from 'json-bigint';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { addUserAction, sendSCFRequest, UserActionEnum } from '../../../helpers';
import { useArexRequestProps, useArexRequestStore } from '../../../hooks';
import { get, post } from '../../../service/scfInfo';
import { ArexEnvironment, ArexRESTRequest, ArexRESTResponse } from '../../../types';
import { IpOptions } from '../../../types/SCFRequest';
import { EnvironmentSelectProps } from '../../NavigationBar/EnvironmentSelect';
import { InfoSummaryProps } from '../../NavigationBar/InfoSummary';
import InterfaceDetail, {
  ChildComponentProps,
} from '../../Request/InterfaceDetail/InterfaceDetail';
import InterfaceHistoryList, {
  ChildComponentPropsHis,
} from '../../Request/InterfaceHistory/InterfaceHistoryList';
import SCFRequestOptions from './RequestOptions';

declare global {
  interface Window {
    __AREX_EXTENSION_INSTALLED__: boolean;
  }
}

const HeaderWrapper = styled.div`
  padding: 0 8px;
  display: flex;
  // .ant-select-selector {
  //   border-radius: 6px 0 0 6px;
  // }
`;

type AutoCompLabel = {
  key: string;
  value: string;
  clusterName?: string;
  functionName?: string;
};

export type RequestProps = {
  disableSave?: boolean;
  onBeforeRequest?: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest;
  onRequest?: (
    reqData: { request: ArexRESTRequest; environment?: ArexEnvironment },
    resData: Awaited<ReturnType<typeof sendSCFRequest>>,
  ) => void;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
  onSaveAs?: () => void;
  navPane?: any;
} & InfoSummaryProps & {
  environmentProps?: EnvironmentSelectProps;
};

const getUserEmail = () => {
  const email: string = getLocalStorage('email') || '';
  return email.toLowerCase().startsWith('guest') ? 'qinsisheng' : email;
};

type EnvCode = '0' | '1' | '2' | '3' | '4';
type EnvValueCode = 'test' | 'stable' | 'sandbox' | 'online' | 'others';
const EnvMapping = {
  '0': 'test',
  '1': 'stable',
  '2': 'sandbox',
  '3': 'online',
  '4': 'others',
};

const useElementBySelector = (selector: string) => {
  const [element, setElement] = useState<HTMLElement | null>(null);

  useEffect(() => {
    const node = document.querySelector(selector);
    if (node) {
      setElement(node as HTMLElement);
    }

    // 返回一个清理函数，当组件卸载时移除事件监听器（这里为空）
    return () => { };
  }, [selector]);

  return element;
};

const isFirstVisit = () => {
  const visitedKey = 'hasRequestTour';
  let isFirst = true;

  if (typeof Storage !== 'undefined') {
    // 检查localStorage是否存在
    if (localStorage.getItem(visitedKey)) {
      // 已有记录，不是首次访问
      isFirst = false;
    } else {
      // 设置首次访问标志
      localStorage.setItem(visitedKey, 'true');
    }
  }
  return isFirst;
};
// 扩展 RequestProps 接口，添加 setCaseItemId 属性
interface SCFCompareRequestProps extends RequestProps {
  setCaseItemId: (id: string) => void;
  navPane?: any;
}

const Request: FC<SCFCompareRequestProps> = (props: any) => {
  const { onBeforeRequest = (request: ArexRESTRequest) => request, onRequest } =
    useArexRequestProps();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();
  const { setCaseItemId, navPane } = props;

  // 移除同步请求状态控制

  // 调用接口详情传参
  const [interfaceDetailParam, setInterfaceDetailParam] = useState<ChildComponentProps>({
    scfID: '',
  });
  // 获取当前URL的查询参数
  const [searchParams] = useSearchParams();
  const scfParam = searchParams.get('scf');
  const isScf = scfParam !== null && scfParam === 'true';
  const [openDetail, updateOpenDetail] = useState<boolean>(false);
  console.log('2222222', isScf);

  // SCF请求相关
  // 接口获取的所有servicename
  const [serviceNameOptions, setServiceNameOptions] = useState<AutoCompLabel[]>([]);
  // serviceName筛选过滤option
  const [serviceFilterOption, setServiceFilterOption] = useState<AutoCompLabel[]>([]);
  // 选中的serviceName
  const [serviceNameValue, setServiceNameValue] = useState<string>();
  const [targetServiceNameValue, setTargetServiceNameValue] = useState<string>();

  // 选中的serviceId
  const [serviceID, setServiceID] = useState<string>();
  // 在 state 定义部分新增
  const [targetServiceID, setTargetServiceID] = useState<string>();
  // 是否选中的serviceName焦点
  const [selectServiceName, isSelectServiceName] = useState(false);

  // 接口获取的所有servicename
  const [interfaceNameOptions, setInterfaceNameOptions] = useState<AutoCompLabel[]>([]);
  // 选中的接口名
  const [interfaceNameValue, setInterfaceNameValue] = useState<string>();
  const [targetInterfaceNameValue, setTargetInterfaceNameValue] = useState<string>();

  // 是否选中的接口名焦点
  const [selectInterfaceName, isSelectInterfaceName] = useState(false);

  // 选中的方法名
  const [functionNameValue, setFunctionNameValue] = useState<string>();
  const [targetFunctionNameValue, setTargetFunctionNameValue] = useState<string>();

  // 筛选项
  const [newFunctionOptions, setNewFunctionOptions] = useState([]);
  const [targetNewFunctionOptions, setTargetNewFunctionOptions] = useState([]);

  // 原始筛选项
  const [originFunctionOptions, setOriginFunctionOptions] = useState([]);
  const [originTargetFunctionOptions, setOriginTargetFunctionOptions] = useState<any[]>([]);

  // 集群名称
  const [clusterName, setClusterName] = useState<string | undefined>('');

  const operatorUser = getUserEmail();

  const [originIpData, setOriginIpData] = useState<IpOptions>();
  const [ipOptions, setIpOptions] = useState<any[]>();
  const [targetIpOptions, setTargetIpOptions] = useState<any[]>();

  const [originIpOptions, setoriginIpOptions] = useState<any[]>();
  const [originTargetIpOptions, setOriginTargetIpOptions] = useState<any[]>([]);

  const [targetInterfaceOptions, setTargetInterfaceOptions] = useState<any[]>([]);
  const [targetFunctionOptions, setTargetFunctionOptions] = useState<any[]>([]);
  const [targetOriginIpData, setTargetOriginIpData] = useState<any>();
  const hisProps = {
    scfId: `0`,
    navPane: props.navPane,
  };

  // 漫游式引导
  const [open, setOpen] = useState(false);

  const [InterfaceHistoryListParam, setInterfaceHistoryListParam] =
    useState<ChildComponentPropsHis>({ hisProps });

  useEffect(() => {
    const getAllServiceName = async () => {
      try {
        const fetchedData = await get('/iapi/iapi/scence/getAllServiceInfo');
        const serviceOpt = fetchedData.data;
        setServiceNameOptions(serviceOpt);
      } catch (error) {
        console.error(error);
      }
    };

    getAllServiceName();

    const getScfDetailByScfId = async (scfId: string) => {
      try {
        return await get(
          `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}&operatorUser=${operatorUser}`,
        );
      } catch (error) {
        console.error(error);
      }
    };

    const getBranchList = async (scfId: string) => {
      try {
        return await get(`/iapi/iapi/scence/getBranchList?scfId=${scfId}`);
      } catch (error) {
        console.error(error);
      }
    };

    const handleScfId = async (scfId: string) => {
      //setInterfaceHistoryListParam({ scfId });
      setInterfaceHistoryListParam((prevState) => ({
        hisProps: {
          ...prevState.hisProps,
          scfId: scfId,
        },
      }));
      // console.log("hisprop scfId",hisProps.scfId)
      setInterfaceDetailParam({ scfID: scfId });
      console.log('isscf', isScf);
      isScf && updateOpenDetail(true);
      const scfDetail = await getScfDetailByScfId(scfId);

      const { implClass, interfaceClass, serviceName } = scfDetail.data;

      const resp = await getBranchList(scfId);
      const branchList = resp.data;

      const scfRequest = {
        implClassName: implClass,
        interfaceName: interfaceClass,
        scfServiceName: serviceName,
        branchList,
        operatorUser,
      };

      dispatch((state) => {
        state.request.body.scfRequest = { ...state.request.body.scfRequest, ...scfRequest };
      });
    };

    const handleService = async (
      scfServiceId: string,
      scfServiceName: string,
      clusterName: string,
    ) => {
      const option: AutoCompLabel = {
        value: scfServiceName,
        key: scfServiceId,
        clusterName,
      };
      handleServiceNameSelect(scfServiceId, option);
    };

    // 如果是通过 树状结构 | history | url 进来的
    if (store.request?.body.scf) {
      const scfContext = jsonBigInt.parse(store.request?.body.scf as string);

      // 如果有response，一般是 history 进来的
      if (scfContext.response) {
        const originRes = scfContext.response;
        const originResBody = scfContext.response.responseBody;
        const resObj = jsonBigInt.parse(originResBody);
        const isSuccess = scfContext.response.status;
        const body = resObj?.result;
        const cost = resObj?.cost || originRes?.costTime;

        dispatch((state) => {
          const response: ArexRESTResponse = {
            type: 'success',
            headers: [],
            body: '',
            statusCode: 200,
            meta: {
              responseSize: 0, // in bytes
              responseDuration: 0, // in millis
            },
          };

          if (cost) {
            response.meta.responseDuration = cost;
          }
          response.body = jsonBigInt.stringify(body);
          if (response.body) {
            response.meta.responseSize = response.body.length;
          }
          if (!body) {
            if (isSuccess != 1) {
              response.statusCode = 500;
            }

            response.body = originResBody;
            response.meta.responseSize = originResBody?.length;
          }
          state.response = response;
        });
      }

      dispatch((state) => {
        state.request.body.scfRequest = scfContext;
      });

      const { scfServiceName, interfaceName, methodName, scfId, scfServiceId, clusterName } =
        scfContext;

      setServiceNameValue(scfServiceName);
      setInterfaceNameValue(interfaceName);
      setFunctionNameValue(methodName);
      isSelectInterfaceName(true);
      isSelectServiceName(true);

      if (scfId) handleScfId(scfId);

      handleService(scfServiceId, scfServiceName, clusterName);
      return;
    }

    // 加载SCF接口信息数据
    if (store.request) {
      setServiceNameValue(store.request.serviceNameValue);
      setInterfaceNameValue(store.request.interfaceNameValue);
      setFunctionNameValue(store.request.functionNameValue);
    }

    // 漫游式引导
    const s = isFirstVisit();
    setOpen(s);
  }, []);

  console.log('functionNameValue0000000000', functionNameValue);
  useEffect(() => {
    dispatch((state) => {
      if (!state.request.body.scfRequest) {
        try {
          const scfInfo = jsonBigInt.parse(store.request.body.scf as string);
          state.request.body.scfRequest = scfInfo;
        } catch (e) {
          state.request.body.scfRequest = {};
        }
      }
      state.request.body.scfRequest.scfServiceName = serviceNameValue;
      state.request.body.scfRequest.interfaceName = interfaceNameValue;
      state.request.body.scfRequest.methodName = functionNameValue;
    });
  }, [serviceNameValue, interfaceNameValue, functionNameValue]);

  useEffect(() => {
    if (!originIpData) {
      return;
    }

    const scfContext = store.request.body.scfRequest;

    const env = scfContext.envID as EnvCode;
    const envVal = EnvMapping[env] as EnvValueCode;
    const ipInfos = originIpData[envVal];

    if (Array.isArray(ipInfos)) {
      const ipOpts = ipInfos.map((ip) => ({ value: ip }));
      setIpOptions(ipOpts);
      setoriginIpOptions(ipOpts);
      if (!scfContext.ip) {
        dispatch((state) => {
          state.request.body.scfRequest.ip = ipOpts[0]?.value;
        });
      }
      return;
    }

    const allIPs: { value: string }[] = [];
    if (ipInfos) {
      for (const [key, value] of Object.entries(ipInfos)) {
        // 遍历每个IP地址数组
        for (const ip of value) {
          // 将IP地址以对象形式添加到allIPs数组中
          allIPs.push({ value: ip });
        }
      }

      const transformedData = Object.keys(ipInfos).map((key) => ({
        label: <span>{key}</span>,
        title: key,
        options: ipInfos[key].map((ip) => ({
          label: <span>{ip}</span>,
          value: ip,
        })),
      }));

      setIpOptions(transformedData);
      setoriginIpOptions(transformedData);
      // IP为空默认选第一个
      const currentIP = scfContext.ip;
      if (!currentIP) {
        dispatch((state) => {
          state.request.body.scfRequest.ip = allIPs[0]?.value;
        });
      }
      // 当前IP不在IP列表中,改为选第一个
      const isIpInList = allIPs.some((item) => {
        return item.value === currentIP;
      });
      if (!isIpInList) {
        dispatch((state) => {
          state.request.body.scfRequest.ip = allIPs[0]?.value;
        });
      }
    }
  }, [originIpData]);

  useEffect(() => {
    if (store.request.body?.scfRequest?.connectType == '2') {
      dispatch((state) => {
        if (!state.request.body?.scfRequest?.isOnlineScfKey) {
          state.request.body.scfRequest.isOnlineScfKey = '1';
        }
      });
    }
  }, [store.request.body?.scfRequest?.connectType]);

  // serviceName start
  const handleSearchServiceName = (value: string) => {
    const filtered = serviceNameOptions.filter((option) =>
      option?.value?.toLocaleLowerCase().includes(value.toLocaleLowerCase()),
    );
    setServiceFilterOption(filtered);
  };

  const handleServiceNameSelect = async (
    value: string,
    option: AutoCompLabel,
    isTarget = false,
  ) => {
    if (isTarget) {
      setTargetServiceID(option.key); // 新增此行
      setTargetRequest((prev) => ({
        ...prev,
        serviceNameValue: option.value,
        interfaceNameValue: '',
        functionNameValue: '',
        serviceID: option.key,
        clusterName: option?.clusterName,
      }));
      // 单独获取target的接口数据
      const interfaceData = await get(
        `/iapi/iapi/scence/getInterfaceClassByServiceName?serviceId=${option.key}`,
      );
      const iOpt = interfaceData.data.map((v: any) => ({ value: v.value }));
      setTargetInterfaceOptions(iOpt); // 新增target专用状态

      // 单独获取target的方法列表
      const functionData = await get(
        `/iapi/iapi/scence/openapi/getFunctions?serviceId=${option.key}`,
      );
      const data = getFunctionOptions(functionData.data);
      setTargetFunctionOptions(data); // 新增target专用状态
      setOriginTargetFunctionOptions(data); // 新增此行 ← 这是关键更新点
      setTargetServiceNameValue(option.key); // 新增此行
      setClusterName(option?.clusterName);
      isSelectServiceName(true);
      // 处理IP数据
      const cName = option.clusterName ? option.clusterName : clusterName;
      if (!cName) return;
      const ipResp = await get(`/iapi/iapi/scence/getEnvInfosByClusterName?clusterName=${cName}`);
      setTargetOriginIpData(ipResp.data);
      // 转换IP数据格式
      const transformedData = Object.keys(ipResp.data).map((key) => ({
        label: <span>{key}</span>,
        title: key,
        options: ipResp.data[key].map((ip: any) => ({
          label: <span>{ip}</span>,
          value: ip,
        })),
      }));
      // 更新target专用IP状态
      setTargetIpOptions(transformedData);
      setOriginTargetIpOptions(transformedData);
      setTargetRequest((prev) => ({
        ...prev,
        body: {
          ...prev.body,
          scfRequest: {
            ...prev.body?.scfRequest,
            ipOptions: ipResp.data,
          },
        },
      }));
    } else {
      if (!option.key) return;

      const interfaceData = await get(
        `/iapi/iapi/scence/getInterfaceClassByServiceName?serviceId=${option.key}`,
      );

      if (!Array.isArray(interfaceData.data)) return;

      // 获取接口列表
      const iOpt = interfaceData.data.map((v: any) => ({ value: v.value }));
      setInterfaceNameOptions(iOpt);

      // 获取方法列表
      const functionData = await get(
        `/iapi/iapi/scence/openapi/getFunctions?serviceId=${option.key}`,
      );
      const data = getFunctionOptions(functionData.data) as any;
      setNewFunctionOptions(data);
      setOriginFunctionOptions(data);
      setServiceNameValue(option.value);
      setServiceID(option.key);
      setClusterName(option?.clusterName);
      isSelectServiceName(true);
      // 获取ip列表
      const cName = option.clusterName ? option.clusterName : clusterName;
      if (!cName) return;
      const ipResp = await get(`/iapi/iapi/scence/getEnvInfosByClusterName?clusterName=${cName}`);
      console.log('---->>>>>>ipResp = ', ipResp)
      setOriginIpData(ipResp.data);
      dispatch((state) => {
        state.request.body.scfRequest.ipOptions = ipResp.data as IpOptions;
        if (!state.request.body.scfRequest.envID) {
          state.request.body.scfRequest.envID = '3';
        }
      });
    }
  };
  // serviceName end

  // interfaceName
  const handleInterfaceNameSelect = async (value: string, isTarget = false) => {
    if (isTarget) {
      setTargetRequest((prev) => ({
        ...prev,
        interfaceNameValue: value,
      }));

      // 获取Target专用的方法列表
      const functionData = await get(
        `/iapi/iapi/scence/openapi/getFunctions?serviceId=${targetServiceID}&interfcClsName=${value}`,
      );
      const data = getFunctionOptions(functionData.data) as any;
      setTargetFunctionOptions(data);
      setOriginTargetFunctionOptions(data);

      if (value !== targetRequest.interfaceNameValue) {
        setTargetRequest((prev) => ({
          ...prev,
          functionNameValue: '',
        }));
      }
    } else {
      setInterfaceNameValue(value);
      isSelectInterfaceName(true);
      //获取方法列表
      const functionData = await get(
        `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceID}&interfcClsName=${value}`,
      );
      const data = getFunctionOptions(functionData.data) as any;
      setNewFunctionOptions(data);
      setOriginFunctionOptions(data);

      if (value !== interfaceNameValue) {
        setFunctionNameValue('');
      }
    }
  };

  // functionName
  const handleFunctionNameSelect = async (
    value: string,
    option: AutoCompLabel,
    isTarget = false,
  ) => {
    if (isTarget) {
      setTargetRequest((prev) => ({
        ...prev,
        functionNameValue: option.functionName,
      }));
      // 获取Target专用的SCF详情
      const scfId = option.key;
      const scfDetail = await get(
        `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}&operatorUser=${operatorUser}`,
      );
      const { implClass, interfaceClass, paramList, serviceName, servicePort, version } =
        scfDetail.data;
      const resp = await get(`/iapi/iapi/scence/getBranchList?scfId=${scfId}`);
      const branchList = resp.data;

      // 更新Target请求的SCF信息
      setTargetRequest((prev) => ({
        ...prev,
        body: {
          ...prev.body,
          scfRequest: {
            ...prev.body?.scfRequest,
            scfId,
            implClassName: implClass,
            interfaceName: interfaceClass,
            params: paramList,
            scfServiceName: serviceName,
            scfServiceId: targetServiceID,
            clusterName,
            port: servicePort,
            branchVersion: version,
            branchList,
            operatorUser,
            methodName: option.functionName, // 确保methodName也被设置
          },
        },
      }));
    } else {
      setFunctionNameValue(option.functionName);
      const scfId = option.key;
      const scfDetail = await get(
        `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}&operatorUser=${operatorUser}`,
      );
      const { implClass, interfaceClass, paramList, serviceName, servicePort, version } =
        scfDetail.data;
      const resp = await get(`/iapi/iapi/scence/getBranchList?scfId=${scfId}`);
      const branchList = resp.data;
      const scfRequest = {
        scfId,
        implClassName: implClass,
        serviceID: serviceID, // 添加必需的 serviceID 属性
        interfaceName: interfaceClass,
        params: paramList,
        scfServiceName: serviceName,
        scfServiceId: serviceID,
        clusterName,
        port: servicePort,
        branchVersion: version,
        branchList,
        connectType: '1',
        serializedVersion: '0',
        envID: '3',
        operatorUser,
        methodName: option.functionName, // 确保methodName也被设置
      };

      dispatch((state) => {
        state.request.functionNameValue = option.functionName;
        state.request.body.scfRequest = { ...state.request.body.scfRequest, ...scfRequest };
      });

      //setInterfaceHistoryListParam({ scfId });
      setInterfaceHistoryListParam((prevState) => ({
        hisProps: {
          ...prevState.hisProps,
          scfId: scfId,
        },
      }));
      setInterfaceDetailParam({ scfID: scfId });
    }
  };

  const handleFilterOption = (inputValue: string, option: any) => {
    return option?.value?.toLocaleLowerCase().includes(inputValue.toLocaleLowerCase());
  };

  // 新增 target 请求状态
  const [targetRequest, setTargetRequest] = useState<ArexRESTRequest>({
    ...JSON.parse(JSON.stringify(store.request)),
    serviceNameValue: undefined,
    interfaceNameValue: undefined,
    functionNameValue: undefined,
    headers: JSON.parse(JSON.stringify(store.request.headers)),
    body: JSON.parse(JSON.stringify(store.request.body)),
  });
  console.log('-------------targetRequest-------------', targetRequest);

  const handleRequest = async () => {
    void addUserAction(UserActionEnum.SEND_SCF);
    dispatch((state) => {
      state.response = {
        type: 'loading',
        headers: undefined,
      };
    });
    const requests = {
      original: store.request,
      target: targetRequest,
    };
    const requestTmp = { ...store.request };

    const targetRequestTmp = {
      ...requests.target,
      body: {
        ...targetRequest.body,
        scfRequest: {
          // ...targetRequest.body?.scfRequest,
          connectType: targetRequest.body?.scfRequest?.connectType || '1',
          interfaceName: targetRequest.interfaceNameValue,
          methodName: targetRequest.functionNameValue,
          scfServiceName: targetRequest.serviceNameValue,
          scfKey: targetRequest.body?.scfRequest?.scfKey,
          envID: targetRequest.body?.scfRequest?.envID,
          ip: targetRequest.body?.scfRequest?.ip,
          params: targetRequest.body?.scfRequest?.params,
          // params: store.request.body?.scfRequest?.params,
          scfServiceId: targetServiceID,
          serviceID: targetServiceID, // 添加缺少的 serviceID 属性
          scfId: targetRequest.body?.scfRequest?.scfId,
          implClassName: targetRequest.body?.scfRequest?.implClassName,
          port: targetRequest.body?.scfRequest?.port,
          branchVersion: targetRequest.body?.scfRequest?.branchVersion,
          serializedVersion: targetRequest.body?.scfRequest?.serializedVersion,
          // ipOptions: targetIpOptions,
          // branchVersion: version,
          clusterName: clusterName,
          operatorUser,
        },
      },
    };

    // origin的参数发送请求
    const res = (await sendSCFRequest(onBeforeRequest(requestTmp), store.environment)) as any;
    console.log('res', res);
    const resStr = res.response.body;
    const isSuccess = res.response.status;
    const resObj = jsonBigInt.parse(resStr);
    const body = resObj.result;
    const cost = resObj.cost;
    if (cost) {
      res.response.meta.responseDuration = cost;
    }
    res.response.body = jsonBigInt.stringify(body);
    if (res.response.body) {
      res.response.meta.responseSize = res.response.body.length;
    }
    if (!body && resObj.message) {
      // res.response.body = resObj.message;
      if (isSuccess != 1) {
        res.response.statusCode = 500;
      }
      // res.response.statusCode = '500';
      res.response.body = resStr;

      // res.response.body = jsonBigInt.stringify(resStr);
      if (res.response.body) {
        res.response.meta.responseSize = res.response.body.length;
      }
    }
    console.log(' Scfresponse', res);

    onRequest?.({ request: store.request, environment: store.environment }, res);
    dispatch((state) => {
      state.response = res.response;
      state.consoles = res.consoles;
      state.visualizer = res.visualizer;
      state.testResult = res.testResult;
    });

    // target的参数发送请求
    const res2 = (await sendSCFRequest(
      onBeforeRequest(targetRequestTmp),
      store.environment,
    )) as any;
    console.log('res2', res2);
    // 处理res2的响应，与res处理方式保持一致
    const resStr2 = res2.response.body;
    const isSuccess2 = res2.response.status;
    const resObj2 = jsonBigInt.parse(resStr2);
    const body2 = resObj2.result;
    const cost2 = resObj2.cost;
    if (cost2) {
      res2.response.meta.responseDuration = cost2;
    }
    res2.response.body = jsonBigInt.stringify(body2);
    if (res2.response.body) {
      res2.response.meta.responseSize = res2.response.body.length;
    }
    if (!body2 && resObj2.message) {
      if (isSuccess2 != 1) {
        res2.response.statusCode = 500;
      }
      res2.response.body = resStr2;
      if (res2.response.body) {
        res2.response.meta.responseSize = res2.response.body.length;
      }
    }
    console.log(' Scfresponse2', res2);
    // console.log(' store.request77777777', store.request);

    onRequest?.({ request: targetRequest, environment: store.environment }, res2);
    dispatch((state) => {
      console.log('consoles', res2.consoles);
      state.response = res2.response;
      state.consoles = res2.consoles;
      state.visualizer = res2.visualizer;
      state.testResult = res2.testResult;
    });
    // 新增上报接口调用
    try {
      const reportData = {
        base: {
          requestType: 'SCF',
          operationName: functionNameValue || store.request.body?.scfRequest?.methodName || '',
          response: {
            headers: res.response?.headers || [],
            body: JSON.parse(res.response?.body) || {},
          },
        },
        test: {
          requestType: 'SCF',
          operationName: targetRequest.functionNameValue,
          response: {
            headers: res2.response?.headers || [],
            body: JSON.parse(res2.response?.body) || {},
          },
        },
      };

      // const res2 = await fetch('http://************:8080/api/ipsExecResultCompare', {
      const resScf = await fetch('/schedule/ipsExecResultCompare', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });
      // 新增响应结构获取
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const responseData = await resScf.json();
      props?.setCaseItemId(responseData.data);
      console.log('responseData-ID', responseData.data);
    } catch (e) {
      console.error('上报请求失败:', e);
    }
  };

  // 方法渲染
  const renderItem = (value: string, key: string, parent: string) => ({
    key,
    value: key as string,
    parent,
    functionName: value,
    label: (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        {value}
      </div>
    ),
  });
  function getFunctionOptions(data: {
    [s: string]: Array<{ scf_function_name: string; scfID: string }>;
  }) {
    return Object.entries(data).map(([label, item]) => {
      return {
        label,
        options: item.map((value: { scf_function_name: string; scfID: string }) => {
          return renderItem(value.scf_function_name, value.scfID, label);
        }),
      };
    });
  }

  const handleSearch = (value: string) => {
    const filteredOptions = originFunctionOptions.flatMap((category: any) =>
      category.options.filter(
        (item: any) => item.functionName.toLowerCase().indexOf(value.toLowerCase()) !== -1,
      ),
    );

    const transformedData = filteredOptions.reduce((acc, curr) => {
      if (!acc[curr.parent]) {
        acc[curr.parent] = [];
      }

      acc[curr.parent].push({
        scfID: curr.key,
        scf_function_name: curr.functionName,
      });

      return acc;
    }, {});
    const newOptions = getFunctionOptions(transformedData) as any;
    setNewFunctionOptions(newOptions);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleTargetFunctionSearch = (value: string) => {
    const filtered = originTargetFunctionOptions.flatMap((category: any) =>
      category.options.filter((item: any) =>
        item.functionName.toLowerCase().includes(value.toLowerCase()),
      )
    );

    const transformedData = filtered.reduce((acc, curr) => {
      if (!acc[curr.parent]) {
        acc[curr.parent] = [];
      }
      acc[curr.parent].push({
        scfID: curr.key,
        scf_function_name: curr.functionName,
      });
      return acc;
    }, {});
    setTargetFunctionOptions(getFunctionOptions(transformedData));
  };

  const onEnvChange = (v: EnvCode) => {
    dispatch((state) => {
      state.request.body.scfRequest.ip = '';
      state.request.body.scfRequest.envID = v;
    });

    const envVal = EnvMapping[v] as EnvValueCode;
    if (!originIpData) return;

    const ipInfos = originIpData[envVal] as { [key: string]: string[] };

    let allIPs: { value: string }[] = [];
    if (Array.isArray(ipInfos) && ipInfos.length > 0) {
      allIPs = ipInfos.map((ip) => ({ value: ip, label: ip }));
      console.log('allIPs', allIPs);
      setIpOptions(allIPs);
      setoriginIpOptions(allIPs);
      setTargetIpOptions(allIPs);
      setOriginTargetIpOptions(allIPs);
      dispatch((state) => {
        // if (!state.request.body.scfRequest.envID) {
        //   state.request.body.scfRequest.envID = '3';
        // }
        state.request.body.scfRequest.ip = allIPs[0].value;
      });



      return;
    }

    for (const [_, value] of Object.entries(ipInfos)) {
      // 遍历每个IP地址数组
      for (const ip of value) {
        // 将IP地址以对象形式添加到allIPs数组中
        allIPs.push({ value: ip });
      }
    }

    const transformedData = Object.keys(ipInfos).map((key) => ({
      label: <span>{key}</span>,
      title: key,
      options: ipInfos[key].map((ip: any) => ({
        label: <span>{ip}</span>,
        value: ip,
      })),
    }));

    setIpOptions(transformedData);
    setoriginIpOptions(transformedData);
    console.log('transformedData', transformedData);

    if (allIPs.length > 0) {
      dispatch((state) => {
        state.request.body.scfRequest.ip = allIPs[0].value;
      });
    }
  };

  // 新增 Target 专属的环境变更处理函数
  const onTargetEnvChange = (v: EnvCode) => {
    setTargetRequest((prev) => ({
      ...prev,
      body: {
        ...prev.body,
        scfRequest: {
          ...prev.body?.scfRequest,
          ip: '',
          envID: v,
        },
      },
    }));



    const envVal = EnvMapping[v] as EnvValueCode;
    if (!targetOriginIpData) return;

    const ipInfos = targetOriginIpData[envVal];

    if (Array.isArray(ipInfos) && ipInfos.length > 0) {
      const ipOpts = ipInfos.map((ip) => ({ value: ip, label: ip }));
      setTargetIpOptions(ipOpts);
      setOriginTargetIpOptions(ipOpts);

      if (ipOpts.length > 0) {
        setTargetRequest((prev) => ({
          ...prev,
          body: {
            ...prev.body,
            scfRequest: {
              ...prev.body?.scfRequest,
              ip: ipOpts[0].value,
            },
          },
        }));
      }
      return;
    }

    if (ipInfos) {
      const allIPs: any[] = [];
      const transformedData = Object.keys(ipInfos).map((key) => ({
        label: <span>{key}</span>,
        title: key,
        options: ipInfos[key].map((ip: any) => {
          allIPs.push(ip);
          return {
            label: <span>{ip}</span>,
            value: ip,
          };
        }),
      }));

      setTargetIpOptions(transformedData);
      setOriginTargetIpOptions(transformedData);

      if (allIPs.length > 0) {
        setTargetRequest((prev) => ({
          ...prev,
          body: {
            ...prev.body,
            scfRequest: {
              ...prev.body?.scfRequest,
              ip: allIPs[0],
            },
          },
        }));
      }
    }
  };

  const onIpChange = (v: any) => {
    dispatch((state) => {
      state.request.body.scfRequest.ip = v;
    });


  };

  // 新增 Target 专属的 IP 变更处理函数
  const onTargetIpChange = (v: any) => {
    setTargetRequest((prev) => ({
      ...prev,
      body: {
        ...prev.body,
        scfRequest: {
          ...prev.body?.scfRequest,
          ip: v,
        },
      },
    }));

    // 如果启用了同步，也更新original的IP

  };

  const handleIpSearch = (value: string) => {
    const currentEnv = store.request.body.scfRequest.envID;
    // stable or online
    if (currentEnv === '3' || currentEnv === '1') {
      const filteredIpOption = originIpOptions?.map((item) => ({
        ...item,
        options: item.options.filter((option: { value: string | string[] }) =>
          option.value.includes(value),
        ),
      }));
      setIpOptions(filteredIpOption);
      return;
    }

    const filteredIpOption = originIpOptions?.filter((item) => item.value.includes(value));
    setIpOptions(filteredIpOption);
  };

  // 新增 Target 专属搜索处理
  const handleTargetIpSearch = (value: string) => {
    const currentEnv = targetRequest.body.scfRequest?.envID;
    if (currentEnv === '3' || currentEnv === '1') {
      const filtered = originTargetIpOptions?.map((item) => ({
        ...item,
        options: item.options.filter((option: { value: string }) => option.value.includes(value)),
      }));
      setTargetIpOptions(filtered);
    } else {
      const filtered = originTargetIpOptions?.filter((item) => item.value.includes(value));
      setTargetIpOptions(filtered);
    }
  };

  const onOnlineScfKeyChange = (v: any) => {
    dispatch((state) => {
      state.request.body.scfRequest.isOnlineScfKey = v;
    });
  };
  const items: TabsProps['items'] = [
    {
      key: 'original',
      label: 'Original',
      children: (
        <div style={{ height: '100%' }}>
          <HeaderWrapper>
            {/* serviceName */}
            <AutoComplete
              value={serviceNameValue}
              style={{ width: 400 }}
              options={serviceNameOptions}
              placeholder='服务名称'
              onSelect={(value, option) => handleServiceNameSelect(value, option, false)}
              onSearch={handleSearchServiceName}
              onChange={(value) => {
                setServiceNameValue(value);
                isSelectServiceName(false);

              }}
              filterOption={handleFilterOption}
              allowClear
              onBlur={() => {
                if (!selectServiceName) {
                  setServiceNameValue('');
                }
              }}
              onClear={() => {
                setInterfaceNameValue('');
                setFunctionNameValue('');
              }}
            />

            {/* 接口 */}
            <AutoComplete
              value={interfaceNameValue}
              popupMatchSelectWidth={false}
              style={{ width: 400 }}
              options={interfaceNameOptions}
              placeholder='接口类'
              onSelect={(value) => handleInterfaceNameSelect(value, false)}
              onSearch={handleSearch}
              onChange={(value) => {
                setInterfaceNameValue(value);
                isSelectInterfaceName(false);

              }}
              filterOption={handleFilterOption}
              allowClear
              onBlur={() => {
                if (!selectInterfaceName) {
                  setInterfaceNameValue('');
                }
              }}
              onClear={async () => {
                const functionData = await get(
                  `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceID}`,
                );
                const data = getFunctionOptions(functionData.data) as any;
                setNewFunctionOptions(data);
                setOriginFunctionOptions(data);
                setFunctionNameValue('');
              }}
            />

            {/* 方法 */}
            <AutoComplete
              value={functionNameValue}
              popupMatchSelectWidth={false}
              style={{ width: 1000 }}
              options={newFunctionOptions}
              placeholder='函数名称'
              onSelect={(value, option) => {
                handleFunctionNameSelect(value, option, false)
              }}
              // onSelect={handleFunctionNameSelect}
              onSearch={handleSearch}
              onChange={(value) => {
                setFunctionNameValue(value);

              }}
              allowClear
            ></AutoComplete>

            <Divider type='vertical' />

            <Select
              popupMatchSelectWidth={false}
              disabled={store?.request?.inherited}
              style={{ width: 180 }}
              value={
                store.request.body.scfRequest?.connectType
                  ? store.request.body.scfRequest?.connectType
                  : '1'
              }
              options={[
                { label: 'IP', value: '1' },
                { label: 'Key', value: '2' },
              ]}
              onChange={(value) => {
                dispatch((state) => {
                  state.request.body.scfRequest.connectType = value;
                });
              }}
            />

            {store.request.body.scfRequest?.connectType === '2' ? (
              <>
                {/* isOnlineScfKey */}
                <Select
                  style={{ width: 280 }}
                  options={[
                    { value: '1', label: '线上' },
                    { value: '2', label: '线下' },
                  ]}
                  value={store.request.body.scfRequest?.isOnlineScfKey}
                  onChange={onOnlineScfKeyChange}
                />

                {/* scfKey */}
                <Input
                  value={store.request.body.scfRequest?.scfKey}
                  style={{ width: 360 }}
                  onChange={(e) => {
                    dispatch((state) => {
                      state.request.body.scfRequest.scfKey = e.target.value;
                    });
                  }}
                />
              </>
            ) : (
              <>
                {/* env */}
                <Select
                  popupMatchSelectWidth={false}
                  // defaultValue={'Online'}
                  style={{ width: 280 }}
                  options={[
                    { value: '0' as EnvCode, label: 'Test' },
                    { value: '1' as EnvCode, label: 'Stable' },
                    { value: '2' as EnvCode, label: 'Sandbox' },
                    { value: '3' as EnvCode, label: 'Online' },
                    { value: '4' as EnvCode, label: '其他' },
                  ]}
                  value={store.request.body.scfRequest?.envID as EnvCode}
                  onChange={onEnvChange}
                />

                {/* IP */}
                <AutoComplete
                  value={store.request.body.scfRequest?.ip}
                  popupMatchSelectWidth={false}
                  style={{ width: 400 }}
                  options={ipOptions}
                  onChange={onIpChange}
                  onSelect={(value) => {
                    dispatch((state) => {
                      state.request.body.scfRequest.ip = value;
                    });
                  }}
                  onSearch={handleIpSearch}
                ></AutoComplete>
              </>
            )}

            {store.request.inherited && (
              <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
                <Label type='secondary'>{t('request.inherit')}</Label>
                <Checkbox
                  checked={store.request.inherited}
                  onChange={(val) => {
                    dispatch((state) => {
                      state.request.inherited = val.target.checked;
                    });
                  }}
                />
              </div>
            )}
          </HeaderWrapper>

          <SCFRequestOptions />

          <InterfaceDetail openModal={openDetail} {...interfaceDetailParam}></InterfaceDetail>
          <InterfaceHistoryList {...InterfaceHistoryListParam}></InterfaceHistoryList>
        </div>
      ),
    },
    {
      key: 'target',
      label: 'Target',
      children: (
        <div style={{ height: '100%' }}>
          <HeaderWrapper>
            {/* serviceName */}
            <AutoComplete
              value={targetRequest.serviceNameValue}
              onSelect={(value, option) => {
                // 处理 Target 专属的服务选择逻辑
                handleServiceNameSelect(value, option, true);
                setTargetRequest((prev) => ({ ...prev, serviceNameValue: value }));
              }}
              style={{ width: 400 }}
              options={serviceNameOptions}
              placeholder='服务名称'
              // onSelect={handleServiceNameSelect}
              onSearch={handleSearchServiceName}
              onChange={(value) => {
                setTargetRequest((prev) => ({ ...prev, serviceNameValue: value }));
                isSelectServiceName(false);
              }}
              filterOption={handleFilterOption}
              allowClear
              onBlur={() => {
                if (!selectServiceName) {
                  setTargetServiceNameValue('');
                }
              }}
              onClear={() => {
                setInterfaceNameValue('');
                setFunctionNameValue('');
              }}
            />

            {/* 接口 */}
            <AutoComplete
              value={targetRequest.interfaceNameValue}
              popupMatchSelectWidth={false}
              style={{ width: 400 }}
              options={targetInterfaceOptions}
              placeholder='接口类'
              onSelect={(value) => {
                handleInterfaceNameSelect(value, true);
                setTargetRequest((prev) => ({
                  ...prev,
                  interfaceNameValue: value,
                  functionNameValue: '',
                })); // 直接更新targetRequest
              }}
              onSearch={handleSearch}
              onChange={(value) => {
                setTargetRequest((prev) => ({ ...prev, interfaceNameValue: value }));
                isSelectInterfaceName(false);
              }}
              filterOption={handleFilterOption}
              allowClear
              onClear={async () => {
                const functionData = await get(
                  `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceID}`,
                );
                const data = getFunctionOptions(functionData.data) as any;
                setTargetFunctionOptions(data);
                setOriginTargetFunctionOptions(data);
                setTargetRequest((prev) => ({
                  ...prev,
                  functionNameValue: '',
                }));
              }}
            />

            {/* 方法 */}
            <AutoComplete
              value={targetRequest.functionNameValue}
              popupMatchSelectWidth={false}
              style={{ width: 1000 }}
              options={targetFunctionOptions}
              placeholder='函数名称'
              onSelect={(value, option) => {
                handleFunctionNameSelect(value, option, true);
              }}
              onSearch={handleTargetFunctionSearch}
              onChange={(value) => {
                setTargetRequest((prev) => ({ ...prev, functionNameValue: value }));
              }}
              allowClear
            ></AutoComplete>

            <Divider type='vertical' />

            <Select
              popupMatchSelectWidth={false}
              disabled={store?.request?.inherited}
              style={{ width: 180 }}
              value={
                targetRequest.body.scfRequest?.connectType
                  ? targetRequest.body.scfRequest?.connectType
                  : '1'
              }
              options={[
                { label: 'IP', value: '1' },
                { label: 'Key', value: '2' },
              ]}
              onChange={(value) => {
                setTargetRequest((prev) => ({
                  ...prev,
                  body: {
                    ...prev.body,
                    scfRequest: {
                      ...prev.body?.scfRequest,
                      connectType: value,
                    },
                  },
                }));
              }}
            />

            {store.request.body.scfRequest?.connectType === '2' ? (
              <>
                {/* isOnlineScfKey */}
                <Select
                  style={{ width: 280 }}
                  options={[
                    { value: '1', label: '线上' },
                    { value: '2', label: '线下' },
                  ]}
                  value={targetRequest.body.scfRequest?.isOnlineScfKey || '1'}
                  onChange={(value) => {
                    setTargetRequest((prev) => ({
                      ...prev,
                      body: {
                        ...prev.body,
                        scfRequest: {
                          ...prev.body?.scfRequest,
                          isOnlineScfKey: value,
                        },
                      },
                    }));
                  }}
                // onChange={onOnlineScfKeyChange}
                />

                {/* scfKey */}
                <Input
                  value={targetRequest.body.scfRequest?.scfKey}
                  style={{ width: 360 }}
                  onChange={(e) => {
                    setTargetRequest((prev) => ({
                      ...prev,
                      body: {
                        ...prev.body,
                        scfRequest: {
                          ...prev.body?.scfRequest,
                          scfKey: e.target.value,
                        },
                      },
                    }));
                  }}
                />
              </>
            ) : (
              <>
                {/* env */}
                <Select
                  popupMatchSelectWidth={false}
                  // defaultValue={'Online'}
                  style={{ width: 280 }}
                  options={[
                    { value: '0' as EnvCode, label: 'Test' },
                    { value: '1' as EnvCode, label: 'Stable' },
                    { value: '2' as EnvCode, label: 'Sandbox' },
                    { value: '3' as EnvCode, label: 'Online' },
                    { value: '4' as EnvCode, label: '其他' },
                  ]}
                  value={targetRequest.body.scfRequest?.envID as EnvCode}
                  onChange={onTargetEnvChange}
                />
                {/* IP */}
                <AutoComplete
                  value={targetRequest.body.scfRequest?.ip}
                  popupMatchSelectWidth={false}
                  style={{ width: 400 }}
                  options={targetIpOptions}
                  onChange={onTargetIpChange}
                  onSearch={handleTargetIpSearch}
                ></AutoComplete>
              </>
            )}

            {store.request.inherited && (
              <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
                <Label type='secondary'>{t('request.inherit')}</Label>
                <Checkbox
                  checked={store.request.inherited}
                  onChange={(val) => {
                    dispatch((state) => {
                      state.request.inherited = val.target.checked;
                    });
                  }}
                />
              </div>
            )}
          </HeaderWrapper>

          <SCFRequestOptions
            isTarget={true}
            targetRequest={targetRequest}
            setTargetRequest={setTargetRequest} />
          <InterfaceDetail openModal={openDetail} {...interfaceDetailParam}></InterfaceDetail>
          <InterfaceHistoryList {...InterfaceHistoryListParam}></InterfaceHistoryList>
        </div>
      ),
    }
  ]
  return (
    <Tabs
      defaultActiveKey='original'
      items={items}
      tabBarExtraContent={{
        right: (
          <>

            <Button
              id='arex-request-send-btn'
              type='primary'
              loading={store.response?.type === 'loading'}
              disabled={store.response?.type === 'extensionNotInstalled'}
              icon={
                store.response?.type === 'extensionNotInstalled' ? (
                  <ExclamationOutlined />
                ) : (
                  <SendOutlined />
                )
              }
              onClick={handleRequest}
              style={{ marginRight: 8 }}
            >
              {t('action.send')}
            </Button>
          </>
        ),
      }}
      tabBarStyle={{
        padding: '0px',
        margin: 10,
      }}
      style={{
        height: '100%',
      }}
    ></Tabs>
  );
};

export default Request;
