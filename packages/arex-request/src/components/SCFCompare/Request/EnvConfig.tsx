import { css, styled, Theme, useArexCoreConfig } from '@arextest/arex-core';
import { Button, Form, Input, Radio, RadioChangeEvent, Select, Typography } from 'antd';
import jsonBigInt from 'json-bigint';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { IpOptions } from '../../../types/SCFRequest';

const { Text } = Typography;

const ResponseTestHeader = styled.div`
  display: flex;
  justify-content: space-between;
  & > span:first-of-type {
    font-size: 13px;
    line-height: 32px;
    font-weight: 500;
    color: #9d9d9d;
  }
`;

const ResponseTestWrapper = styled.div`
  overflow-y: auto;
  display: flex;
  justify-content: space-between;
  flex: 1;
  & > div:last-of-type {
    width: 70%;
    text-align: left;
    padding-left: 20px;
  }
`;

type EnvCode = '0' | '1' | '2' | '3' | '4';
type EnvValueCode = 'test' | 'stable' | 'sandbox' | 'online' | 'others';
const EnvMapping = {
  '0': 'test',
  '1': 'stable',
  '2': 'sandbox',
  '3': 'online',
  '4': 'others',
};
// 添加接口定义，支持 target 相关属性
interface RequestObject {
  body: {
    scf?: string;
    scfRequest?: Record<string, any>;
    [key: string]: any;
  };
  [key: string]: any;
}
interface EnvConfigProps {
  isTarget?: boolean;
  targetRequest?: RequestObject;
  setTargetRequest?: (value: RequestObject) => void;
}

const EnvConfig: FC<EnvConfigProps> = ({ isTarget = false, targetRequest, setTargetRequest }) => {
  // const { theme } = useArexCoreConfig();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [connectType, setConnectType] = useState('1');
  const [branchOptions, setBranchOptions] = useState<any[]>();
  const [ipOptions, setIpOptions] = useState<any[]>();
  const [ipAll, setIpAll] = useState<IpOptions>();
  // 添加获取当前请求对象的辅助函数
  const getCurrentRequest = () => {
    return isTarget && targetRequest ? targetRequest : store.request;
  };
  useEffect(() => {
    // 特殊处理：当 target 模式下，确保 branchOptions 被正确设置
    if (isTarget && targetRequest?.body?.scfRequest?.branchList) {
      console.log(
        'Setting branch options from target request',
        targetRequest.body.scfRequest.branchList,
      );
      setBranchOptions(targetRequest.body.scfRequest.branchList);
      // 强制设置表单值，确保在 target 模式下也能正确填充
      // 强制设置表单值，确保在 target 模式下也能正确填充
      const { branchVersion, port, serializedVersion } = targetRequest.body.scfRequest;
      form.setFieldsValue({
        branchVersion,
        port,
        serializedVersion: serializedVersion || '0'
      });
      console.log('Forced setting form values in target mode:', {
        branchVersion,
        port,
        serializedVersion,
      });
    }
  }, [isTarget, targetRequest?.body?.scfRequest?.branchList]);

  useEffect(() => {
    const currentRequest = getCurrentRequest();
    if (currentRequest?.body.scf) {
      const scfContext = jsonBigInt.parse(currentRequest?.body.scf as string);
      // 根据 isTarget 更新不同的状态
      if (isTarget && setTargetRequest) {
        const updatedRequest = {
          ...targetRequest,
          body: {
            ...targetRequest?.body,
            scfRequest: scfContext,
          },
        };
        setTargetRequest(updatedRequest);
        // setTargetRequest((prev: any) => ({
        //   ...prev,
        //   body: {
        //     ...prev.body,
        //     scfRequest: scfContext,
        //   },
        // }));
      } else {
        dispatch((state) => {
          state.request.body.scfRequest = scfContext;
        });
      }

      const { ip, env, envID, serializedVersion, connectType, isOnlineScfKey, scfKey } = scfContext;

      form.setFieldValue('ip', ip);
      form.setFieldValue('envID', envID || env);
      form.setFieldValue('serializedVersion', serializedVersion);
      form.setFieldValue('isOnlineScfKey', isOnlineScfKey);
      form.setFieldValue('scfKey', scfKey);

      setConnectType(connectType);
      return;
    }
  }, []);

  useEffect(() => {
    const currentRequest = getCurrentRequest();
    // 保存完会丢scfRequest, 直接return, 否则会清除部分数据
    if (!currentRequest.body.scfRequest) {
      return;
    }
    // const { branchVersion, port } = store.request.body.scfRequest;
    const branchVersion = currentRequest.body.scfRequest?.branchVersion;
    const port = currentRequest.body.scfRequest?.port;
    const branchList = currentRequest.body.scfRequest?.branchList;
    form.setFieldValue('branchVersion', branchVersion);
    form.setFieldValue('port', port);
    if (branchList) setBranchOptions(branchList);

    const ipo = currentRequest.body.scfRequest?.ipOptions as IpOptions;
    if (!ipo) {
      return;
    }

    const env = (form.getFieldValue('envID') || form.getFieldValue('env')) as EnvCode;
    const envVal = EnvMapping[env] as EnvValueCode;
    const ipInfos = ipo[envVal];

    setIpAll(ipo);

    if (Array.isArray(ipInfos)) {
      const ipOpts = ipInfos.map((ip) => ({ value: ip }));
      setIpOptions(ipOpts);
      if (!form.getFieldValue('ip')) {
        form.setFieldValue('ip', ipOpts[0]);
      }
      return;
    }

    const allIPs: { value: string }[] = [];
    if (ipInfos) {
      for (const [key, value] of Object.entries(ipInfos)) {
        // 遍历每个IP地址数组
        for (const ip of value) {
          // 将IP地址以对象形式添加到allIPs数组中
          allIPs.push({ value: ip });
        }
      }

      const transformedData = Object.keys(ipInfos).map((key) => ({
        label: <span>{key}</span>,
        title: key,
        options: ipInfos[key].map((ip) => ({
          label: <span>{ip}</span>,
          value: ip,
        })),
      }));

      setIpOptions(transformedData);
      // IP为空默认选第一个
      const currentIP = form.getFieldValue('ip');
      if (!currentIP) {
        form.setFieldValue('ip', allIPs[0]);
      }
      // 当前IP不在IP列表中,改为选第一个
      const isIpInList = allIPs.some((item) => {
        return item.value === currentIP;
      });
      if (!isIpInList) {
        form.setFieldValue('ip', allIPs[0]);
      }
    }
  }, [isTarget, targetRequest, store.request.body.scfRequest, form]);

  const onFormValuesChange = () => {
    if (isTarget && setTargetRequest) {
      const formValues = form.getFieldsValue();
      // 确保 branchOptions 在表单值中可用
      if (branchOptions && !formValues.branchOptions) {
        formValues.branchOptions = branchOptions;
      }

      console.log('targetRequest2222222222', targetRequest);
      console.log('Target form values changed:', form.getFieldsValue());
      console.log('Current branchOptions:', branchOptions);

      // setTargetRequest((prev: any) => ({
      //   ...prev,
      //   body: {
      //     ...prev.body,
      //     scfRequest: {
      //       ...prev.body?.scfRequest,
      //       ...formValues,
      //       // 确保保留 branchList
      //       branchList: prev.body?.scfRequest?.branchList || branchOptions,
      //       serviceID: prev.body?.scfRequest?.serviceID,
      //       // ...(form.getFieldsValue() || {}),
      //     },
      //   },
      // }));
      // 修改这里：不使用函数式更新，而是直接传递一个新对象
      const updatedRequest = {
        ...targetRequest,
        body: {
          ...targetRequest?.body,
          scfRequest: {
            ...targetRequest?.body?.scfRequest,
            ...formValues,
            // 确保保留 branchList
            branchList: targetRequest?.body?.scfRequest?.branchList || branchOptions,
            serviceID: targetRequest?.body?.scfRequest?.serviceID,
          },
        },
      };
      setTargetRequest(updatedRequest);
    } else {
      dispatch((state) => {
        const currentScfRequest = state.request.body.scfRequest || {};
        console.log('currentScfReques33333333t', currentScfRequest);

        const newVal = form.getFieldsValue() || {};
        state.request.body.scfRequest = {
          ...currentScfRequest,
          ...newVal,
          // 确保包含 serviceID
          // serviceID: currentScfRequest.serviceID,
        };
      });
    }
  };

  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
      `}
    >
      <ResponseTestHeader>
        <span>{t('scf.tab.envHeader')}</span>
      </ResponseTestHeader>
      <ResponseTestWrapper>
        <div
          css={css`
            min-width: 0;
            flex: 1;
          `}
        >
        </div>
        <div
          css={css`
            display: flex;
            flex-direction: column;
          `}
        >
          <Form
            name='basic'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 300 }}
            autoComplete='off'
            onFieldsChange={onFormValuesChange}
            form={form}
          >
            <Form.Item
              label='port'
              name='port'
              rules={[{ required: true, message: 'Please input port' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item label='serialized' name='serializedVersion' initialValue='0'>
              <Select
                options={[
                  { value: '0', label: '不指定版本' },
                  { value: 'SCF', label: 'SCF' },
                  { value: 'SCFV3', label: 'SCFV3' },
                  { value: 'SCFV4', label: 'SCFV4' },
                ]}
              />
            </Form.Item>
            <Form.Item label='branch' name='branchVersion'>
              <Select options={branchOptions} />
            </Form.Item>
          </Form>
        </div>
      </ResponseTestWrapper>
    </div>
  );
};

export default EnvConfig;
