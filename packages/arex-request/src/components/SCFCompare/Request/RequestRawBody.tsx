import { css, Theme, useArexCoreConfig } from '@arextest/arex-core';
import { Editor } from '@monaco-editor/react';
import { App, Space, Typography } from 'antd';
import jsonBigInt from 'json-bigint';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { scfParams } from '../../../types/SCFRequest';

const { Text } = Typography;

export type RequestRawBodyRef = {
  prettifyRequestBody: () => void;
};

export type RequestRawBodyProps = {
  item?: scfParams;
  index?: number;
  value?: string; // 新增
  onChange?: (val: string) => void; // 新增
  isTarget?: boolean; // 新增
  targetRequest?: any; // 新增
  setTargetRequest?: (value: any) => void; // 新增
};

const RequestRawBody = forwardRef<RequestRawBodyRef, RequestRawBodyProps>((props: any, ref) => {
  const { theme } = useArexCoreConfig();
  const { store, dispatch } = useArexRequestStore();
  const [param, setParam] = useState(props.item);

  const { message } = App.useApp();
  const { t } = useTranslation();

  const prettifyRequestBody = () => {
    if (props.isTarget && props.setTargetRequest && props.targetRequest) {
      props.setTargetRequest((prev: any) => {
        const params = prev?.body?.scfRequest?.params;
        const p = params?.map((item: any) => {
          let val = item.exampleValue as string;
          try {
            val = jsonBigInt.stringify(jsonBigInt.parse(val), null, 2);
            return {
              ...item,
              exampleValue: val,
            };
          } catch (e) {
            return item;
          }
        });
        return {
          ...prev,
          body: {
            ...prev.body,
            scfRequest: {
              ...prev.body.scfRequest,
              params: p,
            },
          },
        };
      });
    } else {
      dispatch((state) => {
        // let body = state?.request?.body?.body as string;
        // try {
        //   body = JSON.stringify(JSON.parse(body), null, 2);
        // } catch (e) {
        //   // message.error(t('error.json_prettify_invalid_body'));
        //   return;
        // }
        // state.request.body.body = body;
        const params = state?.request?.body?.scfRequest?.params;
        const p = params?.map((item) => {
          let val = item.exampleValue as string;
          try {
            val = jsonBigInt.stringify(jsonBigInt.parse(val), null, 2);
            return {
              ...item,
              exampleValue: val,
            };
          } catch (e) {
            return item;
          }
        });
        state.request.body.scfRequest.params = p;
      });
    }
  };

  useImperativeHandle(
    ref,
    () => ({
      prettifyRequestBody,
    }),
    [props.isTarget, props.setTargetRequest, props.targetRequest, dispatch],
  );
  const handleChange = (val: string | undefined) => {
    if (props.onChange) {
      props.onChange(val ?? '');
    } else {
      dispatch((state) => {
        if (val !== undefined && state.request.body.scfRequest?.params) {
          state.request.body.scfRequest.params[props.index].exampleValue = val;
        }
      });
    }
  };

  return (
    <div
      css={css`
        height: 100%;
        flex: 1;
        overflow-y: auto;
      `}
    >
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        {/* {props.index == 0 ? <a onClick={prettifyRequestBody}>{t('action.prettify')}</a> : null} */}

        {/* <Space size={30}>
          <Text type='secondary'>
            参数名: {props?.item?.fieldName ? props?.item?.fieldName : '暂无'}
          </Text>
          <Text type='secondary'>参数类型: {props?.item?.paramKey}</Text>
        </Space> */}
      </div>
      <Editor
        theme={theme === Theme.dark ? 'vs-dark' : 'light'}
        options={{
          minimap: {
            enabled: false,
          },
          fontSize: 12,
          wordWrap: 'on',
          automaticLayout: true,
          fontFamily: 'IBMPlexMono, "Courier New", monospace',
          scrollBeyondLastLine: false,
        }}
        language={'json'}
        // value={store.request.body.body as string}
        value={
          store.request.body.scfRequest.params
            ? (store.request.body.scfRequest.params[props.index].exampleValue as string)
            : ''
        }
        onChange={handleChange}
      />
    </div>
  );
});

export default RequestRawBody;
