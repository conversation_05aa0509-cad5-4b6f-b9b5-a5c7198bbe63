import { css } from '@arextest/arex-core';
import { Radio, RadioChangeEvent, Select, Typography } from 'antd';
import React, { FC, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { ArexContentTypes, ArexRESTRequest, SCFRequest } from '../../../types';
import RequestBinaryBody from './RequestBinaryBody';
import RequestBodyFormData from './RequestBodyFormData';
import RequestRawBody, { RequestRawBodyRef } from './RequestRawBody';

const genContentType = (contentType?: string) =>
  contentType?.includes('application/json') ? 'raw' : 'binary';

const bigCateOptions = ['raw', 'binary'];

const rawSmallCateOptions = [
  {
    label: 'JSON',
    value: 'application/json',
    // test: <a>JSON</a>,
  },
  {
    label: 'form-data',
    value: 'multipart/form-data',
    // test: <a>form-data</a>,
  },
  // {
  //   label: 'octet-stream',
  //   value: 'application/octet-stream',
  //   // test: <a>octet-stream</a>,
  // },
];
interface RequestBodyProps {
  value: ArexRESTRequest;
  onChange?: (value: ArexRESTRequest) => void;
}

const RequestBody: FC<RequestBodyProps> = ({ value, onChange }) => {
  const { t } = useTranslation();
  const { store, dispatch } = useArexRequestStore();

  const rawBodyRef = useRef<RequestRawBodyRef>(null);

  const isJsonContentType = useMemo(() => {
    return ['application/json'].includes(value?.body?.contentType || '');
  }, [value?.body?.contentType]);

  const isFormDataContentType = useMemo(() => {
    return ['multipart/form-data'].includes(value?.body?.contentType || '');
  }, [value?.body?.contentType]);

  // const onChange = (value: ArexContentTypes) => {
  //   dispatch((state) => {
  //     state.request.body.contentType = value;
  //   });
  // };

  // const handleContentTypeChange = (val: RadioChangeEvent) => {
  //   const newRequest = { ...value };
  //   if (val.target.value === 'binary') {
  //     dispatch((state) => {
  //       // @ts-ignore
  //       state.request.body.contentType = '0';
  //       // state.request.body.body = '';
  //     });
  //   }
  //   if (val.target.value === 'raw') {
  //     dispatch((state) => {
  //       state.request.body.contentType = 'application/json';
  //       // state.request.body.body = '';
  //     });
  //   }
  // };
  // 替换所有store.request.body为value.body
  const handleContentTypeChange = (e: RadioChangeEvent) => {
    const newRequest = { ...value };
    if (e.target.value === 'binary') {
      newRequest.body = {
        contentType: 'application/octet-stream',
        body: '',
        scfRequest: {} as any, // 根据实际类型替换
        formData: [],
      };
    } else {
      newRequest.body = {
        contentType: 'application/json',
        body: '{}',
        scfRequest: {} as any, // 根据实际类型替换
        formData: [],
      };
    }
    onChange?.(newRequest);
  };

  useEffect(() => {
    if (
      value?.body.contentType?.includes('application/json') &&
      value?.body.contentType !== 'application/json'
    ) {
      dispatch((state) => {
        state.request.body.contentType = 'application/json';
      });
    }
  }, [dispatch, value?.body.contentType]);
  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
      `}
    >
      <div
        css={css`
          display: flex;
          justify-content: space-between;
          margin: 6px 0;
          padding-bottom: 6px;
        `}
      >
        <div>
          <Radio.Group
            options={bigCateOptions}
            value={genContentType(value?.body.contentType)}
            onChange={handleContentTypeChange}
          />
          <Typography.Text strong type='secondary'>
            Content Type
          </Typography.Text>
          <Select
            value={value?.body.contentType}
            variant='borderless'
            size={'small'}
            popupMatchSelectWidth={false}
            options={rawSmallCateOptions}
            optionLabelProp={'test'}
            onChange={(val) => {
              const isFormData = val === 'multipart/form-data';
              const isJSON = val === 'application/json';
              // 明确类型断言
              const bodyValue = isFormData
                ? ([] as unknown as string) // 根据实际存储需求进行类型转换
                : isJSON
                  ? '{}'
                  : '';

              onChange?.({
                ...value,
                body: {
                  ...value.body,
                  contentType: val,
                  body: bodyValue,
                  // body: isFormData ? ([] as never[]) : val === 'application/json' ? '{}' : '', // 明确类型断言
                  formData: isFormData ? [] : value.body.formData,
                  // 确保所有接口要求的属性都存在
                  scfRequest: (isFormData ? undefined : value.body.scfRequest || {}) as SCFRequest,
                  wmb: value.body.wmb,
                  wmb_mock: value.body.wmb_mock,
                  wmb_Consumer: value.body.wmb_Consumer,
                },
              });
            }}
            // onChange={(val) =>
            //   onChange?.({
            //     ...value,
            //     body: { ...value.body, contentType: val },
            //   })
            // }
            // onChange={onChange}
            style={{ width: 'auto' }}
          />
        </div>

        {isJsonContentType && (
          <a onClick={rawBodyRef?.current?.prettifyRequestBody}>{t('action.prettify')}</a>
        )}
      </div>

      {/* {isJsonContentType ? <RequestRawBody ref={rawBodyRef} /> : <RequestBinaryBody />} */}

      {isJsonContentType ? (
        <RequestRawBody
          ref={rawBodyRef}
          value={value?.body?.body || ''}
          onChange={(body: any) => {
            onChange?.({
              ...value,
              body: { ...value.body, body },
            })
          }
          }
        />
      ) : isFormDataContentType ? (
        <RequestBodyFormData />
      ) : (
        null
        // <RequestBinaryBody />
      )}
    </div>
  );
};

export default RequestBody;
