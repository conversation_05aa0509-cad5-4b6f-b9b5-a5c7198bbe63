import { css, SmallBadge, styled } from '@arextest/arex-core';
import { Tabs } from 'antd';
import { FC, useMemo, useState } from 'react';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { Tab, TabConfig } from '../../../ArexRequest';
import { useArexRequestProps, useArexRequestStore } from '../../../hooks';
import PreRequestScript from './PreRequestScript';
import RequestBody from './RequestBody';
import RequestHeaders from './RequestHeaders';
import RequestParameters from './RequestParameters';
import RequestTests from './RequestTests';
import { ArexRESTRequest } from '../../../types';

const HttpRequestOptionsWrapper = styled.div`
  height: 100%;
  padding: 0 16px 40px;
  flex: 1;
  display: flex;
  flex-direction: column;
  .ant-tabs-content-holder {
    height: 100px;
  }
`;

// export interface HttpRequestOptionsProps {
//   config?: TabConfig;
//   value: ArexRESTRequest;
//   onChange?: (value: ArexRESTRequest) => void;
// }
export interface HttpRequestOptionsProps {
  config?: TabConfig;
  originalValue?: ArexRESTRequest;
  targetValue?: ArexRESTRequest;
  value: ArexRESTRequest;
  onChange?: (newRequest: any) => void;
}

const HttpRequestOptions: FC<HttpRequestOptionsProps> = ({ value, onChange }) => {
  const { config } = useArexRequestProps();
  // const { store } = useArexRequestStore();

  console.log('---------value', value)

  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState('body');

  const items = useMemo(() => {
    const _items: Tab[] = [
      {
        label: <SmallBadge count={value.params?.length}>{t('tab.parameters')}</SmallBadge>,
        key: 'parameters',
        children: <RequestParameters value={value} onChange={onChange} />,
        forceRender: true,
      },
      {
        label: <SmallBadge count={value.headers?.length}>{t('tab.headers')}</SmallBadge>,
        key: 'headers',
        children: <RequestHeaders value={value} onChange={onChange} />,
        // forceRender: true,
      },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!value?.body?.body?.length}>
            {t('tab.body')}
          </SmallBadge>
        ),
        key: 'body',
        children: <RequestBody value={value} onChange={onChange} />,
        forceRender: true,
      },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!value?.preRequestScript?.length}>
            {t('tab.pre_request_script')}
          </SmallBadge>
        ),
        key: 'pre_request_script',
        children: <PreRequestScript value={value} onChange={onChange} />,
        forceRender: true,
      },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!value.testScript?.length}>
            {t('tab.tests')}
          </SmallBadge>
        ),
        key: 'tests',
        children: <RequestTests value={value} onChange={onChange} />,
        forceRender: true,
      },
    ];

    // concat extra request tabs
    return _items.concat(config?.requestTabs?.extra?.filter((tab) => !tab.hidden) || []);
  }, [value, t]);
  // [store.request, t]
  return (
    <HttpRequestOptionsWrapper>
      <Tabs
        activeKey={activeKey}
        items={items}
        onChange={setActiveKey}
        css={css`
          height: 100%;
          .ant-tabs-nav {
            margin-bottom: 0;
          }
        `}
      />
    </HttpRequestOptionsWrapper>
  );
};

export default HttpRequestOptions;
