import { ExclamationOutlined, SendOutlined } from '@ant-design/icons';
import { css, Label, RequestMethod, styled } from '@arextest/arex-core';
import { Button, Checkbox, Select, Tabs, TabsProps } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import CaseDiffTooltip from '../../../../../arex/src/panes/ReplayCase/CaseDiff/CaseDiffTooltip';
import { addUserAction, sendRequest, UserActionEnum } from '../../../helpers';
import { useArexRequestProps, useArexRequestStore } from '../../../hooks';
import { ArexEnvironment, ArexRESTRequest, ArexRESTResponse } from '../../../types';
import { EnvironmentSelectProps } from '../../NavigationBar/EnvironmentSelect';
import { InfoSummaryProps } from '../../NavigationBar/InfoSummary';
import EnvInput from './EnvInput';
import HttpRequestOptions from './RequestOptions';

const HeaderWrapper = styled.div`
  padding: 0 8px;
  display: flex;
  .ant-select-selector {
    border-radius: 6px 0 0 6px;
  }
`;
// 类型定义保持不变
type RequestType = 'original' | 'target';

export type RequestProps = {
  disableSave?: boolean;
  onBeforeRequest?: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest;
  onRequest?: (
    reqData: { request: ArexRESTRequest; environment?: ArexEnvironment },
    resData: Awaited<ReturnType<typeof sendRequest>>,
  ) => void;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
  onSaveAs?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
} & InfoSummaryProps & {
    environmentProps?: EnvironmentSelectProps;
  };

const Request = (props: { setCaseItemId: (val: string) => void } & RequestProps) => {
  const { onBeforeRequest = (request: ArexRESTRequest) => request, onRequest } =
    useArexRequestProps();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();
  const [endpointStatus, setEndpointStatus] = useState<'error'>();

  // 新增 target 请求状态
  const [targetRequest, setTargetRequest] = useState<ArexRESTRequest>({
    ...store.request,
    headers: JSON.parse(JSON.stringify(store.request.headers)),
    body: JSON.parse(JSON.stringify(store.request.body)),
  });
  console.log('store.request.body442333222222', store.request, targetRequest);
  console.log('store.request.header', store.request.headers);

  const handleRequest = async () => {
    void addUserAction(UserActionEnum.SEND_HTTP);
    const requests = {
      original: store.request,
      target: targetRequest,
    };
    if (!requests.original.endpoint || !requests.target.endpoint) {
      setEndpointStatus('error');
      setTimeout(() => {
        setEndpointStatus(undefined);
      }, 3000);
      window.message.error(t('error.emptyEndpoint'));
      return;
    }
    dispatch((state) => {
      state.response = {
        type: 'loading',
        headers: undefined,
      };
    });

    const requestTmp = { ...store.request };

    let shadowcookie = '';
    let shadowhost = '';

    requestTmp?.headers.forEach((header) => {
      if (header.key.toLowerCase() == 'cookie') {
        shadowcookie = `${header.value};${shadowcookie}`;
      }
      if (header.key.toLowerCase() == 'host') {
        shadowhost = `${header.value}`;
      }
    });

    if (shadowcookie.length > 0) {
      const newHeaders = [...requestTmp.headers];
      newHeaders.push({
        key: 'shadowcookie',
        value: shadowcookie,
        active: true,
      });
      requestTmp.headers = newHeaders;
    }
    // 使用展开运算符创建新数组
    requestTmp.headers = [
      ...requestTmp.headers,
      {
        // id: String(Math.random()),
        key: 'arex-force-record',
        value: 'true',
        active: true,
      },
    ];

    if (shadowhost.length > 0) {
      const newHeaders = [...requestTmp.headers];
      newHeaders.push({
        key: 'shadowhost',
        value: shadowhost,
        active: true,
      });
      requestTmp.headers = newHeaders;
    }

    const res = await sendRequest(onBeforeRequest(requestTmp), store.environment);
    console.log('requestTmp', requestTmp);

    onRequest?.({ request: store.request, environment: store.environment }, res);
    dispatch((state) => {
      state.response = res.response;
      state.consoles = res.consoles;
      state.visualizer = res.visualizer;
      state.testResult = res.testResult;
    });
    dispatch((state) => {
      state.response = res.response;
      state.consoles = res.consoles;
      state.visualizer = res.visualizer;
      state.testResult = res.testResult;
    });
    // 分别处理 target 请求
    dispatch((state) => {
      state.response = {
        type: 'loading',
        headers: undefined,
      };
    });
    const targetRequestTmp = { ...requests.target };
    let targetShadowcookie = '';
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    let targetShadowhost = '';

    targetRequestTmp?.headers.forEach((header) => {
      if (header.key.toLowerCase() === 'cookie') {
        targetShadowcookie = `${header.value};${targetShadowcookie}`;
      }
      if (header.key.toLowerCase() === 'host') {
        targetShadowhost = `${header.value}`;
      }
    });
    if (targetShadowcookie.length > 0) {
      const newHeaders = [...targetRequestTmp.headers];
      newHeaders.push({
        key: 'shadowcookie',
        value: targetShadowcookie,
        active: true,
      });
      targetRequestTmp.headers = newHeaders;
    }
    // 使用展开运算符替代 push
    targetRequestTmp.headers = [
      ...targetRequestTmp.headers,
      {
        // id: String(Math.random()),
        key: 'arex-force-record',
        value: 'true',
        active: true,
      },
    ];

    if (targetShadowhost.length > 0) {
      const newHeaders = [...targetRequestTmp.headers];
      newHeaders.push({
        key: 'shadowhost',
        value: targetShadowhost,
        active: true,
      });
      targetRequestTmp.headers = newHeaders;
    }

    console.log('targetRequestTmp', targetRequestTmp);

    const targetRes = await sendRequest(onBeforeRequest(targetRequestTmp), store.environment);

    // 新增上报接口调用
    try {
      const reportData = {
        base: {
          requestType: 'HTTP',
          operationName: store.request.endpoint,
          response: {
            headers: res.response?.headers || [],
            body: JSON.parse(res.response?.body) || {},
          },
        },
        test: {
          requestType: 'HTTP',
          operationName: targetRequestTmp.endpoint,
          response: {
            headers: targetRes.response?.headers || [],
            body: JSON.parse(targetRes.response?.body) || {},
          },
        },
      };

      // const res2 = await fetch('http://************:8080/api/ipsExecResultCompare', {
      const res2 = await fetch('/schedule/ipsExecResultCompare', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });
      // 新增响应结构获取
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const responseData = await res2.json();

      // 重置 caseItemId 以触发重新渲染
      props?.setCaseItemId('');
      // 延迟设置新的 caseItemId，确保组件能够正确识别变化
      setTimeout(() => {
        props?.setCaseItemId(responseData.data);
        console.log('responseData-ID', responseData.data);
      }, 100);
    } catch (e) {
      console.error('上报请求失败:', e);
    }
    console.log('targetRequest33333333', targetRequest);

    onRequest?.({ request: requests.target, environment: store.environment }, targetRes);
    dispatch((state) => {
      state.response = targetRes.response;
      state.consoles = targetRes.consoles;
      state.visualizer = targetRes.visualizer;
      state.testResult = targetRes.testResult;
    });
  };
  const items: TabsProps['items'] = [
    {
      key: 'original',
      label: 'Original',
      children: (
        <div
          style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {' '}
          <HeaderWrapper>
            <Select
              disabled={store?.request?.inherited}
              css={css`
                width: 80px;
                transform: translateX(1px);
              `}
              value={
                store?.request?.inherited ? store.request.inheritedMethod : store?.request?.method
              }
              options={RequestMethod.map((i: string) => ({ value: i, label: i }))}
              onChange={(value) => {
                dispatch((state) => {
                  state.request.method = value;
                });
              }}
            />

            <EnvInput
              disabled={store?.request?.inherited}
              status={endpointStatus}
              value={
                store?.request?.inherited
                  ? store.request.inheritedEndpoint
                  : store?.request?.endpoint
              }
              onChange={(v) => {
                dispatch((state) => {
                  state.request.endpoint = v || '';
                });
              }}
            />

            {store.request?.inherited && (
              <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
                <Label type='secondary'>{t('request.inherit')}</Label>
                <Checkbox
                  checked={store.request.inherited}
                  onChange={(val) => {
                    dispatch((state) => {
                      state.request.inherited = val.target.checked;
                    });
                  }}
                />
              </div>
            )}
          </HeaderWrapper>
          <HttpRequestOptions
            value={store.request}
            onChange={(newRequest) =>
              dispatch((state) => {
                state.request = newRequest;
              })
            }
            originalValue={undefined}
            targetValue={undefined}
          />
        </div>
      ),
    },
    {
      key: 'target',
      label: 'Target',
      children: (
        <div
          style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {' '}
          <HeaderWrapper>
            <Select
              disabled={targetRequest?.inherited}
              css={css`
                width: 80px;
                transform: translateX(1px);
              `}
              value={
                targetRequest?.inherited ? targetRequest.inheritedMethod : targetRequest?.method
              }
              options={RequestMethod.map((i: string) => ({ value: i, label: i }))}
              onChange={(value) => {
                setTargetRequest((prev) => ({
                  ...prev,
                  method: value,
                }));
              }}
              // dispatch((state) => {
              //   state.request.method = value;
              // });
            />

            <EnvInput
              disabled={targetRequest?.inherited}
              status={endpointStatus}
              value={
                targetRequest?.inherited ? targetRequest.inheritedEndpoint : targetRequest?.endpoint
              }
              // onChange={(v) => {
              //   dispatch((state) => {
              //     state.request.endpoint = v || '';
              //   });
              // }}
              onChange={(v) => {
                setTargetRequest((prev) => ({
                  ...prev,
                  endpoint: v || '',
                }));
              }}
            />

            {targetRequest?.inherited && (
              <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
                <Label type='secondary'>{t('request.inherit')}</Label>
                <Checkbox
                  checked={targetRequest.inherited}
                  // onChange={(val) => {
                  //   dispatch((state) => {
                  //     state.request.inherited = val.target.checked;
                  //   });
                  // }}
                  onChange={(val) => {
                    setTargetRequest((prev) => ({
                      ...prev,
                      inherited: val.target.checked,
                    }));
                  }}
                />
              </div>
            )}
          </HeaderWrapper>
          <HttpRequestOptions
            value={targetRequest}
            onChange={(newRequest) => {
              console.log(newRequest, 'newRequest');
              setTargetRequest(newRequest);
            }}
            originalValue={undefined}
            targetValue={undefined}
          />
        </div>
      ),
    },
  ];

  return (
    <Tabs
      defaultActiveKey='original'
      items={items}
      tabBarExtraContent={{
        right: (
          <Button
            id='arex-request-send-btn'
            type='primary'
            loading={store.response?.type === 'loading'}
            disabled={store.response?.type === 'extensionNotInstalled'}
            icon={
              store.response?.type === 'extensionNotInstalled' ? (
                <ExclamationOutlined />
              ) : (
                <SendOutlined />
              )
            }
            onClick={handleRequest}
            style={{ marginRight: 8 }}
          >
            {t('action.send')}
          </Button>
        ),
      }}
      tabBarStyle={{
        padding: '0px',
        margin: 10,
      }}
      style={{
        height: '100%',
      }}
    ></Tabs>
  );
};

export default Request;
