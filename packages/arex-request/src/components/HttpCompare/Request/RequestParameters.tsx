import { CopyOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { copyToClipboard, SpaceBetweenWrapper, TooltipButton } from '@arextest/arex-core';
import { App, Typography } from 'antd';
import PM from 'postman-collection';
import React, { FC, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { ArexRESTParam, ArexRESTRequest } from '../../../types';
import HeadersTable from '../../HeadersTable';
interface RequestParametersProps {
  value?: ArexRESTRequest;
  onChange?: (value: ArexRESTRequest) => void;
}

const RequestParameters: FC<RequestParametersProps> = ({ value, onChange }) => {
  const { t } = useTranslation();
  const { message } = App.useApp();
  const { store, dispatch } = useArexRequestStore();
  const { endpoint } = store.request;

  // const setParams = (params: ArexRESTParam[]) => {
  //   dispatch((state) => {
  //     state.request.params = params;
  //   });
  // };
  // 移除 store 相关状态管理
  const params = useMemo(() => value?.params || [], [value?.params]);

  const handleChange = (newParams: ArexRESTParam[]) => {
    onChange?.({
      ...value,
      params: newParams,
    } as any);
  };

  useEffect(() => {
    dispatch((state) => {
      const query = PM.Url.parse(state.request.endpoint).query || [];
      if (
        JSON.stringify(query) !== JSON.stringify(params.map(({ key, value }) => ({ key, value })))
      ) {
        if (typeof query !== 'string') {
          // @ts-ignore
          state.request.params = query.map(({ id, key, value }, index) => ({
            key,
            value: value || '',
            active: true,
            id: id || String(Math.random()),
          }));
        }
      }
    });
  }, [endpoint]);

  useEffect(() => {
    dispatch((state) => {
      state.request.endpoint = new PM.Url({
        ...PM.Url.parse(endpoint),
        query: state.request.params,
      }).toString();
    });
  }, [dispatch, endpoint, params]);

  // Uncomment this function
  const handleCopyParameters = () => {
    copyToClipboard(JSON.stringify(params.map((i) => ({ key: i.key, value: i.value }))));
    message.success('copy success🎉');
  };

  return (
    <div>
      <SpaceBetweenWrapper>
        <Typography.Text type='secondary'>{t('request.parameter_list')}</Typography.Text>

        <div>
          {/* Use handleCopyParameters for onClick */}
          <TooltipButton
            title={t('action.copy')}
            icon={<CopyOutlined />}
            onClick={handleCopyParameters}
          />

          <TooltipButton
            title={t('action.clear_all')}
            icon={<DeleteOutlined />}
            // onClick={() => {
            //   setParams([]);
            // }}
            onClick={() => {
              handleChange([]);
            }}
          />

          <TooltipButton
            title={t('add.new')}
            icon={<PlusOutlined />}
            // onClick={() => {
            //   setParams(
            //     params.concat([{ value: '', key: '', id: String(Math.random()), active: true }]),
            //   );
            // }}
            onClick={() => {
              handleChange([
                // 替换原来的 setParams
                ...params,
                { value: '', key: '', id: String(Math.random()), active: true },
              ]);
            }}
          />
        </div>
      </SpaceBetweenWrapper>

      <HeadersTable
        editable
        rowKey='id'
        pagination={false}
        dataSource={params}
        // @ts-ignore
        onEdit={handleChange}
      />
    </div>
  );
};

export default RequestParameters;
