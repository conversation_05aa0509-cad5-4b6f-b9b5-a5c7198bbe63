import './InterfaceHistory.css'; //导入CSS模块,组件样式

import {
  BugOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  CopyOutlined,
  ExclamationCircleOutlined,
  HistoryOutlined,
  LinkOutlined,
  PlusCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { getLocalStorage } from '@arextest/arex-core';
import { Button, Drawer, FloatButton, message, Pagination, Select, Table, Tag } from 'antd'; //导入Ant Design的UI组件
import type { DrawerProps } from 'antd/es/drawer';
import axios from 'axios';
import copy from 'copy-to-clipboard';
import React, { useEffect, useMemo, useState } from 'react';

// import { get } from '../../../service/scfInfo';
import CaseRunHistoryDetail from './CaseRunHistoryDetail';
import { EMAIL_KEY } from './constrants';
import { get } from '../../../../service/scfInfo';


export interface ChildComponentPropsHis {
  //ChildComponentProps接口,任何使用这个接口作为props类型的组件都需要接收一个名为scfID的字符串参数。
  hisProps: {
    scfId: string;
    navPane: any; // 替换为实际的navPane类型
  };
}
//这个React组件InterfaceHIstoryList主要用于展示接口请求list，并通过抽屉（Drawer）组件显示相关数据
const InterfaceHistoryList: React.FC<ChildComponentPropsHis> = ({ hisProps }) => {
  const [open, setOpen] = useState(false); //控制抽屉（Drawer）是否打开,c初始值关闭
  const [size, setSize] = useState<DrawerProps['size']>(); //抽屉的大小，可以根据需要动态设置
  const [loading, setLoading] = useState(true); //表示数据加载状态。
  //scf历史
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  //const [dataSource, setDataSource] = useState<ScfRunHistoryListItem[]>([]);
  const [dataSource, setDataSource] = useState([]);
  //const dataSource =[];
  const token = getLocalStorage<string>(EMAIL_KEY);
  const scfId = hisProps.scfId;
  const navPane = hisProps.navPane;
  const [expandedDetailsReoponse, setExpandedDetailsReoponse] = useState({});
  const [expandedDetailsRequest, setExpandedDetailsRequest] = useState({});
  const [expandedDetailsParams, setExpandedDetailsParams] = useState({});
  const [expandedDataArray, setExpandedDataArray] = useState<any[]>([]);
  const [userValue, setUserValue] = useState<String>();
  const [usersList, setUserList] = useState<any[]>([]);


  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize]);

  const fetchData = async (extendData?: Record<string, any>) => {
    setLoading(true);
    const params = {
      scfId,
      current: extendData ? 1 : pagination.current,
      pageSize: pagination.pageSize,
      userName: userValue,
      ...(extendData || {}),
    }
    // console.log("fetchData.params: " + JSON.stringify(params, null, 4));
    try {
      const response: any = await axios.get('/iapi/iapi/scence/getHistory', { params });
      //console.log('response is', response.data.data);
      if (response.data.code === '0' && Array.isArray(response.data.data.records)) {
        setDataSource(response.data.data.records);
        setPagination({
          ...pagination,
          current: extendData ? 1 : pagination.current,
          total: response.data.data.total,
        });
      } else {
        console.error('获取数据失败');
      }
    } catch (error) {
      console.error('请求错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (paginationParams: any) => {
    // console.log('paginationParams', paginationParams);
    setPagination({
      ...pagination,
      current: paginationParams,
      pageSize: 10,
    });
  };

  // showDrawer 函数的作用就是：当调用此函数时，不仅会把抽屉的尺寸调整为 'large'，还会打开抽屉使其在用户界面上可见
  const showDrawer = () => {
    if (scfId === '0') {
      message.error('请先选择接口！');
    } else {
      fetchData();
      setSize('large');
      setOpen(true);
    }
  };
  // onClose 函数的作用是在触发关闭动作时执行，使得与之关联的抽屉或模态窗口隐藏起来。
  const onClose = () => {
    setOpen(false);
  };

  // 枚举状态图标定义
  const StatusIcon = ({ status }: any) => {
    //const status = props.stauts;
    //console.log("status",status)
    switch (status) {
      case 'Default':
        return (
          <Tag icon={<ClockCircleOutlined />} color='#faad14'>
            未执行
          </Tag>
        );
      case 'Success':
        return (
          <Tag icon={<CheckCircleOutlined />} color='green'>
            成功
          </Tag>
        );
      case 'Processing':
        return (
          <Tag icon={<SyncOutlined spin />} color='#1890ff'>
            测试
          </Tag>
        );
      case 'Error':
        return (
          <Tag icon={<CloseCircleOutlined />} color='red'>
            失败
          </Tag>
        );
      default:
        return null;
    }
  };
  const StatusEnum: any = {
    2: { text: '未执行', status: 'Default' },
    1: { text: '成功', status: 'Success' },
    3: { text: 'Test', status: 'Processing' },
    0: { text: '失败', status: 'Error' },
  };
  const BranchEnum: any = {
    9: '无环境',
    0: 'Test',
    1: 'Stable',
    2: 'Sandbox',
    3: 'Online',
    4: '其他环境',
  };

  const getRequestId = async (hisId: string) => {
    const requestBody = {
      historyId: hisId,
      scfId: scfId,
      id: 0,
      workspaceId: '65e19e5e8b74852f1dd26241',
      parentPath: ['65e19e5e8b74852f1dd26242'],
    };

    const accessToken = getLocalStorage<string>('accessToken');
    const appId = getLocalStorage<string>('appId');
    try {
      const response: any = await axios.post(
        `/report/filesystem/addItemAndSaveHistoryInterface`,
        requestBody,
        {
          headers: {
            'access-token': accessToken,
            appId,
          },
        },
      );
      // setRowData(response.body.infoId);
      //console.log("response&&&",response);
      console.log('response&&&', response.data.body.infoId);
      return response.data.body.infoId;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const handleClick = async (record: any) => {
    const rowData = await getRequestId(record);
    console.log('rowData', rowData);
    //const rowData= '65e19ea08b74852f1dd2624a'
    // 在数据获取成功之后再跳转（这里假设rowData中的某个字段作为跳转依据）
    if (rowData) {
      //window.open(`/collection/request/65e19e5e8b74852f1dd26241-1-${rowData}`)
      navPane({
        type: `request`,
        id: `65e19e5e8b74852f1dd26241-1-${rowData}`,
        //icon: result.method,
        name: 'Untitled',
      });
      setOpen(false);
    }
    // 或者如果不需要等待数据加载完成就可以跳转，则直接在这里使用Link组件或其他方式跳转
    // <Link to={`/targetRoute?data=${encodeURIComponent(JSON.stringify(rowData))}`} />
  };

  //处理copy链接的
  const handleCopy = async (record: any) => {
    try {
      const rowData = await getRequestId(record);
      if (rowData) {
        const copyLink = `https://${window.location.hostname}/collection/request/65e19e5e8b74852f1dd26241-1-${rowData}`;
        // 复制链接到剪贴板
        copy(copyLink);
        message.success('已复制');
      } else {
        console.error('Failed to fetch the request ID');
      }
    } catch (error) {
      console.error('Error occurred while fetching request ID:', error);
    }
  };
  const getBranchList = async (historyId: string) => {
    try {
      return await get(`/iapi/iapi/scence/openapi/getScfHistoryByHistoryId?historyId=${historyId}`);
    } catch (error) {
      console.error(error);
    }
  };

  const handleExpand = async (record: any) => {
    const rowData = await getBranchList(record);
    setExpandedDetailsParams(rowData);
    const requestStr = JSON.stringify({
      cfID: rowData.data.scfID,
      branchName: rowData.data.branchName,
      scfIP: rowData.data.status,
      scfPort: rowData.data.scfPort,
      scfKey: rowData.data.scfKey,
    });
    const paramsStr = rowData.data.params;
    const responseBodyStr = rowData.data.responseBody;

    const dataArray = [
      { title: 'request', info: requestStr },
      { title: 'params', info: paramsStr },
      { title: 'responseBody', info: responseBodyStr },
    ];

    setExpandedDataArray(dataArray);
  };

  const locale = {
    emptyText: '暂无数据',
  };


  const handleChange = async (value: any) => {
    setUserValue(value);
    fetchData({ userName: value });
  }

  const handleSearch = (value: string) => {
    value && getUsersByKeyword(value);
  };

  const getUsersByKeyword = async (keyword: string) => {
    const accessToken = getLocalStorage<string>('accessToken');
    const appId = getLocalStorage<string>('appId');
    try {
      axios.get('/webApi/login/listUsers/' + keyword, {
        headers: {
          'access-token': accessToken,
          appId,
        },
      }).then(res => {
        setUserList(res.data.body)
      });
    } catch (error) {
      console.error(error);
    }
  };

  const usersOptions = useMemo(
    () =>
      usersList?.map((user: any) => ({
        label: user.userName,
        value: user.userName,
      })),
    [usersList],
  );


  const columns = [
    {
      title: '序号',
      key: 'index',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 50,
      render: (text: any, record: { historyID: unknown }, index: number) => {
        <PlusCircleOutlined
          className='expand-icon'
          onClick={() => handleExpand(record?.historyID)}
        />;
        return (pagination.current - 1) * pagination.pageSize + index + 1;
      },
    },
    {
      title: '状态',
      width: 80,
      dataIndex: 'status',
      valueEnum: StatusEnum,
      render: (text: any) => {
        const item = StatusEnum[text];
        return <StatusIcon status={item?.status} />;
      },
    },
    {
      title: '分支名',
      width: 80,
      alignItems: 'center',
      dataIndex: 'branchName',
      ellipsis: true,
    },
    {
      title: '操作人',
      width: 80,
      alignItems: 'center',
      dataIndex: 'realName',
      filterDropdown: ({ }) => (
        <Select
          showSearch
          allowClear
          // mode='multiple' maxTagCount={1}
          value={userValue}
          placeholder={'oa账号'}
          options={usersOptions}
          onSearch={handleSearch}
          onChange={handleChange}
          style={{ minWidth: '240px' }}
        />
      ),
      filterOnClose: true,
    },
    {
      title: '环境',
      dataIndex: 'branchEnv',
      alignItems: 'center',
      width: 70,
      valueEnum: BranchEnum,
      render: (text: any) => BranchEnum[text],
    },
    {
      title: '创建时间',
      alignItems: 'center',
      width: 140,
      key: 'createTime',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      alignItems: 'center',
      width: 160,
      valueType: 'option',
      render: (text: string, record: { historyID: unknown }) => (
        <div>
          <Button
            type='link'
            shape='circle'
            icon={<BugOutlined />}
            onClick={() => handleClick(record?.historyID)}
          >
            测试
          </Button>
          <Button
            type='link'
            icon={<CopyOutlined />}
            onClick={async () => {
              handleCopy(record?.historyID);
            }}
          >
            复制链接
          </Button>
        </div>
      ),
    },
  ];

  return (
    <>
      {/* 这是一个用于打开抽屉的浮动按钮 */}
      <FloatButton
        className='history'
        onClick={() => showDrawer()}
        style={{ right: 64 }}
        icon={<HistoryOutlined />}
      />

      <Drawer
        title='历史记录'
        placement='left'
        onClose={onClose}
        open={open}
        size={size}
        width={1000}
      >
        <Table
          columns={columns}
          //dataSource={historyList}
          dataSource={dataSource}
          locale={locale}
          rowKey='historyID'
          pagination={false}
          // expandedRowRender={record  => {handleExpand(record?.hisId)}}
          //expandedRowRender={record => <p style={{ margin: 0 }}>{`test`}</p>}
          //expandedRowRender={expandedRowRender}
          //expandedRowKeys={expandedRowKeys}
          expandable={{
            expandedRowRender: (record: { historyID: unknown }) => (
              <CaseRunHistoryDetail data={expandedDataArray} />
            ),
            columnWidth: -1,
          }}
          onExpand={(expanded, record) => {
            if (expanded) {
              handleExpand(record.historyID);
            }
          }}
        />
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            padding: '16px',
          }}
        >
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            onChange={handleTableChange}
            total={pagination.total}
          // onShowSizeChange={(current, size) => {
          //   //console.log('current', current, size);
          //   setPagination({ ...pagination, current, pageSize: size });
          // }}
          />
        </div>
      </Drawer>
    </>
  );
};

export default InterfaceHistoryList;
