export enum EnvType {
  Test = 0,
  Stable = 1,
  Sandbox = 2,
  Online = 3,
  Other = 4,
  None = 9, // 无环境
}

export enum ExecuteResult {
  Success = 1,
  Fail = 0,
  None = 2, // 未执行
  Running = 3, // 执行中
}

export enum CaseType {
  Http = 1,
  Scf = 3,
  SQL = 5,
  Timer = 6, // 定时器
  Condition = 7, //条件表达式
}

export enum ResponseCode {
  SUCCESS = '0',
  FAIL = '-1',
  SPECIAL_IMPORT = '20005',
  HAS_RELATED = '20016',
  EXIST_DEFAULT_MOCK_RULE = '20029',
}

export enum ProtocolType {
  Http = 0,
  Https = 1,
}

/**
 * http Case相关
 */
export enum MethodType {
  GET = 'GET',
  POST = 'POST',
  OPTIONS = 'OPTIONS',
  PUT = 'PUT',
  DELETE = 'DELETE',
}

export enum RequestType {
  post = 0,
  get = 1,
  put = 2,
  options = 3,
  delete = 4,
}

export enum PostDataType {
  FormData = 0,
  Raw = 1,
  xWwwFormUrlencoded = 3,
  JSON = 4,
}

export enum AutoFlagType {
  No = 0,
  Yes = 1,
}

export enum MailPolicyType {
  SendAll = 0,
  SendAfterFail = 1,
  SendAfterSuccess = 2,
  NoSend = 3,
}
export enum ExecuteAfterFailureType {
  No = 0,
  Yes = 1,
}

export enum ResponseOldCode {
  SUCCESS = '000000',
  // FAIL = '160000',
}

/**
 * 任务类型
 */
export enum TaskType {
  HttpTask = 1,
  ScfTask = 3,
  All = 4,
}

export enum IfInTaskType {
  InTask = 1,
  NotInTask = 0,
}

/**
 * 任务执行状态
 */
export enum StatusType {
  Success = 1,
  Processing = 0,
  Error = 2,
}

// 这个配置真恶心，后端数据层次不齐
export const envID2envString = [
  {
    type: EnvType.Test,
    str: 'test',
  },
  {
    type: EnvType.Stable,
    str: 'stable',
  },
  {
    type: EnvType.Sandbox,
    str: 'sandbox',
  },
  {
    type: EnvType.Online,
    str: 'online',
  },
  {
    type: EnvType.Other,
    str: 'others',
  },
];

export enum ScfKeyEnvType {
  Online = 1,
  OffLine = 2,
}

export enum IpOrKeyType {
  IP = 1,
  KEY = 2,
}

// 关联的集群记录是否有效
export enum ClusterRelateValidStat {
  Valid = 1,
  NotValid = 0,
}

// 关联的集群记录是否关联了Case
export enum RelateCaseStat {
  Related = 1,
  NotRelated = 0,
}

export enum RunType {
  MANUAL_RUN = 1, // 手动调用
  REGULAR_RUN = 2, // 定时任务调用
  IONE_RUN = 3, // ione调用
  SET_RUN = 4, // 集合调用
  EXACT_RUN = 5, // 精准调用
}

// 文字映射关系
export const EnvTextEnum = {
  [EnvType.Test]: { text: '测试环境' },
  [EnvType.Stable]: { text: '稳定环境' },
  [EnvType.Sandbox]: { text: '沙箱环境' },
  [EnvType.Online]: { text: '生产环境' },
  [EnvType.Other]: { text: '其他环境' },
  [EnvType.None]: { text: '无环境' },
};

export const FreeStatusEnum = {
  [0]: { text: '闲置' },
  [1]: { text: '定时任务' },
  [2]: { text: 'IOne' },
  [3]: { text: '普通任务' },
  [4]: { text: '定时任务和ione卡点' },
};

export const ExecuteResultEnum = {
  [ExecuteResult.None]: { text: '未执行', status: 'Default' },
  [ExecuteResult.Running]: {
    text: 'Test',
    status: 'Processing',
  },
  [ExecuteResult.Success]: {
    text: '成功',
    status: 'Success',
  },
  [ExecuteResult.Fail]: {
    text: '失败',
    status: 'Error',
  },
};

export enum TaskInIOne {
  Yes = 1,
  No = 0,
}

export enum CaseActive {
  Yes = 1,
  No = 2,
}

export const ActiveEnum = {
  [CaseActive.Yes]: { text: '是' },
  [CaseActive.No]: { text: '否' },
};

export const SerializedVersionType = [
  { value: 0, label: '未选择版本' },
  { value: 1, label: 'SCF' },
  { value: 2, label: 'SCFV3' },
  { value: 3, label: 'SCFV4' },
];

export const CASE_LIST_CURRENT_TYPE = 'caseType';
export const CASE_LIST_SEARCH_KEY = 'caseListSearch';

export enum CtConfigExeType {
  byIoneMsg = 1,
  byYunMsg = 2,
}

export enum HmCaseType {
  Http = 1,
  Scf = 2,
}

export enum NoticeType {
  Mail = 1,
  Meishi = 2,
  All = 3,
}

export enum IsCoreApi {
  Yes = 1,
  No = 2,
  None = 0,
}

export const EMAIL_KEY = 'email';
export const ACCESS_TOKEN_KEY = 'accessToken';
