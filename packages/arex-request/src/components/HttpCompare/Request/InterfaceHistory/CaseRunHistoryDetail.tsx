import { Card, Typography } from 'antd';
import React from 'react';
import React<PERSON><PERSON> from 'react-json-view';

const gridStyle = {
  width: '33%',
  maxHeight: 300,
  overflow: 'auto',
};

const CaseRunHistoryDetail: React.FC<{
  data: {
    title: string;
    info: string; // json
  }[];
}> = ({ data }) => (
  <Card>
    {data?.map((item, index) => (
      <Card.Grid style={gridStyle} key={index}>
        <Typography.Title level={5}>{item?.title}</Typography.Title>
        <ReactJson
          name={false}
          collapsed={false}
          src={JSON.parse(item?.info ?? '')}
          displayDataTypes={false}
          iconStyle='square'
          displayObjectSize={false}
        />
      </Card.Grid>
    ))}
  </Card>
);
export default CaseRunHistoryDetail;
