import { copyToClipboard, css, styled } from '@arextest/arex-core';
import { App, Button, Form, Input, InputNumber, Tooltip } from 'antd';
import PM from 'postman-collection';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { ArexRESTParam } from '../../../types';
import { IWmb } from '../../../types/ArexRESTReqBody';

const RequestParameters = () => {
  const { t } = useTranslation();
  const { message } = App.useApp();
  const { store, dispatch } = useArexRequestStore();
  const { endpoint, params } = store.request;
  const [form] = Form.useForm<IWmb>();

  const setParams = (params: ArexRESTParam[]) => {
    dispatch((state) => {
      state.request.params = params;
    });
  };

  const handleWmbChange = () => {
    dispatch((state) => {
      const newVal = form.getFieldsValue() || {};
      state.request.body.wmb = newVal;
    });
  };

  // TODO: Optimize dependency change
  useEffect(() => {
    dispatch((state) => {
      const query = PM.Url.parse(state.request.endpoint).query || [];
      // console.log('------query', state.request);
      if (
        JSON.stringify(query) !== JSON.stringify(params.map(({ key, value }) => ({ key, value })))
      ) {
        if (typeof query !== 'string') {
          // @ts-ignore
          state.request.params = query.map(({ id, key, value }, index) => ({
            key,
            value: value || '',
            active: true,
            id: id || String(Math.random()),
          }));
        }
      }
    });
  }, [endpoint]);

  useEffect(() => {
    store.request?.wmb ? form.setFieldsValue(store.request?.wmb) : form.resetFields();
    handleWmbChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [store.request?.wmb]);

  useEffect(() => {
    dispatch((state) => {
      state.request.endpoint = new PM.Url({
        ...PM.Url.parse(endpoint),
        query: state.request.params,
      }).toString();
    });
  }, [params]);

  const handleCopyParameters = () => {
    copyToClipboard(JSON.stringify(params.map((i) => ({ key: i.key, value: i.value }))));
    message.success('copy success🎉');
  };

  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
      `}
    >
      <br></br>
      <div>
        <Tooltip title='可从wmb云平台上对应【下载密钥tab】获取！'>
          <Button type='primary' style={{ marginTop: '8px' }}>
            如何获取密钥信息？
          </Button>
        </Tooltip>
      </div>

      <Form
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 80 }}
        layout='horizontal'
        form={form}
        // initialValues={TaskInfo}
        onFieldsChange={handleWmbChange}
        // disabled={componentDisabled}
        style={{ maxWidth: 1000, marginTop: 10 }}
      >
        <Form.Item label='subjectId' name='subject'>
          <InputNumber placeholder='client_id' style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label='key' name='key'>
          <Input placeholder='key' />
        </Form.Item>
        <Form.Item label='registry_server_ip' name='registry_server_ip'>
          <Input placeholder='registry_server_ip' />
        </Form.Item>
        <Form.Item label='registry_server_port' name='registry_server_port'>
          <InputNumber style={{ width: '100%' }} placeholder='registry_server_port' />
        </Form.Item>
      </Form>
    </div>
  );
};

export default RequestParameters;
