import { Variable } from 'postman-collection';

const publicSnippet = [
  //   {
  //     name: 'Send a request',
  //     text: `pm.sendRequest("https://postman-echo.com/get", function (err, response) {
  //     console.log(response.json());
  // });
  // `,
  //   },
  {
    name: 'Get an environment variable',
    text: `pm.environment.get("variable_key");
`,
  },
  {
    name: 'Set an environment variable',
    text: `pm.environment.set("variable_key", "variable_value");
`,
  },
];
export const preTestCodeSnippet = [
  {
    name: '替换消息体中指定分割位置的变量',
    text: `let delimiter = ',';  // 分隔符
let replacementMap = {
  "0": "firstValue", // 0表示替换第一个, firstValue表示要替换的值
  "2": "thridValue"
};
let str = pm.request.body.raw.msg;
let parts = str.split(delimiter);
for (let index in replacementMap) {
  if (replacementMap.hasOwnProperty(index)) {
    let n = parseInt(index, 10);
    if (n >= 0 && n < parts.length) {
      parts[n] = replacementMap[index];
    }
  }
}
pm.request.body.raw.msg = parts.join(delimiter);
`,
  },
  {
    name: '替换消息体中的占位符变量',
    text: `// 需要替换的变量在消息体中以 \${variableName} 表示
let replacements = {
  variableName1: "replace1",
  variableName2: "replace2"
};
let msg = pm.request.body.raw.msg;
for (let variableName in replacements) {
  if (replacements.hasOwnProperty(variableName)) {
    let regex = new RegExp('\\\\$\\\\{' + variableName + '\\\\}', 'g');
    msg = msg.replace(regex, replacements[variableName]);
  }
}
pm.request.body.raw.msg = msg;
`,
  },
  ...publicSnippet,
];

export const testCodeSnippet = [
  ...publicSnippet,
  {
    name: 'Response body: JSON value check',
    text: `pm.test("Your test name", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.value).to.eql(100);
  });
`,
  },
  //   {
  //     name: 'Response headers: Content-Type header check',
  //     text: `pm.test("Content-Type is present", function () {
  //     pm.response.to.have.header("Content-Type");
  // });
  // `,
  //   },
];
