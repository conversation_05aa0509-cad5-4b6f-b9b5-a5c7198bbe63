import { css, Theme, useArexCoreConfig } from '@arextest/arex-core';
import React, { FC, useEffect, useRef } from 'react';

interface StreamingRawRendererProps {
  body: any[];
  isComplete: boolean;
}

const createStyles = (theme: Theme) => ({
  container: css`
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    background-color: ${theme === Theme.dark ? '#1e1e1e' : '#fafafa'};
    font-family: 'IBMPlexMono', 'Courier New', monospace;
  `,
  streamingContent: css`
    background-color: ${theme === Theme.dark ? '#2d2d2d' : '#ffffff'};
    border: 1px solid ${theme === Theme.dark ? '#444' : '#e1e1e1'};
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  `,
  chunkHeader: css`
    color: ${theme === Theme.dark ? '#888' : '#666'};
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid ${theme === Theme.dark ? '#444' : '#e1e1e1'};
  `,
  chunkContent: css`
    color: ${theme === Theme.dark ? '#fff' : '#333'};
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 300px;
    overflow-y: auto;
    background-color: ${theme === Theme.dark ? '#1a1a1a' : '#f8f9fa'};
    padding: 12px;
    border-radius: 4px;
    border: 1px solid ${theme === Theme.dark ? '#333' : '#e9ecef'};
  `,
  streamingProgress: css`
    color: ${theme === Theme.dark ? '#888' : '#666'};
    font-style: italic;
    text-align: center;
    padding: 16px;
    background-color: ${theme === Theme.dark ? '#2d2d2d' : '#ffffff'};
    border: 1px dashed ${theme === Theme.dark ? '#444' : '#d1d5db'};
    border-radius: 8px;
    margin-top: 8px;
  `,
  errorContainer: css`
    color: #dc3545;
    padding: 16px;
    background-color: ${theme === Theme.dark ? '#2d1b1b' : '#fff5f5'};
    border: 1px solid ${theme === Theme.dark ? '#5c2626' : '#fecaca'};
    border-radius: 8px;
    margin-bottom: 16px;
  `,
  emptyState: css`
    text-align: center;
    color: ${theme === Theme.dark ? '#888' : '#666'};
    padding: 32px;
    background-color: ${theme === Theme.dark ? '#2d2d2d' : '#ffffff'};
    border: 1px dashed ${theme === Theme.dark ? '#444' : '#d1d5db'};
    border-radius: 8px;
  `,
});

const StreamingRawRenderer: FC<StreamingRawRendererProps> = ({ body, isComplete }) => {
  const { theme } = useArexCoreConfig();
  const styles = createStyles(theme);
  const containerRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [body]);

  if (!Array.isArray(body)) {
    return (
      <div ref={containerRef} css={styles.container}>
        <div css={styles.errorContainer}>
          ❌ 数据格式错误：Body 不是数组类型 - {String(body)}
        </div>
      </div>
    );
  }

  if (body.length === 0) {
    return (
      <div ref={containerRef} css={styles.container}>
        <div css={styles.emptyState}>
          📡 等待流式数据...
        </div>
        {!isComplete && (
          <div css={styles.streamingProgress}>
            🔄 正在接收数据流...
          </div>
        )}
      </div>
    );
  }

  return (
    <div ref={containerRef} css={styles.container}>
      {body.map((chunk, index) => {
        const chunkContent = typeof chunk === 'string' ? chunk : JSON.stringify(chunk, null, 2);
        
        return (
          <div key={`chunk-${index}`} css={styles.streamingContent}>
            <div css={styles.chunkHeader}>
              📦 Chunk {index + 1} ({chunkContent.length} 字符)
            </div>
            <div css={styles.chunkContent}>
              {chunkContent}
            </div>
          </div>
        );
      })}
      
      {!isComplete && (
        <div css={styles.streamingProgress}>
          🔄 流式传输进行中... ({body.length} 个数据块已接收)
        </div>
      )}
    </div>
  );
};

export default StreamingRawRenderer;