import { CopyOutlined } from '@ant-design/icons';
import {
  copyToClipboard,
  css,
  Segmented,
  SpaceBetweenWrapper,
  Theme,
  TooltipButton,
  tryPrettierJsonString,
  useArexCoreConfig,
} from '@arextest/arex-core';
import { Editor } from '@monaco-editor/react';
import { App, Button, Collapse, Input, Space } from 'antd';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ArexRESTResponse } from '../../types';
import MarkdownRenderer from './MarkdownRenderer';
import StreamingRawRenderer from './StreamingRawRenderer';

// 样式常量
const createStyles = (theme: Theme) => ({
  container: css`
    display: flex;
    flex-direction: column;
    height: 100%;
  `,
  contentContainer: css`
    flex: 1;
    overflow-y: auto;
  `,
  streamingContainer: css`
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    // background-color: ${theme === Theme.dark ? '#1e1e1e' : '#f5f5f5'};
  `,
  streamingProgress: css`
    color: ${theme === Theme.dark ? '#888' : '#666'};
    font-style: italic;
    margin-top: 16px;
    text-align: center;
  `,
  pathExtractor: css`
    margin-bottom: 16px;
    padding: 12px;
    background-color: ${theme === Theme.dark ? '#2d2d2d' : '#f9f9f9'};
    border-radius: 6px;
    border: 1px solid ${theme === Theme.dark ? '#444' : '#ddd'};
  `,
  pathExtractorTitle: css`
    margin-bottom: 8px;
    font-weight: 500;
    color: ${theme === Theme.dark ? '#fff' : '#333'};
  `,
  pathExtractorHint: css`
    margin-top: 8px;
    font-size: 12px;
    color: ${theme === Theme.dark ? '#888' : '#666'};
  `,
  previewContentContainer: css`
    border: 1px solid ${theme === Theme.dark ? '#444' : '#ddd'};
    border-radius: 6px;
    overflow: hidden;
  `,
  fallbackContent: css`
    padding: 16px;
    background-color: ${theme === Theme.dark ? '#1e1e1e' : '#fff'};
    color: ${theme === Theme.dark ? '#fff' : '#333'};
  `,
  errorContainer: {
    color: 'red',
    padding: '8px',
    backgroundColor: '#fff2f0',
    border: '1px solid #ffccc7',
    borderRadius: '4px',
  },
});

enum DisplayMode {
  'Pretty' = 'Pretty',
  'Raw' = 'Raw',
  'Preview' = 'Preview',
}

const ResponseBody: FC<{ response?: ArexRESTResponse }> = ({ response }) => {
  const { message } = App.useApp();
  const { t } = useTranslation();
  const { theme } = useArexCoreConfig();
  const styles = createStyles(theme);
  const streamingContainerRef = useRef<HTMLDivElement>(null);

  const [displayMode, setDisplayMode] = useState(DisplayMode.Pretty);
  const [jsonPath, setJsonPath] = useState('');
  const [extractedContent, setExtractedContent] = useState<
    Array<{ path: string; content: string }>
  >([]);

  // localStorage key for JSON path
  const JSON_PATH_STORAGE_KEY = 'arex-json-path-extractor';

  // 从 localStorage 读取保存的 JSON 路径
  useEffect(() => {
    const savedPath = localStorage.getItem(JSON_PATH_STORAGE_KEY);
    if (savedPath) {
      setJsonPath(savedPath);
    }
  }, []);

  const bodyValue = useMemo(() => {
    // 处理response为null或undefined的情况
    if (!response) {
      return 'No data available';
    }

    if (response.type === 'success' || response.type === 'fail') {
      // 处理body为null或undefined的情况
      if (response.body === null || response.body === undefined) {
        return 'No response body available';
      }
      return displayMode === DisplayMode.Pretty
        ? tryPrettierJsonString(response.body)
        : response.body;
    } else if (response.type === 'streaming') {
      // 确保response.body存在且是数组
      if (!response.body) {
        return 'No streaming data available';
      }
      const streamingBody = Array.isArray(response.body) ? response.body.join('') : response.body;
      // 当streaming body为空时，返回默认内容而不是空字符串
      const finalBody = streamingBody || 'No streaming data available';
      return displayMode === DisplayMode.Pretty ? tryPrettierJsonString(finalBody) : finalBody;
    }
    // 当response类型未知时，返回默认内容
    return 'Unknown response type';
  }, [response, displayMode]);

  // 提取JSON路径内容的函数
  const extractJsonPath = (jsonData: any, path: string): string => {
    if (!path.trim()) return JSON.stringify(jsonData, null, 2);

    try {
      const pathArray = path.split('.').filter((p) => p.trim());
      let current = jsonData;

      for (const key of pathArray) {
        if (current === null || current === undefined) {
          return `路径 "${path}" 不存在`;
        }

        // 处理数组索引
        if (key.includes('[') && key.includes(']')) {
          const arrayKey = key.substring(0, key.indexOf('['));
          const indexStr = key.substring(key.indexOf('[') + 1, key.indexOf(']'));
          const index = parseInt(indexStr);

          if (arrayKey) {
            current = current[arrayKey];
          }

          if (Array.isArray(current) && !isNaN(index)) {
            current = current[index];
          } else {
            return `路径 "${path}" 不是有效的数组索引`;
          }
        } else {
          current = current[key];
        }
      }

      if (current === null || current === undefined) {
        return '';
      } else if (typeof current === 'string') {
        return current;
      } else {
        return JSON.stringify(current, null, 2);
      }
    } catch (error: any) {
      return `提取路径时出错: ${error.message}`;
    }
  };

  // 处理多个路径的提取
  const extractMultiplePaths = (
    jsonData: any,
    pathsString: string,
  ): Array<{ path: string; content: string }> => {
    const paths = pathsString
      .split('&&')
      .map((p) => p.trim())
      .filter((p) => p);
    return paths.map((path) => ({
      path,
      content: extractJsonPath(jsonData, path),
    }));
  };

  // 处理路径提取
  const handleExtractPath = () => {
    // 保存路径到 localStorage
    if (jsonPath.trim()) {
      localStorage.setItem(JSON_PATH_STORAGE_KEY, jsonPath.trim());
    }

    try {
      // console.log('Extracting path:', jsonPath);
      // console.log('response.body:', response?.body);
      // console.log('response.type:', response?.type);

      if (response?.type === 'success' || response?.type === 'fail') {
        // 普通模式：处理单个JSON对象
        const jsonData =
          typeof response.body === 'string' ? JSON.parse(response.body) : response.body;
        const results = extractMultiplePaths(jsonData, jsonPath);
        setExtractedContent(results);
      } else if (response?.type === 'streaming' && Array.isArray(response.body)) {
        // Streaming模式：遍历所有chunk，提取路径内容并拼接
        const paths = jsonPath
          .split('&&')
          .map((p) => p.trim())
          .filter((p) => p);
        const pathResults: Array<{ path: string; content: string }> = paths.map((path) => ({
          path,
          content: '',
        }));

        for (const chunk of response.body) {
          try {
            const chunkData = typeof chunk === 'string' ? JSON.parse(chunk) : chunk;

            paths.forEach((path, index) => {
              const extractedValue = extractJsonPath(chunkData, path);

              // 只有当提取到有效内容时才添加到结果中（包括空字符串，但排除错误信息）
              if (
                extractedValue !== null &&
                extractedValue !== undefined &&
                !extractedValue.startsWith('路径') &&
                !extractedValue.startsWith('提取路径时出错')
              ) {
                // 如果提取的是字符串，直接使用；如果是JSON，需要解析
                let valueToAdd = extractedValue;
                try {
                  // 尝试解析JSON字符串，如果是简单字符串则直接使用
                  const parsed = JSON.parse(extractedValue);
                  if (typeof parsed === 'string') {
                    valueToAdd = parsed;
                  } else if (parsed === null) {
                    valueToAdd = '';
                  }
                } catch {
                  // 如果不是JSON格式，直接使用原值
                }
                pathResults[index].content += valueToAdd;
              }
            });
          } catch (chunkError) {
            console.warn('Failed to parse chunk:', chunk, chunkError);
          }
        }

        // 过滤掉没有内容的路径结果
        const validResults = pathResults.filter((result) => result.content.trim() !== '');
        if (validResults.length === 0) {
          setExtractedContent([
            { path: jsonPath, content: `未能从路径 "${jsonPath}" 提取到有效内容` },
          ]);
        } else {
          setExtractedContent(validResults);
        }
      } else {
        message.error('当前响应不是有效的JSON格式');
        return;
      }
    } catch (error: any) {
      message.error('JSON解析失败，请确保响应是有效的JSON格式');
      setExtractedContent([{ path: jsonPath, content: 'JSON解析失败' }]);
    }
  };

  // 当切换到Preview模式时，如果有路径则自动提取
  useEffect(() => {
    if (displayMode === DisplayMode.Preview && jsonPath) {
      handleExtractPath();
    }
  }, [displayMode, response]);

  // 在streaming + Pretty模式下自动滚动到底部
  useEffect(() => {
    if (
      response?.type === 'streaming' &&
      displayMode === DisplayMode.Pretty &&
      streamingContainerRef.current
    ) {
      streamingContainerRef.current.scrollTop = streamingContainerRef.current.scrollHeight;
    }
  }, [response?.body, displayMode]);

  return (
    <div css={styles.container}>
      <SpaceBetweenWrapper style={{ margin: '4px' }}>
        <Segmented
          size='small'
          value={displayMode}
          options={['Pretty', 'Raw', 'Preview']}
          onChange={(value) => setDisplayMode(value as DisplayMode)}
        />
        <TooltipButton
          placement='left'
          color='primary'
          title={t('action.copy')}
          icon={<CopyOutlined />}
          onClick={() => {
            copyToClipboard(bodyValue || '');
            message.success('copy success');
          }}
        />
      </SpaceBetweenWrapper>

      <div css={styles.contentContainer}>
        {response?.type === 'streaming' ? (
          displayMode === DisplayMode.Raw ? (
            <StreamingRawRenderer body={response.body} isComplete={response.isComplete ?? false} />
          ) : displayMode === DisplayMode.Preview ? (
            <div css={styles.streamingContainer}>
              {/* JSON路径选择器 */}
              <div css={styles.pathExtractor}>
                <div css={styles.pathExtractorTitle}>JSON路径提取器</div>
                <Space.Compact style={{ width: '100%' }}>
                  <Input
                    placeholder='输入JSON路径，例如: data.items[0].name，多个路径用&&分割'
                    value={jsonPath}
                    onChange={(e) => setJsonPath(e.target.value)}
                    onPressEnter={handleExtractPath}
                  />
                  <Button type='primary' onClick={handleExtractPath}>
                    提取
                  </Button>
                </Space.Compact>
                <div css={styles.pathExtractorHint}>
                  支持对象属性（data.name）和数组索引（items[0]），多个路径用&&分割（例如：data.name
                  && data.items[0].title）
                </div>
              </div>

              {/* 渲染内容 */}
              <div css={styles.previewContentContainer}>
                {extractedContent.length > 0 ? (
                  extractedContent.map((result, index) => (
                    <div
                      key={index}
                      style={{ marginBottom: index < extractedContent.length - 1 ? '16px' : '0' }}
                    >
                      {extractedContent.length > 1 && (
                        <div
                          style={{
                            padding: '8px 12px',
                            backgroundColor: theme === Theme.dark ? '#2d2d2d' : '#f0f0f0',
                            borderRadius: '4px 4px 0 0',
                            fontSize: '12px',
                            fontWeight: 500,
                            color: theme === Theme.dark ? '#888' : '#666',
                            borderBottom: `1px solid ${theme === Theme.dark ? '#444' : '#ddd'}`,
                          }}
                        >
                          路径: {result.path}
                        </div>
                      )}
                      <MarkdownRenderer
                        content={result.content}
                        style={
                          extractedContent.length > 1 ? { borderRadius: '0 0 4px 4px' } : undefined
                        }
                      />
                    </div>
                  ))
                ) : (
                  <div css={styles.fallbackContent}>
                    {Array.isArray(response.body) && response.body.length > 0
                      ? JSON.stringify(response.body, null, 2)
                      : 'No streaming data available'}
                  </div>
                )}
              </div>
              {!response.isComplete && (
                <div css={styles.streamingProgress}>Streaming in progress...</div>
              )}
            </div>
          ) : (
            <div ref={streamingContainerRef} css={styles.streamingContainer}>
              {Array.isArray(response.body) && response.body.length > 0 ? (
                <Collapse
                  size='small'
                  items={response.body.map((chunk, index) => ({
                    key: `chunk-${index}`,
                    label: `Chunk ${index}`,
                    children: (
                      <Editor
                        language='json'
                        theme={theme === Theme.dark ? 'vs-dark' : 'light'}
                        value={
                          typeof chunk === 'string'
                            ? tryPrettierJsonString(chunk)
                            : JSON.stringify(chunk, null, 2)
                        }
                        options={{
                          readOnly: true,
                          minimap: { enabled: false },
                          scrollBeyondLastLine: false,
                          wordWrap: 'on',
                          lineNumbers: 'on',
                          folding: true,
                          automaticLayout: true,
                          scrollbar: {
                            alwaysConsumeMouseWheel: false,
                          },
                        }}
                        height='200px'
                      />
                    ),
                  }))}
                />
              ) : (
                <div css={styles.fallbackContent}>
                  {Array.isArray(response.body)
                    ? 'No streaming chunks available'
                    : `❌ Body is not an array: ${String(response.body)}`}
                </div>
              )}
              {!response.isComplete && (
                <div css={styles.streamingProgress}>Streaming in progress...</div>
              )}
            </div>
          )
        ) : displayMode !== DisplayMode.Preview ? (
          bodyValue && typeof bodyValue === 'string' && bodyValue.trim() ? (
            <Editor
              language={displayMode === DisplayMode.Pretty ? 'json' : 'text'}
              theme={theme === Theme.dark ? 'vs-dark' : 'light'}
              value={bodyValue}
              options={{
                minimap: {
                  enabled: false,
                },
                fontSize: 12,
                wordWrap: 'on',
                automaticLayout: true,
                fontFamily: 'IBMPlexMono, "Courier New", monospace',
                scrollBeyondLastLine: false,
                readOnly: true,
              }}
            />
          ) : (
            <div css={styles.fallbackContent}>No data available</div>
          )
        ) : (
          <div
            css={styles.fallbackContent}
            dangerouslySetInnerHTML={{ __html: bodyValue || 'No data available' }}
          />
        )}
      </div>
    </div>
  );
};

export default ResponseBody;
