import { css, Theme, useArexCoreConfig } from '@arextest/arex-core';
import { marked } from 'marked';
import React, { FC } from 'react';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
}

const MarkdownRenderer: FC<MarkdownRendererProps> = ({ content, className, style }) => {
  const { theme } = useArexCoreConfig();

  return (
    <div
      className={className}
      style={style}
      css={css`
        padding: 16px;
        background-color: ${theme === Theme.dark ? '#1e1e1e' : '#fff'};
        color: ${theme === Theme.dark ? '#fff' : '#333'};
        text-align: left;
        white-space: pre-wrap;

        /* Markdown样式 */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin: 0.2em 0 0 0;
          color: ${theme === Theme.dark ? '#fff' : '#333'};
        }

        p {
          margin: 0;
          line-height: 1.2;
        }

        code {
          background-color: ${theme === Theme.dark ? '#2d2d2d' : '#f1f1f1'};
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'IBMPlexMono', 'Courier New', monospace;
        }

        pre {
          background-color: ${theme === Theme.dark ? '#2d2d2d' : '#f8f8f8'};
          padding: 12px;
          border-radius: 6px;
          overflow-x: auto;
          border: 1px solid ${theme === Theme.dark ? '#444' : '#ddd'};
        }

        pre code {
          background: none;
          padding: 0;
        }

        blockquote {
          border-left: 4px solid ${theme === Theme.dark ? '#555' : '#ddd'};
          margin: 0;
          padding-left: 1em;
          color: ${theme === Theme.dark ? '#ccc' : '#666'};
        }

        ul,
        ol {
          margin: 0;
          padding-left: 2em;
        }

        li {
          margin-bottom: 0;
        }

        table {
          border-collapse: collapse;
          width: 100%;
          margin: 0;
        }

        th,
        td {
          border: 1px solid ${theme === Theme.dark ? '#444' : '#ddd'};
          padding: 8px 12px;
          text-align: left;
        }

        th {
          background-color: ${theme === Theme.dark ? '#2d2d2d' : '#f8f8f8'};
          font-weight: 600;
        }
      `}
      dangerouslySetInnerHTML={{
        __html: marked(content, {
          breaks: false,
          gfm: true,
        }),
      }}
    />
  );
};

export default MarkdownRenderer;
