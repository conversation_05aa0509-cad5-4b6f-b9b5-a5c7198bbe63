import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  css,
  getLocalStorage,
  styled,
  Theme,
  useArexCoreConfig,
  useTranslation,
} from '@arextest/arex-core';
import { Editor } from '@monaco-editor/react';
import { Button, Tooltip, Typography } from 'antd';
import React from 'react';

import { useArexRequestStore } from '../../../hooks';
import { testCodeSnippet } from './snippets';

const { Text } = Typography;
const RequestTestHeader = styled.div`
  display: flex;
  justify-content: space-between;
  & > span:first-of-type {
    font-size: 13px;
    line-height: 32px;
    font-weight: 500;
    color: #9d9d9d;
  }
`;

const RequestTestWrapper = styled.div`
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  justify-content: space-between;
  flex: 1;
  & > div:last-of-type {
    width: 35%;
    text-align: left;
    //border-left: 1px solid #eee;
    padding-left: 20px;
  }
`;

const editorOptions = {
  minimap: {
    enabled: false,
  },
  fontSize: 12,
  wordWrap: 'on',
  automaticLayout: true,
  fontFamily: 'IBMPlexMono, "Courier New", monospace',
  scrollBeyondLastLine: false,
} as const;

const RequestTests = () => {
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();
  const { theme } = useArexCoreConfig();
  // 添加状态来跟踪连续空格的次数和上一次输入的字符
  const [spaceCount, setSpaceCount] = React.useState(0);
  const [lastChar, setLastChar] = React.useState('');
  // 添加等待状态
  const [isWaiting, setIsWaiting] = React.useState(false);
  // 添加流式响应状态
  const [streamingMessage, setStreamingMessage] = React.useState('');
  // 添加生成结果状态
  const [generatedResult, setGeneratedResult] = React.useState('');
  // 添加内容区域的引用
  const streamingContentRef = React.useRef<HTMLDivElement>(null);
  const resultContentRef = React.useRef<HTMLDivElement>(null);

  // 添加自动滚动效果
  React.useEffect(() => {
    if (streamingContentRef.current && isWaiting) {
      streamingContentRef.current.scrollTop = streamingContentRef.current.scrollHeight;
    }
  }, [streamingMessage, isWaiting]);

  React.useEffect(() => {
    if (resultContentRef.current && generatedResult) {
      resultContentRef.current.scrollTop = resultContentRef.current.scrollHeight;
    }
  }, [generatedResult]);

  const ThemeColorPrimaryButton = styled(Button)`
    color: ${(props) => props.theme.colorPrimary} !important;
  `;
  const codeSnippet = testCodeSnippet;

  const addTest = (text: string) => {
    dispatch((state) => {
      state.request.testScript = state.request.testScript += text;
    });
  };

  // 将生成的内容填入编辑器
  const applyGeneratedContent = () => {
    if (generatedResult.trim()) {
      dispatch((state) => {
        // 获取当前编辑器内容
        const currentScript = state.request.testScript || '';
        // 将当前内容的每一行注释掉
        const commentedScript = currentScript
          .split('\n')
          .map((line) => `// ${line}`)
          .join('\n');

        // 替换所有的pm.response.json()为pm.response.json().result
        // let processedResult = generatedResult;
        // processedResult = processedResult.replace(
        //   /pm\.response\.json\(\)/g,
        //   'pm.response.json().result',
        // );

        // 组合新的内容：注释掉的旧内容 + 换行 + 新生成的内容
        const newScript = `${commentedScript}\n${generatedResult}`;

        state.request.testScript = newScript;
      });
      setGeneratedResult(''); // 清空已采纳的生成结果
    }
  };

  // 发送消息到AI服务
  const sendToAI = async (content: string) => {
    if (!content.trim() || isWaiting) return;

    setIsWaiting(true);
    setStreamingMessage('');
    setGeneratedResult('');

    try {
      // 构建请求参数
      const params = {
        oa: getLocalStorage('email') as string,
        message: content,
        moduleName: 'POSTMAN_SCRIPT',
        serviceName: 'wreplay-copilot',
        triggerUrl: window.location.href,
        isHidden: false,
      };

      const response = await fetch('/ai/ai-assistant/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'text/event-stream',
        },
        body: JSON.stringify(params),
      });

      if (!response || !response.ok || !response.body) {
        throw new Error('Invalid response');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let completeResponse = '';
      let buffer = '';
      let responseId = '';

      let isReading = true;
      while (isReading) {
        const { done, value } = await reader.read();

        if (done) {
          isReading = false;

          // 处理缓冲区中可能剩余的数据
          if (buffer.trim()) {
            try {
              if (buffer.includes('data:')) {
                const dataContent = buffer.substring(buffer.indexOf('data:') + 5).trim();
                if (dataContent) {
                  try {
                    const jsonData = JSON.parse(dataContent);

                    // 尝试从响应中提取ID
                    if (jsonData.data?.id && !responseId) {
                      responseId = jsonData.data.id;
                    }

                    // 只提取需要的内容
                    if (jsonData.content) {
                      completeResponse += jsonData.content;
                    } else if (jsonData.data?.streamResp) {
                      const processedContent = jsonData.data.streamResp.replace(/\\n/g, '\n');
                      completeResponse += processedContent;
                    } else if (jsonData.data?.content) {
                      completeResponse += jsonData.data.content;
                    }
                    setStreamingMessage(completeResponse);
                  } catch (e) {
                    console.warn('解析最终缓冲区JSON失败:', e);
                  }
                }
              }
            } catch (e) {
              console.warn('处理最终缓冲区失败:', e);
            }
          }
          break;
        }

        // 解码当前数据块
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理完整的数据行
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim().startsWith('data:')) {
            try {
              // 提取data后的内容
              const dataContent = line.substring(line.indexOf('data:') + 5).trim();

              if (dataContent) {
                try {
                  // 尝试解析JSON
                  const jsonData = JSON.parse(dataContent);

                  // 尝试从响应中提取ID
                  if (jsonData.data?.id && !responseId) {
                    responseId = jsonData.data.id;
                  }

                  // 只提取需要的内容字段
                  if (jsonData.content) {
                    const newContent = jsonData.content;
                    if (typeof newContent === 'string') {
                      completeResponse += newContent;
                    }
                  } else if (jsonData.data?.streamResp) {
                    const processedContent = jsonData.data.streamResp.replace(/\\n/g, '\n');
                    completeResponse += processedContent;
                  } else if (jsonData.data?.content) {
                    const newContent = jsonData.data.content;
                    if (typeof newContent === 'string') {
                      completeResponse += newContent;
                    }
                  }

                  setStreamingMessage(completeResponse);
                } catch (e) {
                  console.warn('JSON parsing error:', e);
                  if (dataContent.startsWith('"') && dataContent.endsWith('"')) {
                    try {
                      const textContent = JSON.parse(dataContent);
                      if (typeof textContent === 'string') {
                        completeResponse += textContent;
                        setStreamingMessage(completeResponse);
                      }
                    } catch {
                      // 解析失败，忽略
                    }
                  }
                }
              }
            } catch (e) {
              console.warn('Error processing data line:', e);
            }
          } else if (line.trim() && !line.includes('{') && !line.includes('}')) {
            // 只处理不包含JSON结构的纯文本行
            completeResponse += line + '\n';
            setStreamingMessage(completeResponse);
          }
        }
      }

      // 确保消息不为空再保存生成结果
      if (completeResponse.trim()) {
        // 不再直接更新编辑器内容，而是保存到状态中
        setGeneratedResult(completeResponse);
      } else {
        console.warn('Empty response, not updating editor');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // 显示错误信息
      setGeneratedResult('// 抱歉，生成代码时发生错误，请稍后再试。');
    } finally {
      setIsWaiting(false);
    }
  };

  // 添加处理连续空格的函数
  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      const currentChar = value.length > 0 ? value[value.length - 1] : '';

      // 更新空格计数逻辑
      let newSpaceCount = spaceCount;
      if (currentChar === ' ') {
        newSpaceCount = lastChar === ' ' ? spaceCount + 1 : 1;
      } else {
        newSpaceCount = 0;
      }

      // 检测到连续三次空格
      if (newSpaceCount === 3 && currentChar === ' ' && !isWaiting) {
        // 移除连续的三个空格
        const newValue = value.slice(0, -3) + '';

        // 更新编辑器内容（移除空格）
        dispatch((state) => {
          state.request.testScript = newValue;
        });

        // 重置空格计数
        setSpaceCount(0);

        // 只有当内容不为空时才发送请求
        if (newValue.trim()) {
          // 设置等待状态
          setIsWaiting(true);

          // 发送当前编辑器内容到AI服务
          setTimeout(() => {
            sendToAI(newValue);
          }, 0);
        }

        return;
      }

      // 更新状态
      setSpaceCount(newSpaceCount);
      setLastChar(currentChar);

      dispatch((state) => {
        state.request.testScript = value;
      });
    }
  };

  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
      `}
    >
      <RequestTestHeader>
        <span>{t('preRequest.javascript_code')}</span>
        <div
          css={css`
            display: flex;
            align-items: center;
            gap: 8px;
          `}
        >
          <Button
            size='small'
            type='primary'
            onClick={() => {
              const currentScript = store.request.testScript;
              if (currentScript?.trim() && !isWaiting) {
                setIsWaiting(true);
                sendToAI(currentScript);
              }
            }}
          >
            AI生成测试脚本
          </Button>
          <Tooltip title='使用自然语言输入您的测试内容，点击自动生成测试脚本'>
            <QuestionCircleOutlined style={{ color: '#9d9d9d', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      </RequestTestHeader>

      {/* 添加等待效果 */}
      {isWaiting && (
        <div
          css={css`
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
          `}
        >
          <div
            css={css`
              background-color: white;
              padding: 24px;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              max-width: 90%;
              max-height: 90%;
              width: 800px;
              height: 600px;
              display: flex;
              flex-direction: column;
            `}
          >
            <Typography.Text strong>正在生成代码片段...</Typography.Text>
            <div
              ref={streamingContentRef}
              css={css`
                margin-top: 16px;
                flex: 1;
                overflow-y: auto;
                white-space: pre-wrap;
                font-family: monospace;
                font-size: 12px;
                background-color: #f5f5f5;
                padding: 16px;
                border-radius: 4px;
                margin-bottom: 16px; /* 减小底部边距，从24px改为16px */
                max-height: calc(100% - 90px); /* 增加内容区域高度，从120px改为90px */
                display: block;
                position: relative;
              `}
            >
              {streamingMessage}
            </div>
            {/* 添加一个空的占位区域，与按钮区域高度一致 */}
            <div
              css={css`
                height: 40px; /* 减小占位区域高度，从56px改为40px */
              `}
            ></div>
          </div>
        </div>
      )}

      {/* 添加生成结果和应用按钮 */}
      {!isWaiting && generatedResult && (
        <div
          css={css`
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
          `}
        >
          <div
            css={css`
              background-color: white;
              padding: 24px;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              max-width: 90%;
              max-height: 90%;
              width: 800px;
              height: 600px;
              display: flex;
              flex-direction: column;
            `}
          >
            <Typography.Text strong>AI已生成代码片段</Typography.Text>
            <div
              ref={resultContentRef}
              css={css`
                margin-top: 16px;
                flex: 1;
                overflow-y: auto;
                white-space: pre-wrap;
                font-family: monospace;
                font-size: 12px;
                background-color: #f5f5f5;
                padding: 16px;
                border-radius: 4px;
                max-height: calc(100% - 90px); /* 增加内容区域高度，从120px改为90px */
                display: block;
                position: relative;
              `}
            >
              {generatedResult}
            </div>
            <div
              css={css`
                margin-top: 16px; /* 减小上边距，从24px改为16px */
                display: flex;
                justify-content: space-between;
              `}
            >
              <Button onClick={() => setGeneratedResult('')}>取消</Button>
              <Button type='primary' onClick={applyGeneratedContent}>
                采纳
              </Button>
            </div>
          </div>
        </div>
      )}

      <RequestTestWrapper>
        <div
          css={css`
            min-width: 0;
            flex: 1;
            //width: 100%;
          `}
        >
          <Editor
            theme={theme === Theme.dark ? 'vs-dark' : 'light'}
            options={editorOptions}
            language={'javascript'}
            value={store.request.testScript}
            onChange={handleEditorChange}
          />
        </div>

        <div
          css={css`
            display: flex;
            flex-direction: column;
          `}
        >
          <Text
            type={'secondary'}
            css={css`
              margin-bottom: 4px;
            `}
          >
            Test scripts are written in JavaScript, and are run after the response is received.
          </Text>
          <div>
            <a
              type='text'
              onClick={() =>
                window.open('https://learning.postman.com/docs/writing-scripts/test-scripts/')
              }
              style={{ marginLeft: '8px' }}
            >
              Read documentation
            </a>
          </div>
          <Text
            type={'secondary'}
            css={css`
              padding: 16px 0;
            `}
          >
            Snippets
          </Text>
          <div
            css={css`
              overflow: auto;
              flex: 1;
            `}
          >
            {codeSnippet.map((e, i) => (
              <ThemeColorPrimaryButton
                key={i}
                size='small'
                type='text'
                onClick={() => addTest(e.text)}
              >
                {e.name}
              </ThemeColorPrimaryButton>
            ))}
          </div>
        </div>
      </RequestTestWrapper>
    </div>
  );
};

export default RequestTests;
