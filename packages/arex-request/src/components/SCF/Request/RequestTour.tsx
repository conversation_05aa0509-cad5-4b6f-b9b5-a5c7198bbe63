import { Tour, TourProps } from 'antd';
import React, { FC, useEffect, useState } from 'react';

// 从原组件中提取的辅助函数
const useElementBySelector = (selector: string) => {
  const [element, setElement] = useState<HTMLElement | null>(null);

  useEffect(() => {
    const node = document.querySelector(selector);
    if (node) {
      setElement(node as HTMLElement);
    }

    // 返回一个清理函数，当组件卸载时移除事件监听器（这里为空）
    return () => { };
  }, [selector]);

  return element;
};

const isFirstVisit = () => {
  const visitedKey = 'hasRequestTour';
  let isFirst = true;

  if (typeof Storage !== 'undefined') {
    // 检查localStorage是否存在
    if (localStorage.getItem(visitedKey)) {
      // 已有记录，不是首次访问
      isFirst = false;
    } else {
      // 设置首次访问标志
      localStorage.setItem(visitedKey, 'true');
    }
  }
  return isFirst;
};

interface RequestTourProps {
  // 可以添加自定义属性，如果需要的话
}

const RequestTour: FC<RequestTourProps> = () => {
  const [open, setOpen] = useState(false);

  // 获取DOM元素引用
  const avatarBtn = useElementBySelector('#avatar');
  const protocalSelectBtn = useElementBySelector('#protocal-select');
  const collectTree = useElementBySelector('#collectTree');
  const importBtn = useElementBySelector('#arex-import-btn');
  const detailBtn = useElementBySelector('.detail');
  const historyBtn = useElementBySelector('.history');

  // Tour步骤定义
  const steps: TourProps['steps'] = [
    {
      title: '为了您能够有更好地使用体验，请遵循以下步骤安装我们的Chrome插件',
      description:
        '1. 首先，请访问以下链接以下载所需的Chrome插件安装包： https://wosin2.58corp.com/QSOnMlKjIQv/tegyunuploadfile/21973a3c56301eaf02ce7e520c0c6ff2\r\n' +
        '下载完成后，请解压缩该文件夹以获取Chrome插件安装文件。\r\n',
      placement: 'center',
      // cover: (
      //   <img
      //     alt="tour.png"
      //     src="https://pages.anjukestatic.com/fe/esf/img/e8e6f6af/arex_1.png"
      //   />
      // ),
    },
    {
      title: '安装插件',
      description:
        '2. 接下来，启动您的Google Chrome浏览器，并输入"chrome://extensions/"进入扩展程序管理页面。\r\n' +
        '并在扩展程序管理页面中，启用"开发者模式"（如果尚未启用）。\r\n',
      placement: 'center',
      // cover: (
      //   <img
      //     alt="tour.png"
      //     src="https://pages.anjukestatic.com/fe/esf/img/30a603f9/arex_2.png"
      //   />
      // ),
    },
    {
      title: '安装插件',
      description:
        '3. 将解压得到的插件文件直接拖拽到此页面进行安装，或者点击"加载已解压的扩展程序"按钮并选择相应文件夹。完成上述操作后，插件将会自动安装并在Chrome浏览器中启用。\r\n',
      placement: 'center',
      // cover: (
      //   <img
      //     alt="tour.png"
      //     src="https://pages.anjukestatic.com/fe/esf/img/c059111a/arex_3.png"
      //   />
      // ),
    },
    {
      title: '个人设置',
      description: '可以在这里进行个人设置',
      target: () => avatarBtn as HTMLElement,
      placement: 'bottom',
    },
    {
      title: '用例管理',
      description: '可以在这里像使用PostMan一样对接口进行维护',
      target: () => collectTree as HTMLElement,
      placement: 'right',
    },
    {
      title: '导入导出',
      description: '可以在这里快速导入导出，支持postman、收藏夹、curl',
      target: () => importBtn as HTMLElement,
      placement: 'right',
    },
    {
      title: '选择协议',
      description: '可在此切换协议, 当前支持SCF、HTTP、WMB',
      target: () => protocalSelectBtn as HTMLElement,
      placement: 'right',
    },
    {
      title: '查看接口详情',
      description: '可在此查看接口详情',
      target: () => detailBtn as HTMLElement,
      placement: 'top',
    },
    {
      title: '查看历史调用',
      description: '可在此查看历史调用',
      target: () => historyBtn as HTMLElement,
      placement: 'top',
    },
  ];

  // 组件挂载时检查是否是首次访问
  useEffect(() => {
    const s = isFirstVisit();
    setOpen(s);
  }, []);

  return <Tour open={open} onClose={() => setOpen(false)} steps={steps} />;
};

export default RequestTour;
