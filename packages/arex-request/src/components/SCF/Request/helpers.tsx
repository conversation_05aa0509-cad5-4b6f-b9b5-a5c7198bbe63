import React from 'react';

// Helper function to render item for AutoComplete
const renderItem = (value: string, key: string, parent: string) => ({
  key,
  value: key as string, // Use scfID as the value for selection
  parent,
  functionName: value,
  label: (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
      }}
    >
      {value}
    </div>
  ),
});

// Function to transform function data into options for AutoComplete
export function getFunctionOptions(data: {
  [s: string]: Array<{ scf_function_name: string; scfID: string }>;
}) {
  if (!data) return [];
  return Object.entries(data).map(([label, item]) => {
    return {
      label,
      options: item.map((value: { scf_function_name: string; scfID: string }) => {
        return renderItem(value.scf_function_name, value.scfID, label);
      }),
    };
  });
}
