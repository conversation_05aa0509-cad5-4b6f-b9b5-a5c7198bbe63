import { ExclamationOutlined, SendOutlined } from '@ant-design/icons';
import { getLocalStorage, styled, useTranslation } from '@arextest/arex-core';
import { AutoComplete, Button, Divider, Input, Select } from 'antd';
import jsonBigInt from 'json-bigint';
import React, { FC, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { addUserAction, sendSCFRequest, UserActionEnum } from '../../../helpers';
import {
  useArexRequestProps,
  useArexRequestStore,
  useInterfaceNameHandler,
  useIpHandler,
  useServiceNameHandler,
} from '../../../hooks';
import { get } from '../../../service/scfInfo';
import { ArexEnvironment, ArexRESTRequest, ArexRESTResponse } from '../../../types';
import { EnvironmentSelectProps } from '../../NavigationBar/EnvironmentSelect';
import { InfoSummaryProps } from '../../NavigationBar/InfoSummary';
import { useParamsExtractor } from '../../Request/hooks/useParamsExtractor';
import InterfaceDetail, {
  ChildComponentProps,
} from '../../Request/InterfaceDetail/InterfaceDetail';
import InterfaceHistoryList, {
  ChildComponentPropsHis,
} from '../../Request/InterfaceHistory/InterfaceHistoryList';
import SCFRequestOptions from './RequestOptions';
import RequestTour from './RequestTour';

declare global {
  interface Window {
    __AREX_EXTENSION_INSTALLED__: boolean;
  }
}

const HeaderWrapper = styled.div`
  padding: 0 8px;
  display: flex;
`;

export type AutoCompLabel = {
  key: string;
  value: string;
  clusterName?: string;
  functionName?: string;
};

export type RequestProps = {
  disableSave?: boolean;
  onBeforeRequest?: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest;
  onRequest?: (
    reqData: { request: ArexRESTRequest; environment?: ArexEnvironment },
    resData: Awaited<ReturnType<typeof sendSCFRequest>>,
  ) => void;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
  onSaveAs?: () => void;
  navPane?: any;
} & InfoSummaryProps & {
    environmentProps?: EnvironmentSelectProps;
  };

const getUserEmail = () => {
  const email: string = getLocalStorage('email') || '';
  return email.toLowerCase().startsWith('guest') ? 'qinsisheng' : email;
};

type EnvCode = '0' | '1' | '2' | '3' | '4';

const Request: FC<RequestProps> = (props: any) => {
  const { onBeforeRequest = (request: ArexRESTRequest) => request, onRequest } =
    useArexRequestProps();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();

  const { extractParamsFromBody } = useParamsExtractor();

  const [interfaceDetailParam, setInterfaceDetailParam] = useState<ChildComponentProps>({
    scfID: '',
  });
  const [searchParams] = useSearchParams();
  const scfParam = searchParams.get('scf');
  const isScf = scfParam !== null && scfParam === 'true';
  const [openDetail, updateOpenDetail] = useState<boolean>(false);

  const [interfaceNameValue, setInterfaceNameValue] = useState<string | undefined>(
    store.request?.interfaceNameValue,
  );
  const [functionNameValue, setFunctionNameValue] = useState<string>();
  const [newFunctionOptions, setNewFunctionOptions] = useState<any[]>([]);
  const [originFunctionOptions, setOriginFunctionOptions] = useState<any[]>([]);
  const [loadedFromHistory, setLoadedFromHistory] = useState(false);
  const operatorUser = getUserEmail();

  const [interfaceNameOptions, setInterfaceNameOptions] = useState<AutoCompLabel[]>([]);

  const hisProps = {
    scfId: `0`,
    navPane: props.navPane,
  };

  const [InterfaceHistoryListParam, setInterfaceHistoryListParam] =
    useState<ChildComponentPropsHis>({ hisProps });

  const { setOriginIpData, ipOptions, setIpOptions, onEnvChange, onIpChange, handleIpSearch } = useIpHandler();

  const {
    serviceNameOptions,
    serviceNameValue,
    serviceID,
    clusterName,
    selectServiceName,
    setServiceNameValue,
    setServiceID,
    setClusterName,
    isSelectServiceName,
    handleSearchServiceName,
    handleServiceNameSelect,
  } = useServiceNameHandler(
    setInterfaceNameOptions,
    setNewFunctionOptions,
    setOriginFunctionOptions,
    setOriginIpData,
    store.request?.serviceNameValue,
    setInterfaceNameValue,
    setFunctionNameValue,
  );

  const {
    selectInterfaceName,
    isSelectInterfaceName,
    handleInterfaceNameSelect,
    handleFunctionSearch,
    handleFunctionNameSelect,
    handleFilterOption,
  } = useInterfaceNameHandler(
    serviceID,
    interfaceNameOptions,
    setInterfaceNameOptions,
    setNewFunctionOptions,
    setOriginFunctionOptions,
    setFunctionNameValue,
    interfaceNameValue,
    setInterfaceNameValue,
    operatorUser,
    clusterName,
    setInterfaceHistoryListParam,
    setInterfaceDetailParam,
    extractParamsFromBody, // 传递参数提取函数
  );

  useEffect(() => {
    // 当 selectServiceName 为 true 时，确保 serviceNameValue 有值
    if (selectServiceName && !serviceNameValue && serviceID) {
      // 根据 serviceID 重新获取服务名称
      const option = serviceNameOptions.find((opt) => opt.key === serviceID);
      if (option) {
        setServiceNameValue(option.value);
      }
    }
  }, [selectServiceName, serviceNameValue, serviceID, serviceNameOptions]);

  useEffect(() => {
    const getScfDetailByScfId = async (scfId: string) => {
      try {
        return await get(
          `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}&operatorUser=${operatorUser}`,
        );
      } catch (error) {
        console.error(error);
      }
    };

    const getBranchList = async (scfId: string) => {
      try {
        return await get(`/iapi/iapi/scence/getBranchList?scfId=${scfId}`);
      } catch (error) {
        console.error(error);
      }
    };

    const handleScfId = async (scfId: string) => {
      setInterfaceHistoryListParam((prevState) => ({
        hisProps: {
          ...prevState.hisProps,
          scfId: scfId,
        },
      }));
      setInterfaceDetailParam({ scfID: scfId });
      isScf && updateOpenDetail(true);
      const scfDetail = await getScfDetailByScfId(scfId);

      const { implClass, interfaceClass, serviceName } = scfDetail.data;

      const resp = await getBranchList(scfId);
      const branchList = resp.data;

      const scfRequest = {
        implClassName: implClass,
        interfaceName: interfaceClass,
        scfServiceName: serviceName,
        branchList,
        operatorUser,
      };

      dispatch((state) => {
        state.request.body.scfRequest = { ...state.request.body.scfRequest, ...scfRequest };
      });
    };

    const handleService = async (
      scfServiceId: string,
      scfServiceName: string,
      clusterName: string,
    ) => {
      // 只有在从历史记录加载且尚未清空服务名称时才执行
      if (loadedFromHistory) {
        const option: AutoCompLabel = {
          value: scfServiceName,
          key: scfServiceId,
          clusterName,
        };
        handleServiceNameSelect(scfServiceId, option);
      }
    };

    if (store.request?.body.scf) {
      const scfContext = jsonBigInt.parse(store.request?.body.scf as string);

      if (scfContext.response) {
        const originRes = scfContext.response;
        const originResBody = scfContext.response.responseBody;
        const resObj = jsonBigInt.parse(originResBody);
        const isSuccess = scfContext.response.status;
        const body = resObj?.result;
        const cost = resObj?.cost || originRes?.costTime;

        dispatch((state) => {
          const response: ArexRESTResponse = {
            type: 'success',
            headers: [],
            body: '',
            statusCode: 200,
            meta: {
              responseSize: 0,
              responseDuration: 0,
            },
          };

          if (cost) {
            response.meta.responseDuration = cost;
          }
          response.body = jsonBigInt.stringify(body);
          if (response.body) {
            response.meta.responseSize = response.body.length;
          }
          if (!body) {
            if (isSuccess != 1) {
              response.statusCode = 500;
            }

            response.body = originResBody;
            response.meta.responseSize = originResBody?.length;
          }
          state.response = response;
        });
      }

      dispatch((state) => {
        state.request.body.scfRequest = scfContext;
      });

      const { scfServiceName, interfaceName, methodName, scfId, scfServiceId, clusterName } =
        scfContext;

      setServiceNameValue(scfServiceName);
      setInterfaceNameValue(interfaceName);
      setFunctionNameValue(methodName);

      isSelectServiceName(true);
      isSelectInterfaceName(true);

      if (scfId) handleScfId(scfId);

      handleService(scfServiceId, scfServiceName, clusterName);
      setLoadedFromHistory(true);
      return;
    }

    if (store.request) {
      setServiceNameValue(store.request.serviceNameValue);
      setInterfaceNameValue(store.request.interfaceNameValue);
      setFunctionNameValue(store.request.functionNameValue);

      // 如果有scfId，则需要调用handleScfId
      if (store.request.body?.scfRequest?.scfId) {
        handleScfId(store.request.body.scfRequest.scfId);
      }
    }
  }, [
    dispatch,
    handleServiceNameSelect,
    isSelectServiceName,
    setInterfaceNameValue,
    isSelectInterfaceName,
    store.request.body.scf,
  ]);

  useEffect(() => {
    dispatch((state) => {
      state.request.requestType = 'SCF';
      if (!state.request.body) {
        state.request.body = {
          contentType: undefined,
          body: undefined,
          scf: undefined,
          scfRequest: {},
          formData: [],
        };
      }
      if (!state.request.body.scfRequest) {
        try {
          const scfInfo = state.request.body.scf
            ? jsonBigInt.parse(state.request.body.scf as string)
            : {};
          state.request.body.scfRequest = scfInfo;
        } catch (e) {
          state.request.body.scfRequest = {};
        }
      }
      state.request.body.scfRequest.scfServiceName = serviceNameValue;
      state.request.body.scfRequest.interfaceName = interfaceNameValue;
      state.request.body.scfRequest.methodName = functionNameValue;
    });
  }, [serviceNameValue, interfaceNameValue, functionNameValue]);

  useEffect(() => {
    if (store.request.body?.scfRequest?.connectType == '2') {
      dispatch((state) => {
        if (!state.request.body?.scfRequest?.isOnlineScfKey) {
          state.request.body.scfRequest.isOnlineScfKey = '1';
        }
      });
    }
  }, [store.request.body?.scfRequest?.connectType]);

  const handleRequest = async () => {
    void addUserAction(UserActionEnum.SEND_SCF);
    dispatch((state) => {
      state.response = {
        type: 'loading',
        headers: undefined,
      };
    });

    const res = (await sendSCFRequest(onBeforeRequest(store.request), store.environment)) as any;
    const resStr = res.response.body;
    const isSuccess = res.response.status;
    const resObj = jsonBigInt.parse(resStr);
    const body = resObj.result;
    const cost = resObj.cost;
    if (cost) {
      res.response.meta.responseDuration = cost;
    }
    res.response.body = jsonBigInt.stringify(body);
    if (res.response.body) {
      res.response.meta.responseSize = res.response.body.length;
    }
    if (!body && resObj.message) {
      if (isSuccess != 1) {
        res.response.statusCode = 500;
      }
      res.response.body = resStr;

      if (res.response.body) {
        res.response.meta.responseSize = res.response.body.length;
      }
    }

    onRequest?.({ request: store.request, environment: store.environment }, res);
    dispatch((state) => {
      state.response = res.response;
      state.consoles = res.consoles;
      state.visualizer = res.visualizer;
      state.testResult = res.testResult;
    });
  };

  const onOnlineScfKeyChange = (v: any) => {
    dispatch((state) => {
      state.request.body.scfRequest.isOnlineScfKey = v;
    });
  };

  return (
    <div style={{ height: '100%' }}>
      <HeaderWrapper>
        <AutoComplete
          value={serviceNameValue}
          style={{ width: 400 }}
          options={serviceNameOptions}
          placeholder='服务名称'
          onSelect={(value, option) => {
            setInterfaceNameValue('');
            setFunctionNameValue('');
            isSelectInterfaceName(false);
            if (serviceID) {
              handleInterfaceNameSelect('');
            }
            handleServiceNameSelect(value, option);
          }}
          onSearch={handleSearchServiceName}
          onChange={(value) => {
            setServiceNameValue(value);
            isSelectServiceName(false);
          }}
          filterOption={handleFilterOption}
          allowClear
          onBlur={() => {
            // console.log('服务名称 onBlur 被触发', { selectServiceName });
            // if (!selectServiceName) {
            //   setServiceNameValue('');
            // }
          }}
          onClear={() => {
            setServiceNameValue('');
            setInterfaceNameValue('');
            setFunctionNameValue('');
            setLoadedFromHistory(false); // 重置历史记录状态
            dispatch((state) => {
              if (state.request.body.scfRequest) {
                state.request.body.scfRequest.ip = '';
                // 清除历史记录中的服务ID和相关信息
                // state.request.body.scfRequest.scfServiceId = undefined;
                state.request.body.scfRequest.scfId = undefined;
                // 清除接口类和函数名相关信息
                state.request.body.scfRequest.interfaceName = undefined;
                state.request.body.scfRequest.methodName = undefined;
                state.request.body.scfRequest.implClassName = undefined;
              }
            });
          }}
        />

        <AutoComplete
          value={interfaceNameValue}
          popupMatchSelectWidth={false}
          style={{ width: 400 }}
          options={interfaceNameOptions}
          placeholder='接口类'
          onSelect={(value, option) => {
            handleInterfaceNameSelect(option.value);
          }}
          onChange={(value) => {
            setInterfaceNameValue(value);
            isSelectInterfaceName(false);
          }}
          filterOption={handleFilterOption}
          allowClear
          onBlur={() => {
            if (!selectInterfaceName) {
              setInterfaceNameValue('');
            }
          }}
          onClear={() => {
            setInterfaceNameValue('');
            setFunctionNameValue('');
            isSelectInterfaceName(false);
            if (serviceID) {
              handleInterfaceNameSelect('');
            }
          }}
        />

        <AutoComplete
          value={functionNameValue}
          popupMatchSelectWidth={false}
          style={{ width: 1000 }}
          options={newFunctionOptions}
          placeholder='函数名称'
          onSelect={handleFunctionNameSelect}
          onSearch={handleFunctionSearch}
          onChange={(value) => {
            setFunctionNameValue(value);
          }}
          allowClear
          onClear={() => setFunctionNameValue('')}
        />

        <Divider type='vertical' />

        <Select
          popupMatchSelectWidth={false}
          disabled={store?.request?.inherited}
          style={{ width: 180 }}
          value={
            store.request.body.scfRequest?.connectType
              ? store.request.body.scfRequest?.connectType
              : '1'
          }
          options={[
            { label: 'IP', value: '1' },
            { label: 'Key', value: '2' },
          ]}
          onChange={(value) => {
            dispatch((state) => {
              state.request.body.scfRequest.connectType = value;
            });
          }}
        />

        {store.request.body.scfRequest?.connectType === '2' ? (
          <>
            <Select
              style={{ width: 280 }}
              options={[
                { value: '1', label: '线上' },
                { value: '2', label: '线下' },
              ]}
              value={store.request.body.scfRequest?.isOnlineScfKey || '1'}
              onChange={onOnlineScfKeyChange}
            />
            <Input
              value={store.request.body.scfRequest?.scfKey}
              style={{ width: 360 }}
              placeholder='请输入 Key'
              onChange={(e) => {
                dispatch((state) => {
                  state.request.body.scfRequest.scfKey = e.target.value;
                });
              }}
            />
          </>
        ) : (
          <>
            <Select
              popupMatchSelectWidth={false}
              style={{ width: 280 }}
              options={[
                { value: '0' as EnvCode, label: 'Test' },
                { value: '1' as EnvCode, label: 'Stable' },
                { value: '2' as EnvCode, label: 'Sandbox' },
                { value: '3' as EnvCode, label: 'Online' },
                { value: '4' as EnvCode, label: '其他' },
              ]}
              value={(store.request.body.scfRequest?.envID || '3') as EnvCode}
              onChange={onEnvChange}
            />
            <AutoComplete
              value={store.request.body.scfRequest?.ip}
              popupMatchSelectWidth={false}
              style={{ width: 400 }}
              options={ipOptions}
              onChange={onIpChange}
              onSelect={onIpChange}
              onSearch={handleIpSearch}
              placeholder='IP 地址'
              allowClear
              onClear={() => onIpChange('')}
            />
          </>
        )}

        <Button
          id='arex-request-send-btn'
          type='primary'
          loading={store.response?.type === 'loading'}
          disabled={store.response?.type === 'extensionNotInstalled'}
          icon={
            store.response?.type === 'extensionNotInstalled' ? (
              <ExclamationOutlined />
            ) : (
              <SendOutlined />
            )
          }
          onClick={handleRequest}
        >
          {t('action.send')}
        </Button>
      </HeaderWrapper>

      <SCFRequestOptions />

      <InterfaceDetail openModal={openDetail} {...interfaceDetailParam}></InterfaceDetail>
      <InterfaceHistoryList {...InterfaceHistoryListParam}></InterfaceHistoryList>
      <RequestTour />
    </div>
  );
};

export default Request;
