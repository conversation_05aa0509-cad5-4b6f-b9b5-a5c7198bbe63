import { css, styled, Theme, useArexCoreConfig } from '@arextest/arex-core';
import { Button, Form, Input, Radio, RadioChangeEvent, Select, Typography } from 'antd';
import jsonBigInt from 'json-bigint';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { IpOptions } from '../../../types/SCFRequest';

const { Text } = Typography;

const ResponseTestHeader = styled.div`
  display: flex;
  justify-content: space-between;
  & > span:first-of-type {
    font-size: 13px;
    line-height: 32px;
    font-weight: 500;
    color: #9d9d9d;
  }
`;

const ResponseTestWrapper = styled.div`
  overflow-y: auto;
  display: flex;
  justify-content: space-between;
  flex: 1;
  & > div:last-of-type {
    width: 70%;
    text-align: left;
    padding-left: 20px;
  }
`;

type EnvCode = '0' | '1' | '2' | '3' | '4';
type EnvValueCode = 'test' | 'stable' | 'sandbox' | 'online' | 'others';
const EnvMapping = {
  '0': 'test',
  '1': 'stable',
  '2': 'sandbox',
  '3': 'online',
  '4': 'others',
};

const EnvConfig = () => {
  const { theme } = useArexCoreConfig();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [connectType, setConnectType] = useState('1');
  const [branchOptions, setBranchOptions] = useState<any[]>();
  const [ipOptions, setIpOptions] = useState<any[]>();
  const [ipAll, setIpAll] = useState<IpOptions>();

  useEffect(() => {
    // 如果是通过树状结构点进来的
    if (store.request?.body.scf) {
      const scfContext = jsonBigInt.parse(store.request?.body.scf as string);
      dispatch((state) => {
        state.request.body.scfRequest = scfContext;
      });

      const { ip, env, envID, serializedVersion, connectType, isOnlineScfKey, scfKey } = scfContext;

      form.setFieldValue('ip', ip);
      form.setFieldValue('envID', envID || env);
      form.setFieldValue('serializedVersion', serializedVersion);
      form.setFieldValue('isOnlineScfKey', isOnlineScfKey);
      form.setFieldValue('scfKey', scfKey);

      setConnectType(connectType);
      return;
    }
  }, []);

  useEffect(() => {
    // 保存完会丢scfRequest, 直接return, 否则会清除部分数据
    if (!store.request.body.scfRequest) {
      return;
    }
    // const { branchVersion, port } = store.request.body.scfRequest;
    const branchVersion = store.request.body.scfRequest?.branchVersion;
    const port = store.request.body.scfRequest?.port;
    const branchList = store.request.body.scfRequest?.branchList;
    form.setFieldValue('branchVersion', branchVersion);
    form.setFieldValue('port', port);
    if (branchList) setBranchOptions(branchList);

    const ipo = store.request.body.scfRequest?.ipOptions as IpOptions;
    if (!ipo) {
      return;
    }

    const env = (form.getFieldValue('envID') || form.getFieldValue('env')) as EnvCode;
    const envVal = EnvMapping[env] as EnvValueCode;
    const ipInfos = ipo[envVal];

    setIpAll(ipo);

    if (Array.isArray(ipInfos)) {
      const ipOpts = ipInfos.map((ip) => ({ value: ip }));
      setIpOptions(ipOpts);
      if (!form.getFieldValue('ip')) {
        form.setFieldValue('ip', ipOpts[0]);
      }
      return;
    }

    const allIPs: { value: string }[] = [];
    if (ipInfos) {
      for (const [key, value] of Object.entries(ipInfos)) {
        // 遍历每个IP地址数组
        for (const ip of value) {
          // 将IP地址以对象形式添加到allIPs数组中
          allIPs.push({ value: ip });
        }
      }

      const transformedData = Object.keys(ipInfos).map((key) => ({
        label: <span>{key}</span>,
        title: key,
        options: ipInfos[key].map((ip) => ({
          label: <span>{ip}</span>,
          value: ip,
        })),
      }));

      setIpOptions(transformedData);
      // IP为空默认选第一个
      const currentIP = form.getFieldValue('ip');
      if (!currentIP) {
        form.setFieldValue('ip', allIPs[0]);
      }
      // 当前IP不在IP列表中,改为选第一个
      const isIpInList = allIPs.some((item) => {
        return item.value === currentIP;
      });
      if (!isIpInList) {
        form.setFieldValue('ip', allIPs[0]);
      }
    }
  }, [dispatch, store.request.body.scfRequest]);

  // const onChange = ({ target: { value } }: RadioChangeEvent) => {
  //   setConnectType(value);
  //   dispatch((state) => {
  //     if (!state.request.body.scfRequest) {
  //       try {
  //         const scfInfo = jsonBigInt.parse(store.request.body.scf as string);
  //         state.request.body.scfRequest = scfInfo;
  //       } catch (e) {
  //         state.request.body.scfRequest = {};
  //       }
  //     }
  //     state.request.body.scfRequest.connectType = value;
  //   });
  // };

  const onFormValuesChange = () => {
    dispatch((state) => {
      const currentScfRequest = state.request.body.scfRequest || {};
      const newVal = form.getFieldsValue() || {};
      state.request.body.scfRequest = {
        ...currentScfRequest,
        ...newVal,
      };
    });
  };

  const onEnvChange = (v: EnvCode) => {
    form.setFieldValue('ip', '');
    const envVal = EnvMapping[v] as EnvValueCode;
    if (!ipAll) return;

    const ipInfos = ipAll[envVal] as { [key: string]: string[] };

    let allIPs: { value: string }[] = [];
    if (Array.isArray(ipInfos) && ipInfos.length > 0) {
      allIPs = ipInfos.map((ip) => ({ value: ip }));
      setIpOptions(allIPs);
      form.setFieldValue('ip', allIPs[0].value);
      return;
    }

    for (const [_, value] of Object.entries(ipInfos)) {
      // 遍历每个IP地址数组
      for (const ip of value) {
        // 将IP地址以对象形式添加到allIPs数组中
        allIPs.push({ value: ip });
      }
    }

    const transformedData = Object.keys(ipInfos).map((key) => ({
      label: <span>{key}</span>,
      title: key,
      options: ipInfos[key].map((ip: any) => ({
        label: <span>{ip}</span>,
        value: ip,
      })),
    }));

    setIpOptions(transformedData);

    if (allIPs.length > 0) {
      form.setFieldValue('ip', allIPs[0].value);
    }
  };

  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
      `}
    >
      <ResponseTestHeader>
        <span>{t('scf.tab.envHeader')}</span>
      </ResponseTestHeader>
      <ResponseTestWrapper>
        <div
          css={css`
            min-width: 0;
            flex: 1;
          `}
        >
          {/* <Radio.Group defaultValue='1' size='large' onChange={onChange} value={connectType}>
            <Radio.Button value='1'>IP</Radio.Button>
            <Radio.Button value='2'>ScfKey</Radio.Button>
          </Radio.Group> */}
        </div>
        <div
          css={css`
            display: flex;
            flex-direction: column;
          `}
        >
          <Form
            name='basic'
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 300 }}
            autoComplete='off'
            onFieldsChange={onFormValuesChange}
            form={form}
          >
            {/* {connectType === '1' ? (
              <>
                <Form.Item
                  label='Env'
                  name='envID'
                  initialValue='3'
                  rules={[{ required: connectType === '1', message: 'Please choose env!' }]}
                >
                  <Select
                    // defaultValue={'Online'}
                    options={[
                      { value: '0', label: 'Test' },
                      { value: '1', label: 'Stable' },
                      { value: '2', label: 'Sandbox' },
                      { value: '3', label: 'Online' },
                      { value: '4', label: '其他' },
                    ]}
                    onChange={onEnvChange}
                  />
                </Form.Item>
                <Form.Item
                  label='IP'
                  name='ip'
                  rules={[{ required: connectType === '1', message: 'Please choose ip' }]}
                >
                  <Select options={ipOptions} />
                </Form.Item>
              </>
            ) : (
              <>
                <Form.Item
                  label='Env'
                  name='isOnlineScfKey'
                  initialValue='2'
                  rules={[
                    {
                      required: connectType === '2',
                      message: 'Please choose env!',
                    },
                  ]}
                >
                  <Select
                    options={[
                      { value: '1', label: '线上' },
                      { value: '2', label: '线下' },
                    ]}
                  />
                </Form.Item>
                <Form.Item
                  label='ScfKey'
                  name='scfKey'
                  rules={[
                    {
                      required: connectType === '2',
                      message: 'Please input scfKey',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </>
            )} */}
            <Form.Item
              label='port'
              name='port'
              rules={[{ required: true, message: 'Please input port' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item label='serialized' name='serializedVersion' initialValue='0'>
              <Select
                options={[
                  { value: '0', label: '不指定版本' },
                  { value: 'SCF', label: 'SCF' },
                  { value: 'SCFV3', label: 'SCFV3' },
                  { value: 'SCFV4', label: 'SCFV4' },
                ]}
              />
            </Form.Item>
            <Form.Item label='branch' name='branchVersion'>
              <Select options={branchOptions} />
            </Form.Item>
          </Form>
        </div>
      </ResponseTestWrapper>
    </div>
  );
};

export default EnvConfig;
