import { ExclamationOutlined, SendOutlined } from '@ant-design/icons';
import { getLocalStorage, Label, styled } from '@arextest/arex-core';
import { AutoComplete, Button, Checkbox, Divider, Input, Select } from 'antd';
import jsonBigInt from 'json-bigint';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { addUserAction, sendSCFRequest, UserActionEnum } from '../../../helpers';
import { useArexRequestProps, useArexRequestStore } from '../../../hooks';
import { get } from '../../../service/scfInfo';
import { ArexEnvironment, ArexRESTRequest, ArexRESTResponse } from '../../../types';
import { IpOptions } from '../../../types/SCFRequest';
import { EnvironmentSelectProps } from '../../NavigationBar/EnvironmentSelect';
import { InfoSummaryProps } from '../../NavigationBar/InfoSummary';
import { useParamsExtractor } from '../../Request/hooks/useParamsExtractor';
import InterfaceDetail, {
  ChildComponentProps,
} from '../../Request/InterfaceDetail/InterfaceDetail';
import InterfaceHistoryList, {
  ChildComponentPropsHis,
} from '../../Request/InterfaceHistory/InterfaceHistoryList';
import SCFRequestOptions from './RequestOptions';
import RequestTour from './RequestTour';

declare global {
  interface Window {
    __AREX_EXTENSION_INSTALLED__: boolean;
  }
}

const HeaderWrapper = styled.div`
  padding: 0 8px;
  display: flex;
  // .ant-select-selector {
  //   border-radius: 6px 0 0 6px;
  // }
`;

type AutoCompLabel = {
  key: string;
  value: string;
  clusterName?: string;
  functionName?: string;
};

export type RequestProps = {
  disableSave?: boolean;
  onBeforeRequest?: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest;
  onRequest?: (
    reqData: { request: ArexRESTRequest; environment?: ArexEnvironment },
    resData: Awaited<ReturnType<typeof sendSCFRequest>>,
  ) => void;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
  onSaveAs?: () => void;
  navPane?: any;
} & InfoSummaryProps & {
    environmentProps?: EnvironmentSelectProps;
  };

const getUserEmail = () => {
  const email: string = getLocalStorage('email') || '';
  return email.toLowerCase().startsWith('guest') ? 'qinsisheng' : email;
};

type EnvCode = '0' | '1' | '2' | '3' | '4';
type EnvValueCode = 'test' | 'stable' | 'sandbox' | 'online' | 'others';
const EnvMapping = {
  '0': 'test',
  '1': 'stable',
  '2': 'sandbox',
  '3': 'online',
  '4': 'others',
};

const useElementBySelector = (selector: string) => {
  const [element, setElement] = useState<HTMLElement | null>(null);

  useEffect(() => {
    const node = document.querySelector(selector);
    if (node) {
      setElement(node as HTMLElement);
    }

    // 返回一个清理函数，当组件卸载时移除事件监听器（这里为空）
    return () => { };
  }, [selector]);

  return element;
};

const Request: FC<RequestProps> = (props: any) => {
  const { onBeforeRequest = (request: ArexRESTRequest) => request, onRequest } =
    useArexRequestProps();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();
  const { extractParamsFromBody } = useParamsExtractor();

  // 调用接口详情传参
  const [interfaceDetailParam, setInterfaceDetailParam] = useState<ChildComponentProps>({
    scfID: '',
  });
  // 获取当前URL的查询参数
  const [searchParams] = useSearchParams();
  const scfParam = searchParams.get('scf');
  const isScf = scfParam !== null && scfParam === 'true';
  const [openDetail, updateOpenDetail] = useState<boolean>(false);

  // SCF请求相关
  // 接口获取的所有servicename
  const [serviceNameOptions, setServiceNameOptions] = useState<AutoCompLabel[]>([]);
  // serviceName筛选过滤option
  const [serviceFilterOption, setServiceFilterOption] = useState<AutoCompLabel[]>([]);
  // 选中的serviceName
  const [serviceNameValue, setServiceNameValue] = useState<string>();
  // 选中的serviceId
  const [serviceID, setServiceID] = useState<string>();
  // 是否选中的serviceName焦点
  const [selectServiceName, isSelectServiceName] = useState(false);

  // 接口获取的所有servicename
  const [interfaceNameOptions, setInterfaceNameOptions] = useState<AutoCompLabel[]>([]);
  // 选中的接口名
  const [interfaceNameValue, setInterfaceNameValue] = useState<string>();
  // 是否选中的接口名焦点
  const [selectInterfaceName, isSelectInterfaceName] = useState(false);

  // 选中的方法名
  const [functionNameValue, setFunctionNameValue] = useState<string>();
  // 筛选项
  const [newFunctionOptions, setNewFunctionOptions] = useState([]);
  // 原始筛选项
  const [originFunctionOptions, setOriginFunctionOptions] = useState([]);

  // 集群名称
  const [clusterName, setClusterName] = useState<string | undefined>('');

  const operatorUser = getUserEmail();

  const [originIpData, setOriginIpData] = useState<IpOptions>();
  const [ipOptions, setIpOptions] = useState<any[]>();
  const [originIpOptions, setoriginIpOptions] = useState<any[]>();

  const hisProps = {
    scfId: `0`,
    navPane: props.navPane,
  };

  const [InterfaceHistoryListParam, setInterfaceHistoryListParam] =
    useState<ChildComponentPropsHis>({ hisProps });

  useEffect(() => {
    const getAllServiceName = async () => {
      try {
        const fetchedData = await get('/iapi/iapi/scence/getAllServiceInfo');
        const serviceOpt = fetchedData.data;
        setServiceNameOptions(serviceOpt);
      } catch (error) {
        console.error(error);
      }
    };

    getAllServiceName();

    const getScfDetailByScfId = async (scfId: string) => {
      try {
        return await get(
          `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}&operatorUser=${operatorUser}`,
        );
      } catch (error) {
        console.error(error);
      }
    };

    const getBranchList = async (scfId: string) => {
      try {
        return await get(`/iapi/iapi/scence/getBranchList?scfId=${scfId}`);
      } catch (error) {
        console.error(error);
      }
    };

    const handleScfId = async (scfId: string) => {
      //setInterfaceHistoryListParam({ scfId });
      setInterfaceHistoryListParam((prevState) => ({
        hisProps: {
          ...prevState.hisProps,
          scfId: scfId,
        },
      }));
      // console.log("hisprop scfId",hisProps.scfId)
      setInterfaceDetailParam({ scfID: scfId });
      isScf && updateOpenDetail(true);
      const scfDetail = await getScfDetailByScfId(scfId);

      const { implClass, interfaceClass, serviceName } = scfDetail.data;

      const resp = await getBranchList(scfId);
      const branchList = resp.data;

      const scfRequest = {
        implClassName: implClass,
        interfaceName: interfaceClass,
        scfServiceName: serviceName,
        branchList,
        operatorUser,
      };

      dispatch((state) => {
        state.request.body.scfRequest = { ...state.request.body.scfRequest, ...scfRequest };
      });
    };

    const handleService = async (
      scfServiceId: string,
      scfServiceName: string,
      clusterName: string,
    ) => {
      const option: AutoCompLabel = {
        value: scfServiceName,
        key: scfServiceId,
        clusterName,
      };
      handleServiceNameSelect(scfServiceId, option);
    };

    console.log('----->>>>>useeffect = ', store.request, store.request.recordId);

    // 如果是通过 树状结构 | history | url 进来的
    if (store.request?.body.scf) {
      const scfContext = jsonBigInt.parse(store.request?.body.scf as string);

      // 如果有response，一般是 history 进来的
      if (scfContext.response) {
        const originRes = scfContext.response;
        const originResBody = scfContext.response.responseBody;
        const resObj = jsonBigInt.parse(originResBody);
        const isSuccess = scfContext.response.status;
        const body = resObj?.result;
        const cost = resObj?.cost || originRes?.costTime;

        dispatch((state) => {
          const response: ArexRESTResponse = {
            type: 'success',
            headers: [],
            body: '',
            statusCode: 200,
            meta: {
              responseSize: 0, // in bytes
              responseDuration: 0, // in millis
            },
          };

          if (cost) {
            response.meta.responseDuration = cost;
          }
          response.body = jsonBigInt.stringify(body);
          if (response.body) {
            response.meta.responseSize = response.body.length;
          }
          if (!body) {
            if (isSuccess != 1) {
              response.statusCode = 500;
            }

            response.body = originResBody;
            response.meta.responseSize = originResBody?.length;
          }
          state.response = response;
        });
      }

      dispatch((state) => {
        state.request.body.scfRequest = scfContext;
      });

      const { scfServiceName, interfaceName, methodName, scfId, scfServiceId, clusterName } =
        scfContext;

      setServiceNameValue(scfServiceName);
      setInterfaceNameValue(interfaceName);
      setFunctionNameValue(methodName);
      isSelectInterfaceName(true);
      isSelectServiceName(true);

      if (scfId) handleScfId(scfId);

      handleService(scfServiceId, scfServiceName, clusterName);
      return;
    }

    // 加载SCF接口信息数据
    if (store.request) {
      setServiceNameValue(store.request.serviceNameValue);
      setInterfaceNameValue(store.request.interfaceNameValue);
      setFunctionNameValue(store.request.functionNameValue);
    }
  }, []);

  useEffect(() => {
    dispatch((state) => {
      if (!state.request.body) {
        state.request.body = {
          contentType: undefined,
          body: undefined,
          scf: undefined,
          scfRequest: {}, // 初始化为空对象而不是undefined
          formData: [], // 初始化为空数组
        };
      }
      if (!state.request.body.scfRequest) {
        try {
          const scfInfo = state.request.body.scf
            ? jsonBigInt.parse(state.request.body.scf as string)
            : {};
          state.request.body.scfRequest = scfInfo;
        } catch (e) {
          state.request.body.scfRequest = {};
        }
      }
      state.request.body.scfRequest.scfServiceName = serviceNameValue;
      state.request.body.scfRequest.interfaceName = interfaceNameValue;
      state.request.body.scfRequest.methodName = functionNameValue;
    });
  }, [serviceNameValue, interfaceNameValue, functionNameValue]);

  useEffect(() => {
    if (!originIpData) {
      return;
    }

    const scfContext = store.request.body.scfRequest;

    const env = scfContext.envID as EnvCode;
    const envVal = EnvMapping[env] as EnvValueCode;
    const ipInfos = originIpData[envVal];

    if (Array.isArray(ipInfos)) {
      const ipOpts = ipInfos.map((ip) => ({ value: ip }));
      setIpOptions(ipOpts);
      setoriginIpOptions(ipOpts);
      if (!scfContext.ip) {
        dispatch((state) => {
          state.request.body.scfRequest.ip = ipOpts[0]?.value;
        });
      }
      return;
    }

    // 将 recordId 中的 IP 部分从破折号格式转换为点分格式
    let formattedIp = '';
    const recordId = store.request.recordId;
    if (recordId) {
      const parts = recordId.split('-');
      if (parts.length >= 6) {
        formattedIp = `${parts[1]}.${parts[2]}.${parts[3]}.${parts[4]}`;
        console.log('格式化后的 IP:', formattedIp);
      }
    }

    const allIPs: { value: string }[] = [];
    if (ipInfos) {
      for (const [key, value] of Object.entries(ipInfos)) {
        // 遍历每个IP地址数组
        for (const ip of value) {
          // 将IP地址以对象形式添加到allIPs数组中
          allIPs.push({ value: ip });
        }
      }

      const transformedData = Object.keys(ipInfos).map((key) => ({
        label: <span>{key}</span>,
        title: key,
        options: ipInfos[key].map((ip) => ({
          label: <span>{ip}</span>,
          value: ip,
        })),
      }));

      setIpOptions(transformedData);
      setoriginIpOptions(transformedData);

      //console.log('--->>>>>>use effect originIpData = ', originIpData, scfContext, allIPs)
      //console.log('所有可用的IP列表:', allIPs.map(item => item.value))
      // IP为空时，检查 formattedIp 是否存在于 envID 2 或 3 的 IP 列表中
      const currentIP = scfContext.ip;
      if (!currentIP && formattedIp) {
        // 检查 formattedIp 是否在 envID 2 (sandbox) 的 IP 列表中
        const sandboxIPs = store.request.body.scfRequest.ipOptions?.sandbox || [];
        const isInSandbox = sandboxIPs.includes(formattedIp);
        //console.log("sandboxIPs is**", sandboxIPs)

        // 检查 formattedIp 是否在 envID 3 (online) 的 IP 列表中
        const onlineIPs = store.request.body.scfRequest.ipOptions?.online ? Object.values(store.request.body.scfRequest.ipOptions.online).flat() : [];
        const isInOnline = onlineIPs.includes(formattedIp);
        //console.log("onlineIPs is**", onlineIPs)


        console.log("formattedIp:", formattedIp);
        console.log("是否在 sandbox 环境中:", isInSandbox);
        console.log("是否在 online 环境中:", isInOnline);

        if (isInSandbox) {
          // 如果在 sandbox 环境中找到，先设置 sandbox 环境的 IP 选项
          const sandboxIPOptions = sandboxIPs.map(ip => ({ value: ip, label: ip }));
          setIpOptions(sandboxIPOptions);
          setoriginIpOptions(sandboxIPOptions);

          // 然后设置 envID 为 2 和 IP
          dispatch((state) => {
            state.request.body.scfRequest.envID = '2';
            state.request.body.scfRequest.ip = formattedIp;
          });
        } else if (isInOnline) {
          // 如果在 online 环境中找到，先设置 online 环境的 IP 选项
          const onlineIPOptions = Object.entries(store.request.body.scfRequest.ipOptions?.online || {}).map(([key, ips]) => ({
            label: <span>{key}</span>,
            options: (ips as string[]).map((ip) => ({
              value: ip,
              label: ip,
            })),
          }));
          setIpOptions(onlineIPOptions);
          setoriginIpOptions(onlineIPOptions);

          // 然后设置 envID 为 3 和 IP
          dispatch((state) => {
            state.request.body.scfRequest.envID = '3';
            state.request.body.scfRequest.ip = formattedIp;
          });
        } else {
          // 如果都没找到，使用默认的第一个 IP
          dispatch((state) => {
            state.request.body.scfRequest.ip = allIPs[0]?.value;
          });
        }
      } else {
        // 只有在没有 formattedIp 的情况下，才检查当前 IP 是否在列表中
        const isIpInList = allIPs.some((item) => {
          return item.value === currentIP;
        });
        if (!isIpInList) {
          dispatch((state) => {
            state.request.body.scfRequest.ip = allIPs[0]?.value;
          });
        }
      }
    }
  }, [originIpData]);

  useEffect(() => {
    if (store.request.body?.scfRequest?.connectType == '2') {
      dispatch((state) => {
        if (!state.request.body?.scfRequest?.isOnlineScfKey) {
          state.request.body.scfRequest.isOnlineScfKey = '1';
        }
      });
    }
  }, [store.request.body?.scfRequest?.connectType]);

  // serviceName start
  const handleSearchServiceName = (value: string) => {
    const filtered = serviceNameOptions.filter((option) =>
      option?.value?.toLocaleLowerCase().includes(value.toLocaleLowerCase()),
    );
    setServiceFilterOption(filtered);
  };

  const handleServiceNameSelect = async (value: string, option: AutoCompLabel) => {
    setServiceNameValue(option.value);
    setServiceID(option.key);
    setClusterName(option?.clusterName);
    isSelectServiceName(true);
    // TODO 如果保存的时候没有选方法，option.key为空，就不会去拿后面的信息
    // 获取接口列表
    if (!option.key) return;

    const interfaceData = await get(
      `/iapi/iapi/scence/getInterfaceClassByServiceName?serviceId=${option.key}`,
    );

    if (!Array.isArray(interfaceData.data)) return;

    // 获取接口列表
    const iOpt = interfaceData.data.map((v: any) => ({ value: v.value }));
    setInterfaceNameOptions(iOpt);

    // 获取方法列表
    const functionData = await get(
      `/iapi/iapi/scence/openapi/getFunctions?serviceId=${option.key}`,
    );
    const data = getFunctionOptions(functionData.data) as any;
    setNewFunctionOptions(data);
    setOriginFunctionOptions(data);

    // 获取ip列表
    const cName = option.clusterName ? option.clusterName : clusterName;
    if (!cName) return;
    const ipResp = await get(`/iapi/iapi/scence/getEnvInfosByClusterName?clusterName=${cName}`);
    setOriginIpData(ipResp.data);
    console.log('---->>>>>>>ipResp == ', ipResp.data)

    dispatch((state) => {
      state.request.body.scfRequest.ipOptions = ipResp.data as IpOptions;
      if (!state.request.body.scfRequest.envID) {
        state.request.body.scfRequest.envID = '3';
      }
    });
  };
  // serviceName end

  // interfaceName
  const handleInterfaceNameSelect = async (value: string) => {
    setInterfaceNameValue(value);
    isSelectInterfaceName(true);

    //获取方法列表
    const functionData = await get(
      `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceID}&interfcClsName=${value}`,
    );
    const data = getFunctionOptions(functionData.data) as any;
    setNewFunctionOptions(data);
    setOriginFunctionOptions(data);

    if (value !== interfaceNameValue) {
      setFunctionNameValue('');
    }
  };

  // functionName
  const handleFunctionNameSelect = async (value: string, option: AutoCompLabel) => {
    setFunctionNameValue(option.functionName);
    // isSelectFunctionName(true);
    // isImplDisable(true);
    const scfId = option.key;
    const scfDetail = await get(
      `/iapi/iapi/scence/openapi/getScfDetailByScfIdV2?scfId=${scfId}&operatorUser=${operatorUser}`,
    );
    const { implClass, interfaceClass, paramList, serviceName, servicePort, version } =
      scfDetail.data;
    const resp = await get(`/iapi/iapi/scence/getBranchList?scfId=${scfId}`);
    const branchList = resp.data;
    const scfRequest = {
      scfId,
      implClassName: implClass,
      interfaceName: interfaceClass,
      params: paramList,
      scfServiceName: serviceName,
      scfServiceId: serviceID,
      clusterName,
      port: servicePort,
      branchVersion: version,
      branchList,
      connectType: '1',
      serializedVersion: '0',
      envID: '3',
      operatorUser,
    };

    dispatch((state) => {
      state.request.body.scfRequest = { ...state.request.body.scfRequest, ...scfRequest };
    });

    // 在切换方法后重新进行参数分析
    try {
      // 保存当前的contract数据，以便后续恢复描述信息
      const currentContract = store.request.contract ? [...store.request.contract] : [];
      const currentDescriptions: Record<string, string> = {};

      // 提取当前的描述信息
      if (currentContract && currentContract.length > 0) {
        const extractDescriptions = (params: any[]) => {
          params.forEach((param) => {
            if (param.desc) {
              currentDescriptions[param.key] = param.desc;
            }
            if (param.children && param.children.length > 0) {
              extractDescriptions(param.children);
            }
          });
        };

        extractDescriptions(currentContract);
      }

      // 构建包含新scfRequest的requestBody
      const requestBody = {
        ...store.request.body,
        scfRequest: { ...store.request.body.scfRequest, ...scfRequest },
      };

      // 使用参数提取器重新分析参数
      const finalRequestType = 'SCF';
      const params = extractParamsFromBody(requestBody, finalRequestType, store.request.params);

      if (params && params.length > 0) {
        // 恢复描述信息到新提取的参数
        const restoreDescriptions = (paramList: any[]): any[] => {
          return paramList.map((param) => {
            const savedDesc = currentDescriptions[param.key];
            const updatedParam = { ...param };
            if (savedDesc) {
              updatedParam.desc = savedDesc;
            }
            if (param.children && param.children.length > 0) {
              updatedParam.children = restoreDescriptions(param.children);
            }
            return updatedParam;
          });
        };

        const paramsWithDesc = restoreDescriptions(params);

        dispatch((state: any) => {
          state.request.contract = paramsWithDesc;
        });
      }
    } catch (error) {
      console.error('切换方法后重新分析参数失败:', error);
    }

    //setInterfaceHistoryListParam({ scfId });
    setInterfaceHistoryListParam((prevState) => ({
      hisProps: {
        ...prevState.hisProps,
        scfId: scfId,
      },
    }));
    setInterfaceDetailParam({ scfID: scfId });

  };

  const handleFilterOption = (inputValue: string, option: any) => {
    return option?.value?.toLocaleLowerCase().includes(inputValue.toLocaleLowerCase());
  };

  const handleRequest = async () => {
    void addUserAction(UserActionEnum.SEND_SCF);

    // 在发送请求前重新进行参数分析
    try {
      if (store.request.body) {
        // 保存当前的contract数据，以便后续恢复描述信息
        const currentContract = store.request.contract ? [...store.request.contract] : [];
        const currentDescriptions: Record<string, string> = {};

        // 提取当前的描述信息
        if (currentContract && currentContract.length > 0) {
          const extractDescriptions = (params: any[]) => {
            params.forEach((param) => {
              if (param.desc) {
                currentDescriptions[param.key] = param.desc;
              }
              if (param.children && param.children.length > 0) {
                extractDescriptions(param.children);
              }
            });
          };

          extractDescriptions(currentContract);
        }

        // 检测SCF请求并提取新的参数
        let requestBody = store.request.body;
        const isSCFRequest = store.request.requestType === 'SCF' || (!store.request.requestType && requestBody?.scfRequest);

        if (isSCFRequest && requestBody?.scf && (!requestBody.scfRequest || !requestBody.scfRequest.params || requestBody.scfRequest.params.length === 0)) {
          try {
            const scfInfo = JSON.parse(requestBody.scf);
            requestBody = {
              ...requestBody,
              scfRequest: {
                ...requestBody.scfRequest,
                ...scfInfo,
              },
            };
          } catch (error) {
            console.error('解析SCF数据失败:', error);
          }
        }

        // 使用参数提取器重新分析参数
        const finalRequestType = isSCFRequest ? 'SCF' : store.request.requestType;
        const params = extractParamsFromBody(requestBody, finalRequestType, store.request.params);

        if (params && params.length > 0) {
          // 恢复描述信息到新提取的参数
          const restoreDescriptions = (paramList: any[]): any[] => {
            return paramList.map((param) => {
              const savedDesc = currentDescriptions[param.key];
              const updatedParam = { ...param };
              if (savedDesc) {
                updatedParam.desc = savedDesc;
              }
              if (param.children && param.children.length > 0) {
                updatedParam.children = restoreDescriptions(param.children);
              }
              return updatedParam;
            });
          };

          const paramsWithDesc = restoreDescriptions(params);

          dispatch((state: any) => {
            state.request.contract = paramsWithDesc;
          });
        }
      }
    } catch (error) {
      console.error('重新分析参数失败:', error);
    }

    dispatch((state) => {
      state.response = {
        type: 'loading',
        headers: undefined,
      };
    });

    // if (!window.__AREX_EXTENSION_INSTALLED__) return;
    console.log('Call Scf Request Info', store.request);
    const res = (await sendSCFRequest(onBeforeRequest(store.request), store.environment)) as any;
    console.log('Call Scf Origin Response Info', res);
    const resStr = res.response.body;
    const isSuccess = res.response.status;
    const resObj = jsonBigInt.parse(resStr);
    const body = resObj.result;
    const cost = resObj.cost;
    if (cost) {
      res.response.meta.responseDuration = cost;
    }
    res.response.body = jsonBigInt.stringify(body);
    if (res.response.body) {
      res.response.meta.responseSize = res.response.body.length;
    }
    if (!body && resObj.message) {
      // res.response.body = resObj.message;
      if (isSuccess != 1) {
        res.response.statusCode = 500;
      }
      // res.response.statusCode = '500';
      res.response.body = resStr;

      // res.response.body = jsonBigInt.stringify(resStr);
      if (res.response.body) {
        res.response.meta.responseSize = res.response.body.length;
      }
    }
    console.log('Call Scf Final Response Info', res);

    onRequest?.({ request: store.request, environment: store.environment }, res);
    dispatch((state) => {
      console.log('consoles', res.consoles);
      state.response = res.response;
      state.consoles = res.consoles;
      state.visualizer = res.visualizer;
      state.testResult = res.testResult;
    });
  };

  // 方法渲染
  const renderItem = (value: string, key: string, parent: string) => ({
    key,
    value: key as string,
    parent,
    functionName: value,
    label: (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        {value}
      </div>
    ),
  });
  function getFunctionOptions(data: {
    [s: string]: Array<{ scf_function_name: string; scfID: string }>;
  }) {
    return Object.entries(data).map(([label, item]) => {
      return {
        label,
        options: item.map((value: { scf_function_name: string; scfID: string }) => {
          return renderItem(value.scf_function_name, value.scfID, label);
        }),
      };
    });
  }

  const handleSearch = (value: string) => {
    const filteredOptions = originFunctionOptions.flatMap((category: any) =>
      category.options.filter(
        (item: any) => item.functionName.toLowerCase().indexOf(value.toLowerCase()) !== -1,
      ),
    );

    const transformedData = filteredOptions.reduce((acc, curr) => {
      if (!acc[curr.parent]) {
        acc[curr.parent] = [];
      }

      acc[curr.parent].push({
        scfID: curr.key,
        scf_function_name: curr.functionName,
      });

      return acc;
    }, {});
    const newOptions = getFunctionOptions(transformedData) as any;
    setNewFunctionOptions(newOptions);
  };

  const onEnvChange = (v: EnvCode) => {
    dispatch((state) => {
      state.request.body.scfRequest.ip = '';
      state.request.body.scfRequest.envID = v;
    });

    const envVal = EnvMapping[v] as EnvValueCode;
    if (!originIpData) return;

    const ipInfos = originIpData[envVal] as { [key: string]: string[] };

    let allIPs: { value: string }[] = [];
    if (Array.isArray(ipInfos) && ipInfos.length > 0) {
      allIPs = ipInfos.map((ip) => ({ value: ip, label: ip }));
      setIpOptions(allIPs);
      setoriginIpOptions(allIPs);
      dispatch((state) => {
        // if (!state.request.body.scfRequest.envID) {
        //   state.request.body.scfRequest.envID = '3';
        // }
        state.request.body.scfRequest.ip = allIPs[0].value;
      });

      return;
    }

    for (const [_, value] of Object.entries(ipInfos)) {
      // 遍历每个IP地址数组
      for (const ip of value) {
        // 将IP地址以对象形式添加到allIPs数组中
        allIPs.push({ value: ip });
      }
    }

    const transformedData = Object.keys(ipInfos).map((key) => ({
      label: <span>{key}</span>,
      title: key,
      options: ipInfos[key].map((ip: any) => ({
        label: <span>{ip}</span>,
        value: ip,
      })),
    }));

    setIpOptions(transformedData);
    setoriginIpOptions(transformedData);

    if (allIPs.length > 0) {
      dispatch((state) => {
        state.request.body.scfRequest.ip = allIPs[0].value;
      });
    }
  };

  const onIpChange = (v: any) => {
    dispatch((state) => {
      state.request.body.scfRequest.ip = v;
    });
  };

  const handleIpSearch = (value: string) => {
    const currentEnv = store.request.body.scfRequest.envID;
    // stable or online
    if (currentEnv === '3' || currentEnv === '1') {
      const filteredIpOption = originIpOptions?.map((item) => ({
        ...item,
        options: item.options.filter((option: { value: string | string[] }) =>
          option.value.includes(value),
        ),
      }));
      setIpOptions(filteredIpOption);
      return;
    }

    const filteredIpOption = originIpOptions?.filter((item) => item.value.includes(value));
    setIpOptions(filteredIpOption);
  };

  const onOnlineScfKeyChange = (v: any) => {
    dispatch((state) => {
      state.request.body.scfRequest.isOnlineScfKey = v;
    });
  };

  return (
    <div style={{ height: '100%' }}>
      <HeaderWrapper>
        {/* serviceName */}
        <AutoComplete
          value={serviceNameValue}
          style={{ width: 400 }}
          options={serviceNameOptions}
          placeholder='服务名称'
          onSelect={handleServiceNameSelect}
          onSearch={handleSearchServiceName}
          onChange={(value) => {
            setServiceNameValue(value);
            isSelectServiceName(false);
          }}
          filterOption={handleFilterOption}
          allowClear
          onBlur={() => {
            if (!selectServiceName) {
              setServiceNameValue('');
            }
          }}
          onClear={() => {
            setInterfaceNameValue('');
            setFunctionNameValue('');
          }}
        />

        {/* 接口 */}
        <AutoComplete
          value={interfaceNameValue}
          popupMatchSelectWidth={false}
          style={{ width: 400 }}
          options={interfaceNameOptions}
          placeholder='接口类'
          onSelect={handleInterfaceNameSelect}
          // onSearch={handleSearch}
          onChange={(value) => {
            setInterfaceNameValue(value);
            isSelectInterfaceName(false);
          }}
          filterOption={handleFilterOption}
          allowClear
          onBlur={() => {
            if (!selectInterfaceName) {
              setInterfaceNameValue('');
            }
          }}
          onClear={async () => {
            const functionData = await get(
              `/iapi/iapi/scence/openapi/getFunctions?serviceId=${serviceID}`,
            );
            const data = getFunctionOptions(functionData.data) as any;
            setNewFunctionOptions(data);
            setOriginFunctionOptions(data);
            setFunctionNameValue('');
          }}
        />

        {/* 方法 */}
        <AutoComplete
          value={functionNameValue}
          popupMatchSelectWidth={false}
          style={{ width: 1000 }}
          options={newFunctionOptions}
          placeholder='函数名称'
          onSelect={handleFunctionNameSelect}
          onSearch={handleSearch}
          onChange={(value) => {
            setFunctionNameValue(value);
          }}
          allowClear
        ></AutoComplete>

        <Divider type='vertical' />

        <Select
          popupMatchSelectWidth={false}
          disabled={store?.request?.inherited}
          style={{ width: 180 }}
          value={
            store.request.body.scfRequest?.connectType
              ? store.request.body.scfRequest?.connectType
              : '1'
          }
          options={[
            { label: 'IP', value: '1' },
            { label: 'Key', value: '2' },
          ]}
          onChange={(value) => {
            dispatch((state) => {
              state.request.body.scfRequest.connectType = value;
            });
          }}
        />

        {store.request.body.scfRequest?.connectType === '2' ? (
          <>
            {/* isOnlineScfKey */}
            <Select
              style={{ width: 280 }}
              options={[
                { value: '1', label: '线上' },
                { value: '2', label: '线下' },
              ]}
              value={store.request.body.scfRequest?.isOnlineScfKey}
              onChange={onOnlineScfKeyChange}
            />

            {/* scfKey */}
            <Input
              value={store.request.body.scfRequest?.scfKey}
              style={{ width: 360 }}
              onChange={(e) => {
                dispatch((state) => {
                  state.request.body.scfRequest.scfKey = e.target.value;
                });
              }}
            />
          </>
        ) : (
          <>
            {/* env */}
            <Select
              popupMatchSelectWidth={false}
              // defaultValue={'Online'}
              style={{ width: 280 }}
              options={[
                { value: '0' as EnvCode, label: 'Test' },
                { value: '1' as EnvCode, label: 'Stable' },
                { value: '2' as EnvCode, label: 'Sandbox' },
                { value: '3' as EnvCode, label: 'Online' },
                { value: '4' as EnvCode, label: '其他' },
              ]}
              value={store.request.body.scfRequest?.envID as EnvCode}
              onChange={onEnvChange}
            />

            {/* ip */}
            {/* <Select
              popupMatchSelectWidth={false}
              style={{ width: 400 }}
              options={ipOptions}
              onChange={onIpChange}
              value={store.request.body.scfRequest?.ip}
            /> */}

            {/* IP */}
            <AutoComplete
              value={store.request.body.scfRequest?.ip}
              popupMatchSelectWidth={false}
              style={{ width: 400 }}
              options={ipOptions}
              onChange={onIpChange}
              onSelect={(value) => {
                dispatch((state) => {
                  state.request.body.scfRequest.ip = value;
                });
              }}
              onSearch={handleIpSearch}
            ></AutoComplete>
          </>
        )}

        {store.request.inherited && (
          <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
            <Label type='secondary'>{t('request.inherit')}</Label>
            <Checkbox
              checked={store.request.inherited}
              onChange={(val) => {
                dispatch((state) => {
                  state.request.inherited = val.target.checked;
                });
              }}
            />
          </div>
        )}

        <div style={{ marginLeft: '8px' }}>
          <Button
            id='arex-request-send-btn'
            type='primary'
            loading={store.response?.type === 'loading'}
            disabled={store.response?.type === 'extensionNotInstalled'}
            icon={
              store.response?.type === 'extensionNotInstalled' ? (
                <ExclamationOutlined />
              ) : (
                <SendOutlined />
              )
            }
            onClick={handleRequest}
          >
            {t('action.send')}
          </Button>
        </div>
      </HeaderWrapper>

      <SCFRequestOptions />

      <InterfaceDetail openModal={openDetail} {...interfaceDetailParam}></InterfaceDetail>
      <InterfaceHistoryList {...InterfaceHistoryListParam}></InterfaceHistoryList>
      <RequestTour />
    </div>
  );
};

export default Request;
