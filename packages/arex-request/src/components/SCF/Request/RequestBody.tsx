import { css, EmptyWrapper } from '@arextest/arex-core';
import { Allotment } from 'allotment';
import { Radio, RadioChangeEvent, Select, Typography } from 'antd';
import jsonBigInt from 'json-bigint';
import React, { useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { ArexContentTypes } from '../../../types';
import RequestBinaryBody from './RequestBinaryBody';
import RequestRawBody, { RequestRawBodyRef } from './RequestRawBody';

const genContentType = (contentType?: string) =>
  contentType?.includes('application/json') ? 'raw' : 'binary';

const bigCateOptions = ['raw', 'binary'];

const rawSmallCateOptions = [
  {
    label: 'JSON',
    value: 'application/json',
    test: <a>JSON</a>,
  },
];

const RequestBody = () => {
  const { t } = useTranslation();
  const { store, dispatch } = useArexRequestStore();

  const rawBodyRef = useRef<RequestRawBodyRef>(null);

  const isJsonContentType = useMemo(() => {
    return ['application/json'].includes(store.request.body.contentType || '');
  }, [store.request.body.contentType]);

  const onChange = (value: ArexContentTypes) => {
    dispatch((state) => {
      state.request.body.contentType = value;
    });
  };

  const handleContentTypeChange = (val: RadioChangeEvent) => {
    if (val.target.value === 'binary') {
      dispatch((state) => {
        // @ts-ignore
        state.request.body.contentType = '0';
        // state.request.body.body = '';
      });
    }
    if (val.target.value === 'raw') {
      dispatch((state) => {
        state.request.body.contentType = 'application/json';
        // state.request.body.body = '';
      });
    }
  };

  useEffect(() => {
    if (!store.request?.body.scfRequest) {
      try {
        const scfContext = jsonBigInt.parse(store.request?.body.scf as string);
        dispatch((state) => {
          state.request.body.scfRequest = scfContext;
        });
      } catch (error) {
        console.warn('SCF context is not valid, ', store.request?.body.scf);
      }
    }
  }, [dispatch, store.request.body.scfRequest]);

  useEffect(() => {
    if (
      store.request.body.contentType?.includes('application/json') &&
      store.request.body.contentType !== 'application/json'
    ) {
      dispatch((state) => {
        state.request.body.contentType = 'application/json';
      });
    }
  }, [store.request.body.contentType]);

  function getParameterCount(methodStr: string | undefined): number {
    if (!methodStr) {
      // console.warn('methodStr is empty');
      return 0;
    }
    const match = methodStr.match(/\((.*?)\)/);
    if (!match || !match[1]) {
      // console.warn('methodStr is not valid, ', methodStr);
      return 0;
    }
    return match[1]
      .split(',')
      .map((param) => param.trim())
      .filter((param) => param.length > 0).length;
  }

  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
      `}
    >
      <div
        css={css`
          display: flex;
          justify-content: space-between;
          margin: 6px 0;
          padding-bottom: 6px;
        `}
      >
        <div></div>
        <a onClick={rawBodyRef?.current?.prettifyRequestBody}>{t('action.prettify')}</a>
      </div>
      {getParameterCount(store.request.body.scfRequest?.methodName ?? '') > 0 ? (
        <Allotment vertical={false}>
          {Array.from({
            length: getParameterCount(store.request.body.scfRequest?.methodName ?? ''),
          }).map((_, index) => {
            const param = store.request?.body?.scfRequest?.params?.[index] || {
              paramKey: '',
              exampleValue: '',
            };
            return (
              <Allotment.Pane key={index}>
                <RequestRawBody item={param} index={index} ref={rawBodyRef} />
              </Allotment.Pane>
            );
          })}
        </Allotment>
      ) : (
        <EmptyWrapper
          description={<Typography.Text type='secondary'>{t('scf.body.noParam')}</Typography.Text>}
        />
      )}
    </div>
  );
};

export default RequestBody;
