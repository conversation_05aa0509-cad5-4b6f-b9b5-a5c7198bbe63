import { css, Theme, useArexCoreConfig } from '@arextest/arex-core';
import { Editor } from '@monaco-editor/react';
import { App, Space, Typography } from 'antd';
import jsonBigInt from 'json-bigint';
import React, { forwardRef, useEffect,useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useArexRequestStore } from '../../../hooks';
import { scfParams } from '../../../types/SCFRequest';

const { Text } = Typography;

export type RequestRawBodyRef = {
  prettifyRequestBody: () => void;
};

export type RequestRawBodyProps = {
  item?: scfParams;
  index?: number;
};

const RequestRawBody = forwardRef<RequestRawBodyRef, RequestRawBodyProps>(
  ({ item, index }, ref) => {
    const { theme } = useArexCoreConfig();
    const { store, dispatch } = useArexRequestStore();
    const [param, setParam] = useState<scfParams | undefined>(item);
    const { message } = App.useApp();
    const { t } = useTranslation();

    // 当 props.item 变化时同步更新本地状态
    useEffect(() => {
      if (item) {
        setParam(item);
      }
    }, [item]);

    const prettifyRequestBody = () => {
      dispatch((state) => {
        const params = state?.request?.body?.scfRequest?.params;
        if (!params || !params[index!]) return;

        try {
          const val = jsonBigInt.stringify(
            jsonBigInt.parse(params[index!].exampleValue as string),
            null,
            2,
          );
          params[index!].exampleValue = val;
        } catch (e) {
          message.error(t('error.json_prettify_invalid_body'));
        }
      });
    };

    useImperativeHandle(ref, () => ({
      prettifyRequestBody,
    }));

    // 将 [emoji:code] 格式转回 emoji 的函数
    const convertBackToEmoji = (obj: any): any => {
      if (typeof obj === 'string') {
        return obj.replace(/\[emoji:([0-9a-f]+)\]/g, (_, code) => {
          return String.fromCodePoint(parseInt(code, 16));
        });
      }
      if (Array.isArray(obj)) {
        return obj.map(convertBackToEmoji);
      }
      if (obj && typeof obj === 'object') {
        const result: any = {};
        for (const key in obj) {
          result[key] = convertBackToEmoji(obj[key]);
        }
        return result;
      }
      return obj;
    };


    // 安全获取当前参数的 exampleValue
    // const currentExampleValue = store.request.body.scfRequest?.params?.[index!]?.exampleValue;
    const currentExampleValue = convertBackToEmoji(store.request.body.scfRequest?.params?.[index!]?.exampleValue);
    const displayValue = currentExampleValue ?? param?.exampleValue ?? '';

    return (
      <div
        css={css`
          height: 100%;
          flex: 1;
          overflow-y: auto;
        `}
      >
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {/* 参数信息展示（可选） */}
          {/* <Space size={30}>
            <Text type='secondary'>参数名: {param?.fieldName || '无'}</Text>
            <Text type='secondary'>参数类型: {param?.paramKey || '无'}</Text>
          </Space> */}
        </div>

        <Editor
          theme={theme === Theme.dark ? 'vs-dark' : 'light'}
          options={{
            minimap: { enabled: false },
            fontSize: 12,
            wordWrap: 'on',
            automaticLayout: true,
            fontFamily: 'IBMPlexMono, "Courier New", monospace',
            scrollBeyondLastLine: false,
          }}
          language={'json'}
          value={displayValue as string}
          onChange={(value) => {
            dispatch((state) => {
              if (value !== undefined && state.request.body.scfRequest?.params) {
                const params = state.request.body.scfRequest.params;
                if (params[index!]) {
                  params[index!].exampleValue = value;
                } else {
                  // 如果 index 超出范围，可以扩展数组并填充默认值
                  params[index!] = {
                    paramKey: `param${index}`,
                    exampleValue: value,
                  };
                }
              }
            });
          }}
        />
      </div>
    );
  },
);

export default RequestRawBody;
