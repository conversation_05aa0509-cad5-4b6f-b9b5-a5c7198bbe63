import { css, SmallBadge, styled } from '@arextest/arex-core';
import { Tabs } from 'antd';
import { FC, useEffect, useMemo, useState } from 'react';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { Tab, TabConfig } from '../../../ArexRequest';
import { useArexRequestProps, useArexRequestStore } from '../../../hooks';
import { ArexRESTRequest, ArexRESTResponse } from '../../../types';
import RequestContract from '../../Request/RequestContract';
import EnvConfig from './EnvConfig';
import PreRequestScript from './PreRequestScript';
import RequestBody from './RequestBody';
import RequestHeaders from './RequestHeaders';
import RequestParameters from './RequestParameters';
import RequestTests from './RequestTests';

const HttpRequestOptionsWrapper = styled.div`
  height: 100%;
  padding: 0 16px 40px;
  flex: 1;
  display: flex;
  flex-direction: column;
  .ant-tabs-content-holder {
    height: 100px;
  }
`;

export interface HttpRequestOptionsProps {
  config?: TabConfig;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
}

const HttpRequestOptions: FC<HttpRequestOptionsProps> = (props) => {
  const { config } = useArexRequestProps();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();
  // const [activeKey, setActiveKey] = useState('Env');
  const [activeKey, setActiveKey] = useState('Env');

  const items = useMemo(() => {
    const _items: Tab[] = [
      {
        label: <SmallBadge offset={[4, 2]}>{t('scf.tab.env')}</SmallBadge>,
        key: 'Env',
        children: <EnvConfig />,
        forceRender: true,
      },
      {
        label: (
          <SmallBadge
            offset={[4, 2]}
            dot={
              !!store.request?.body?.scfRequest?.params &&
              store.request?.body?.scfRequest?.params.length > 0
            }
          >
            {t('scf.tab.param')}
          </SmallBadge>
        ),
        key: 'Parameters',
        children: <RequestBody />,
        forceRender: true,
      },
      {
        label: (
          <SmallBadge count={store.request.body?.scfRequest?.invisibleParams?.length}>
            {t('scf.tab.context')}
          </SmallBadge>
        ),
        key: 'Context',
        children: <RequestHeaders />,
        // forceRender: true,
      },
      // {
      //   label: (
      //     <SmallBadge offset={[4, 2]} dot={!!store.request?.body?.body?.length}>
      //       {t('scf.tab.body')}
      //     </SmallBadge>
      //   ),
      //   key: 'body',
      //   children: <RequestBody />,
      //   forceRender: true,
      // },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!store.request?.preRequestScript?.length}>
            {t('tab.pre_request_script')}
          </SmallBadge>
        ),
        key: 'pre_request_script',
        children: <PreRequestScript />,
        forceRender: true,
      },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!store.request.testScript?.length}>
            {t('tab.tests')}
          </SmallBadge>
        ),
        key: 'tests',
        children: <RequestTests />,
        forceRender: true,
      },
      // 添加参数分析标签页
      {
        label: (
          <SmallBadge
            offset={[4, 2]}
            dot={!!(store.request.contract && store.request.contract.length > 0)}
          >
            {t('参数分析')}
          </SmallBadge>
        ),
        key: 'contract',
        children: <RequestContract onSave={props.onSave} />,
        forceRender: true,
      },
    ];

    // concat extra request tabs
    return _items.concat(config?.requestTabs?.extra?.filter((tab) => !tab.hidden) || []);
  }, [store.request]);

  useEffect(() => {
    if (
      store.request?.body?.scfRequest?.params &&
      store.request.body.scfRequest.params.length > 0
    ) {
      setActiveKey('Parameters');
    }
  }, [dispatch, store.request?.body?.scfRequest?.params]);

  return (
    <HttpRequestOptionsWrapper>
      <Tabs
        activeKey={activeKey}
        items={items}
        onChange={setActiveKey}
        css={css`
          height: 100%;
          .ant-tabs-nav {
            margin-bottom: 0;
          }
        `}
      />
    </HttpRequestOptionsWrapper>
  );
};

export default HttpRequestOptions;
