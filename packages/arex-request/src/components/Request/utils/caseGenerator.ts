import { get, set } from 'lodash';

import { ParamContract } from '../../../types';

// 辅助函数：根据路径获取对象中的值
export const getFieldValue = (obj: any, path: string) => {
  // 使用lodash.get来处理复杂路径，包括数组索引
  return get(obj, path);
};

// 辅助函数：根据路径修改对象中的值
export const modifyField = (obj: any, path: string, newValue: any) => {
  // 使用lodash.set来处理复杂路径，包括数组索引
  return set(obj, path, newValue);
};

// 生成测试用例的函数
export const generateTestCases = (
  originalBody: any,
  params: ParamContract[],
  selectedPaths: string[] = [],
  originalParams?: any, // 新增：URL参数
) => {
  // 生成测试用例
  const cases: { caseDesc: string; caseBody: any; caseParams?: any }[] = [];

  // 判断数据源类型
  const hasBody = originalBody && Object.keys(originalBody).length > 0;
  const hasParams = originalParams && Object.keys(originalParams).length > 0;

  // 添加基础用例（原始请求体和URL参数）
  cases.push({
    caseDesc: '原始请求',
    caseBody: hasBody ? JSON.parse(JSON.stringify(originalBody)) : {},
    caseParams: hasParams ? JSON.parse(JSON.stringify(originalParams)) : {},
  });

  // 递归生成测试用例
  const generateCasesForField = (
    fieldPath: string,
    fieldType: string,
    fieldValue: any,
    isBodyField: boolean, // 新增：标识是否为body字段
  ) => {
    // 创建深拷贝，避免互相影响
    const cloneBody = () => hasBody ? JSON.parse(JSON.stringify(originalBody)) : {};
    const cloneParams = () => hasParams ? JSON.parse(JSON.stringify(originalParams)) : {};

    // 根据字段类型生成不同的测试用例
    switch (fieldType) {
      case 'string':
        // 字符串类型测试用例
        cases.push({
          caseDesc: `${fieldPath} - 非法数字类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, 123) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, 123) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法布尔类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, true) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, true) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法对象类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, {}) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, {}) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数组类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, []) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, []) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - null值`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, null) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, null) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 空字符串`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, '') : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, '') : cloneParams(),
        });
        break;

      case 'number':
        // 数字类型测试用例
        cases.push({
          caseDesc: `${fieldPath} - 非法字符串类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, String(fieldValue)) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, String(fieldValue)) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法布尔类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, true) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, true) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法对象类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, {}) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, {}) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数组类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, []) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, []) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - null值`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, null) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, null) : cloneParams(),
        });

        // 数字特殊值测试
        if (fieldValue >= 0) {
          cases.push({
            caseDesc: `${fieldPath} - 负数`,
            caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, -Math.abs(fieldValue || 1)) : cloneBody(),
            caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, -Math.abs(fieldValue || 1)) : cloneParams(),
          });
        } else {
          cases.push({
            caseDesc: `${fieldPath} - 正数`,
            caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, Math.abs(fieldValue || 1)) : cloneBody(),
            caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, Math.abs(fieldValue || 1)) : cloneParams(),
          });
        }

        cases.push({
          caseDesc: `${fieldPath} - 零值`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, 0) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, 0) : cloneParams(),
        });

        // 如果是整数，添加小数测试
        if (Number.isInteger(fieldValue)) {
          cases.push({
            caseDesc: `${fieldPath} - 小数值`,
            caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, fieldValue + 0.5) : cloneBody(),
            caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, fieldValue + 0.5) : cloneParams(),
          });
        }
        break;

      case 'boolean':
        // 布尔类型测试用例
        cases.push({
          caseDesc: `${fieldPath} - 非法字符串类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, String(fieldValue)) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, String(fieldValue)) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数字类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, 1) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, 1) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法对象类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, {}) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, {}) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数组类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, []) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, []) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - null值`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, null) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, null) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 相反布尔值`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, !fieldValue) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, !fieldValue) : cloneParams(),
        });
        break;

      case 'object':
        // 对象类型测试用例
        cases.push({
          caseDesc: `${fieldPath} - 非法字符串类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, JSON.stringify(fieldValue)) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, JSON.stringify(fieldValue)) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数字类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, 123) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, 123) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法布尔类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, true) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, true) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数组类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, []) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, []) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - null值`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, null) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, null) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 空对象`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, {}) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, {}) : cloneParams(),
        });
        break;

      case 'array':
        // 数组类型测试用例
        cases.push({
          caseDesc: `${fieldPath} - 非法字符串类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, JSON.stringify(fieldValue)) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, JSON.stringify(fieldValue)) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数字类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, 123) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, 123) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法布尔类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, true) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, true) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法对象类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, {}) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, {}) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - null值`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, null) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, null) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 空数组`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, []) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, []) : cloneParams(),
        });
        break;

      case 'null':
        // null类型测试用例
        cases.push({
          caseDesc: `${fieldPath} - 非法字符串类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, 'null') : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, 'null') : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数字类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, 0) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, 0) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法布尔类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, false) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, false) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法对象类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, {}) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, {}) : cloneParams(),
        });
        cases.push({
          caseDesc: `${fieldPath} - 非法数组类型`,
          caseBody: isBodyField ? modifyField(cloneBody(), fieldPath, []) : cloneBody(),
          caseParams: !isBodyField ? modifyField(cloneParams(), fieldPath, []) : cloneParams(),
        });
        break;
    }
  };

  // 根据参数契约生成测试用例
  const traverseParams = (params: ParamContract[], parentPath = '') => {
    params.forEach((param) => {
      const fieldPath = param.key;

      // 判断字段是在body中还是在params中
      let fieldValue;
      let isBodyField = false;

      // 与fetchAdditionalCases保持一致的判断逻辑
      if (hasBody && !hasParams) {
        // 只有body数据
        fieldValue = getFieldValue(originalBody, fieldPath);
        isBodyField = true;
      } else if (!hasBody && hasParams) {
        // 只有URL参数数据
        fieldValue = getFieldValue(originalParams, fieldPath);
        isBodyField = false;
      } else if (hasBody && hasParams) {
        // 两者都有，根据字段是否存在于body中来判断
        const bodyHasField = get(originalBody, fieldPath) !== undefined;
        if (bodyHasField) {
          fieldValue = getFieldValue(originalBody, fieldPath);
          isBodyField = true;
        } else {
          fieldValue = getFieldValue(originalParams, fieldPath);
          isBodyField = false;
        }
      }

      // 只为选中的字段生成测试用例
      if (selectedPaths.length === 0 || selectedPaths.includes(fieldPath)) {
        // 为当前字段生成测试用例
        generateCasesForField(fieldPath, param.type, fieldValue, isBodyField);
      }

      // 递归处理子参数
      if (param.children && param.children.length > 0) {
        traverseParams(param.children, fieldPath);
      }
    });
  };

  traverseParams(params);

  return cases;
};

// 解析请求体的函数
export const parseRequestBody = (requestBody: any) => {
  let originalBody: any = {};

  if (requestBody.contentType && requestBody.body) {
    originalBody =
      typeof requestBody.body === 'string' ? JSON.parse(requestBody.body) : requestBody.body;
  } else if (requestBody.wmb_mock) {
    originalBody =
      typeof requestBody.wmb_mock === 'string'
        ? JSON.parse(requestBody.wmb_mock)
        : requestBody.wmb_mock;
  } else if (requestBody.wmb_Consumer && requestBody.wmb_Consumer.msgBody) {
    originalBody =
      typeof requestBody.wmb_Consumer.msgBody === 'string'
        ? JSON.parse(requestBody.wmb_Consumer.msgBody)
        : requestBody.wmb_Consumer.msgBody;
  } else if (requestBody.scfRequest && requestBody.scfRequest.params) {
    // 处理SCF请求：将params数组转换为可索引的数组格式
    const scfParamsArray: any[] = [];
    requestBody.scfRequest.params.forEach((param: any) => {
      try {
        // 尝试解析exampleValue为JSON对象
        const value = JSON.parse(param.exampleValue as string);
        scfParamsArray.push(value);
      } catch (e) {
        // 如果解析失败，则保留原始字符串
        scfParamsArray.push(param.exampleValue);
      }
    });
    originalBody = scfParamsArray;
  } else if (requestBody.formData && requestBody.formData.length > 0) {
    const formDataObj: Record<string, string> = {};
    requestBody.formData.forEach((item: any) => {
      formDataObj[item.key] = item.value;
    });
    originalBody = formDataObj;
  }

  return originalBody;
};
