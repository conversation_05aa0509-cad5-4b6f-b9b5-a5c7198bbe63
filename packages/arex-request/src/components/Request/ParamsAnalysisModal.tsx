import { getLocalStorage, useTranslation } from '@arextest/arex-core';
import { Alert, Button, Modal, Spin, Tabs } from 'antd';
import * as d3 from 'd3';
import { flamegraph } from 'd3-flame-graph';
import mermaid from 'mermaid';
import React, { FC, useEffect, useRef, useState } from 'react';

import { ArexResponse, ArexRESTReqBody, ArexRESTResponse } from '../../types';

// 定义 SkyWalking Span 结构 (根据你的例子简化)
interface Span {
  traceId: string;
  segmentId: string;
  spanId: number;
  parentSpanId: number;
  serviceCode: string;
  startTime: number;
  endTime: number;
  endpointName: string;
  type: string; // Entry, Exit, Local
  component: string;
  isError: boolean;
  layer: string;
  tags: { key: string; value: string }[];
  logs: any[];
  during: number;
}

// 定义 API 响应结构 (根据你的例子)
interface TraceResponse {
  result?: {
    data?: {
      trace?: {
        spans: Span[];
      };
    };
  };
}

interface ParamsAnalysisModalProps {
  isVisible: boolean;
  onClose: () => void;
  requestBody?: ArexRESTReqBody;
  responseBody?: ArexRESTResponse; // 接收 response 数据
}
// 获取 Token 的辅助函数 (假设与 WMB/Request/index.tsx 中一致)
const getAccessToken = () => {
  const accessToken: string = '83902dca4bdabc18d7987ca28a287aeb';
  return accessToken;
};

// 将 Trace 数据转换为 Mermaid Sequence Diagram
const convertTraceToMermaid = (spans: Span[]): string => {
  if (!spans || spans.length === 0) {
    return 'sequenceDiagram\n  Note over User: No trace data available';
  }

  // 按开始时间排序
  spans.sort((a, b) => a.startTime - b.startTime);

  // 限制处理的 span 数量，避免图表过大
  const maxSpans = 100;
  if (spans.length > maxSpans) {
    console.log(`ParamsAnalysisModal: Limiting spans from ${spans.length} to ${maxSpans}`);
    spans = spans.slice(0, maxSpans);
  }

  let mermaidString = 'sequenceDiagram\n';

  // 优化：使用 Map 存储服务名称，避免重复的长名称
  const serviceNameMap = new Map<string, string>();
  let serviceCounter = 0;

  const participants = new Set<string>();
  const spanMap = new Map<number, Span>();
  const childrenMap = new Map<number, number[]>(); // parentSpanId -> [spanId, spanId, ...]

  // 识别参与者并构建映射
  spans.forEach((span) => {
    // 优化：为长服务名创建简短别名
    let serviceCode = span.serviceCode;
    if (serviceCode.length > 30) {
      if (!serviceNameMap.has(serviceCode)) {
        const shortName = `Service${++serviceCounter}`;
        serviceNameMap.set(serviceCode, shortName);
      }
      serviceCode = serviceNameMap.get(serviceCode) || serviceCode.substring(0, 27) + '...';
    }

    participants.add(serviceCode);
    span.serviceCode = serviceCode; // 更新简化后的服务名称
    spanMap.set(span.spanId, span);
    if (!childrenMap.has(span.parentSpanId)) {
      childrenMap.set(span.parentSpanId, []);
    }
    childrenMap.get(span.parentSpanId)?.push(span.spanId);
  });

  // 添加参与者，优化：按照调用顺序添加参与者
  mermaidString += `  participant User\n`; // 始终将 User 放在第一位

  // 获取根级别的 spans
  const rootSpans = childrenMap.get(-1) || [];
  const orderedServices = new Set<string>(['User']);

  // 递归收集服务，按照调用顺序
  const collectServices = (spanIds: number[]) => {
    spanIds.forEach((spanId) => {
      const span = spanMap.get(spanId);
      if (span && !orderedServices.has(span.serviceCode)) {
        orderedServices.add(span.serviceCode);
      }

      const children = childrenMap.get(spanId) || [];
      collectServices(children);
    });
  };

  collectServices(rootSpans);

  // 按顺序添加参与者
  orderedServices.forEach((service) => {
    if (service !== 'User') {
      mermaidString += `  participant ${service}\n`;
    }
  });

  // 如果有服务名映射，添加注释说明
  if (serviceNameMap.size > 0) {
    mermaidString += `  Note over User: Service name mappings:\n`;
    serviceNameMap.forEach((shortName, longName) => {
      mermaidString += `  Note over User: ${shortName} = ${longName}\n`;
    });
  }

  // 递归构建调用链
  const buildSequence = (parentSpanId: number, level: number = 0, maxLevel: number = 5) => {
    // 限制嵌套层级，避免图表过于复杂
    if (level >= maxLevel) {
      // 优化：添加提示，表明还有更多调用被省略
      const parentSpan = spanMap.get(parentSpanId);
      if (parentSpan) {
        mermaidString += `  Note over ${parentSpan.serviceCode}: 更多嵌套调用被省略...\n`;
      }
      return;
    }

    const childSpanIds = childrenMap.get(parentSpanId) || [];
    // 按时间排序子调用
    childSpanIds.sort(
      (aId, bId) => (spanMap.get(aId)?.startTime || 0) - (spanMap.get(bId)?.startTime || 0),
    );

    // 优化：如果子调用过多，只显示前N个
    const maxChildrenToShow = 20;
    let shownChildren = childSpanIds;
    if (childSpanIds.length > maxChildrenToShow) {
      shownChildren = childSpanIds.slice(0, maxChildrenToShow);
      const parentSpan = spanMap.get(parentSpanId);
      if (parentSpan) {
        mermaidString += `  Note over ${parentSpan.serviceCode}: 显示 ${maxChildrenToShow}/${childSpanIds.length} 个调用\n`;
      }
    }

    shownChildren.forEach((spanId) => {
      const span = spanMap.get(spanId);
      if (!span) return;

      const parentSpan = spanMap.get(span.parentSpanId);
      const caller = parentSpan ? parentSpan.serviceCode : 'User'; // 如果没有父 Span，认为是 User 发起
      const callee = span.serviceCode;

      // 优化：更好地格式化消息文本
      let message = span.endpointName || span.component || 'call';
      // 提取HTTP方法和路径
      const httpMethodMatch = message.match(/^(GET|POST|PUT|DELETE|PATCH|OPTIONS|HEAD)\s+(.+)$/);
      if (httpMethodMatch) {
        const [, method, path] = httpMethodMatch;
        message = `${method} ${path.length > 30 ? path.substring(0, 27) + '...' : path}`;
      } else if (message.length > 40) {
        message = message.substring(0, 37) + '...';
      }

      // 优化：添加耗时信息
      const duration = span.during || span.endTime - span.startTime;
      const durationText = duration < 1000 ? `${duration}ms` : `${(duration / 1000).toFixed(2)}s`;

      // 优化：为错误添加醒目标记
      const errorMark = span.isError ? ' ❌' : '';

      if (caller !== callee) {
        // 只有跨服务调用才显示箭头
        if (parentSpan) {
          // 排除 User 发起的第一次调用
          mermaidString += `  ${caller}->>${callee}: ${message}${errorMark} (${durationText})\n`;
          mermaidString += `  activate ${callee}\n`;
          buildSequence(spanId, level + 1, maxLevel);
          mermaidString += `  ${callee}-->>${caller}: Response\n`;
          mermaidString += `  deactivate ${callee}\n`;
        } else {
          // 处理 User 发起的第一次调用
          mermaidString += `  ${caller}->>${callee}: ${message}${errorMark} (${durationText})\n`;
          mermaidString += `  activate ${callee}\n`;
          buildSequence(spanId, level + 1, maxLevel);
          // 假设入口调用最终会返回给 User
          mermaidString += `  ${callee}-->>${caller}: Response\n`;
          mermaidString += `  deactivate ${callee}\n`;
        }
      } else {
        // 同一个服务内的调用，可以简化或用 note 表示
        // 优化：为内部调用添加更多信息
        mermaidString += `  Note over ${callee}: 内部调用: ${message}${errorMark} (${durationText})\n`;
        buildSequence(spanId, level + 1, maxLevel); // 继续处理子调用
      }
    });
  };

  // 从根 Span (-1) 开始构建
  buildSequence(-1);

  return mermaidString;
};

// 将 Trace 数据转换为火焰图格式
const convertToFlameGraphFormat = (spans: Span[]) => {
  if (!spans || spans.length === 0) {
    return { name: 'root', value: 0, children: [] };
  }

  // 按开始时间排序
  spans.sort((a, b) => a.startTime - b.startTime);

  // 构建 span ID 到 span 的映射
  const spanMap = new Map();
  spans.forEach((span) => {
    spanMap.set(span.spanId, span);
  });

  // 构建父子关系树
  const childrenMap = new Map();
  spans.forEach((span) => {
    if (!childrenMap.has(span.parentSpanId)) {
      childrenMap.set(span.parentSpanId, []);
    }
    childrenMap.get(span.parentSpanId).push(span.spanId);
  });

  // 递归构建火焰图数据
  // 定义火焰图节点类型
  interface FlameNode {
    name: string;
    value: number;
    children?: FlameNode[];
    span?: Span;
  }

  const buildFlameNode = (spanId: number): FlameNode | null => {
    const span = spanMap.get(spanId);
    if (!span) return null;

    const duration = span.during || (span.endTime - span.startTime);
    const children: FlameNode[] = []; // 使用明确的类型

    // 处理子 span
    const childIds = childrenMap.get(spanId) || [];
    childIds.forEach((childId: number) => {
      const childNode = buildFlameNode(childId);
      if (childNode) {
        children.push(childNode);
      }
    });

    return {
      name: `${span.serviceCode}: ${span.endpointName || 'unknown'}`,
      value: duration,
      children: children.length > 0 ? children : undefined,
      span: span // 保存原始 span 数据用于展示详情
    };
  };

  // 从根 span 开始构建
  const rootSpanIds = childrenMap.get(-1) || [];
  const rootNode: FlameNode = {
    name: 'Request',
    value: 0,
    children: [],
  };

  // 计算总持续时间
  let totalDuration = 0;
  rootSpanIds.forEach((rootSpanId: number) => {
    const node = buildFlameNode(rootSpanId);
    if (node) {
      rootNode.children!.push(node); // 使用非空断言，因为我们已经初始化了 children 数组
      totalDuration += node.value;
    }
  });

  rootNode.value = totalDuration;
  return rootNode;
};

const ParamsAnalysisModal: FC<ParamsAnalysisModalProps> = ({
  isVisible,
  onClose,
  requestBody,
  responseBody, // 接收 response
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [mermaidString, setMermaidString] = useState<string | null>(null);
  const [flameData, setFlameData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('flame'); // 修改默认值为 'flame'，原来是 'sequence'
  const mermaidRef = useRef<HTMLDivElement>(null);
  const flameGraphRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    mermaid.initialize({ startOnLoad: false });
  }, []);

  useEffect(() => {
    const fetchTraceData = async () => {
      setLoading(true);
      setError(null);
      setMermaidString(null);
      setFlameData(null);

      // 1. 获取 traceId (arex-record-id)
      let traceId: string | undefined;
      if (responseBody?.headers) {
        const recordIdHeader = responseBody.headers.find(
          (header) => header.key.toLowerCase() === 'arex-record-id',
        );
        traceId = recordIdHeader?.value;
        console.log('ParamsAnalysisModal: traceId from header:', traceId);
      } else {
        console.log('ParamsAnalysisModal: responseBody or headers not found');
      }

      // 先写死 (for debugging)
      traceId = 'dc0e958fea954b6ea26cbcdf1a643a28.120.17458264200141883';
      console.log('ParamsAnalysisModal: Using hardcoded traceId:', traceId);

      if (!traceId) {
        setError(t('智能分析需要有效的 traceId (arex-record-id)'));
        setLoading(false);
        console.error('ParamsAnalysisModal: No traceId found');
        return;
      }

      // 2. 获取 token
      const token = getAccessToken();
      console.log('ParamsAnalysisModal: Access token:', token ? 'found' : 'not found');
      if (!token) {
        console.warn('Access token not found for trace analysis');
      }

      // 3. 发送请求
      const formData = new FormData();
      formData.append('traceId', traceId);
      if (token) {
        formData.append('token', token);
      }
      console.log(
        'ParamsAnalysisModal: Sending request to /wtrace/manager/openapi/wtrace-trace-info',
      );

      try {
        const response = await fetch('/wtrace/manager/openapi/wtrace-trace-info', {
          method: 'POST',
          body: formData,
        });
        console.log('ParamsAnalysisModal: Fetch response status:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: TraceResponse = await response.json();
        console.log('ParamsAnalysisModal: Received trace data:', data);

        // 4. 转换并设置数据
        const spans = data.result?.data?.trace?.spans;
        if (spans && spans.length > 0) {
          console.log('ParamsAnalysisModal: Converting spans to visualization formats');

          // 生成 Mermaid 序列图
          const diagram = convertTraceToMermaid(spans);
          console.log('ParamsAnalysisModal: Generated Mermaid string:', diagram);
          setMermaidString(diagram);

          // 生成火焰图数据
          const flameGraphData = convertToFlameGraphFormat(spans);
          console.log('ParamsAnalysisModal: Generated flame graph data:', flameGraphData);
          setFlameData(flameGraphData);
        } else {
          console.log('ParamsAnalysisModal: No spans found in trace data');
          setMermaidString('sequenceDiagram\n  Note over User: No spans found in trace data');
          setFlameData(null);
        }
      } catch (err: any) {
        console.error('ParamsAnalysisModal: Failed to fetch or process trace data:', err);
        setError(t('获取或处理链路数据失败') + `: ${err.message}`);
      } finally {
        console.log('ParamsAnalysisModal: fetchTraceData finished');
        setLoading(false);
      }
    };

    if (isVisible) {
      console.log('ParamsAnalysisModal: Modal became visible, triggering fetchTraceData');
      fetchTraceData();
    }
  }, [isVisible, responseBody, t]); // 依赖 isVisible 和 responseBody

  useEffect(() => {
    mermaid.initialize({
      startOnLoad: false,
      // 增加最大文本大小限制
      maxTextSize: 500000,
      // 简化渲染
      securityLevel: 'loose',
      // 调整图表样式
      sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35,
        mirrorActors: false,
        // 优化：添加更多样式配置
        actorMargin: 120, // 增加参与者之间的距离
        bottomMarginAdj: 10, // 底部边距调整
        useMaxWidth: true, // 使用最大宽度
        rightAngles: false, // 使用斜线而非直角
        wrap: true, // 允许文本换行
        messageAlign: 'center', // 消息文本居中
      },
    });
  }, []);

  // 使用 useEffect 来渲染 Mermaid 图
  useEffect(() => {
    if (mermaidString && mermaidRef.current && activeTab === 'sequence') {
      const currentRef = mermaidRef.current; // Capture ref value
      console.log('ParamsAnalysisModal: Mermaid ref found, attempting to render.');
      currentRef.innerHTML = ''; // 清空之前的渲染

      // 创建一个唯一ID的容器元素
      const containerId = `mermaid-container-${Date.now()}`;
      const containerDiv = document.createElement('div');
      containerDiv.id = containerId;
      currentRef.appendChild(containerDiv);

      // 延迟渲染，确保 DOM 准备好
      const timerId = setTimeout(() => {
        console.log('ParamsAnalysisModal: setTimeout callback executing. currentRef:', currentRef);
        try {
          // 确保 ref 仍然存在
          if (currentRef && document.getElementById(containerId)) {
            console.log('ParamsAnalysisModal: Calling mermaid.render with id: mermaid-graph');
            mermaid
              .render('mermaid-graph', mermaidString)
              .then(({ svg, bindFunctions }) => {
                console.log('ParamsAnalysisModal: mermaid.render promise resolved.');
                // 再次检查 ref 是否在回调执行时仍然存在
                const container = document.getElementById(containerId);
                if (container) {
                  console.log('ParamsAnalysisModal: Setting innerHTML with SVG code.');
                  container.innerHTML = svg;
                  // 如果有绑定函数，执行它们
                  if (bindFunctions) {
                    console.log('ParamsAnalysisModal: Executing bindFunctions.');
                    bindFunctions(container);
                  }
                } else {
                  console.warn('ParamsAnalysisModal: mermaid.render callback - container is null.');
                }
              })
              .catch((error) => {
                console.error('ParamsAnalysisModal: Mermaid rendering error:', error);
                setError(t('Mermaid 图渲染失败'));
                // 显示原始代码以便调试
                if (document.getElementById(containerId)) {
                  document.getElementById(containerId)!.innerHTML = `<pre>${mermaidString}</pre>`;
                }
              });
          } else {
            console.warn('ParamsAnalysisModal: setTimeout callback - container is null.');
          }
        } catch (e) {
          console.error('ParamsAnalysisModal: Mermaid rendering error inside setTimeout:', e);
          setError(t('Mermaid 图渲染失败'));
          // 确保 ref 仍然存在
          if (currentRef) {
            console.log('ParamsAnalysisModal: Displaying raw Mermaid string due to error.');
            currentRef.innerHTML = `<pre>${mermaidString}</pre>`; // 显示原始代码以便调试
          }
        }
      }, 0); // 延迟 0ms

      // 清理函数，以防组件在 setTimeout 执行前卸载
      return () => {
        console.log('ParamsAnalysisModal: Clearing setTimeout timerId:', timerId);
        clearTimeout(timerId);
      };
    } else if (mermaidRef.current && activeTab === 'sequence') {
      console.log(
        'ParamsAnalysisModal: mermaidString is null or empty, clearing mermaid container.',
      );
      mermaidRef.current.innerHTML = ''; // 清空内容
    }
  }, [mermaidString, activeTab, t]); // 依赖 mermaidString 和 activeTab

  // 渲染火焰图
  useEffect(() => {
    if (flameData && flameGraphRef.current && activeTab === 'flame') {
      console.log('ParamsAnalysisModal: Rendering flame graph');

      // 清空容器
      flameGraphRef.current.innerHTML = '';

      try {
        console.log('Creating flamegraph with API:', Object.keys(flamegraph()));

        // 创建自定义颜色方案
        const colorHue = d3.scaleLinear().domain([0, 1]).range([10, 350]); // 从红色到蓝紫色的色谱

        // 创建火焰图
        const chart = flamegraph()
          .width(900)
          .height(400)
          .tooltip(true)
          .inverted(true) // 倒置火焰图，根节点在顶部
          .cellHeight(24) // 增加单元格高度，使文本更清晰
          .transitionDuration(750)
          .minFrameSize(1)
          .title('');

        // 使用类型断言添加不在类型定义中的方法
        (chart as any).color((d: any, i: number) => {
          // 根据深度生成不同的颜色
          const depth = d.depth || 0;
          const maxDepth = 20; // 假设最大深度
          const hue = colorHue(depth / maxDepth);
          return d3.hsl(hue, 0.8, 0.8); // 使用 HSL 颜色空间
        });

        (chart as any).label((d: any) => {
          const name = d.data.name || '';
          // 如果有时间信息，添加到标签中
          if (d.data.value) {
            const duration = d.data.value;
            const durationText =
              duration < 1000 ? `${duration.toFixed(0)}ms` : `${(duration / 1000).toFixed(2)}s`;
            return `${name} (${durationText})`;
          }
          return name;
        });

        (chart as any).onClick((d: any) => {
          // 点击显示 span 详情
          if (d.data.span) {
            console.log('Span details:', d.data.span);
            // 这里可以添加显示详情的逻辑，比如弹出一个 Modal
            alert(`服务: ${d.data.span.serviceCode}\n端点: ${d.data.span.endpointName}\n耗时: ${d.data.value}ms`);
          }
        });
        console.log('Chart created successfully');

        // 渲染火焰图
        d3.select(flameGraphRef.current).datum(flameData).call(chart);

        console.log('ParamsAnalysisModal: Flame graph rendered successfully');
      } catch (error) {
        console.error('详细错误信息:', error);
        setError(`火焰图渲染失败: ${error}`);

        // 显示错误信息
        if (flameGraphRef.current) {
          flameGraphRef.current.innerHTML = `<div class="flame-error">${t(
            '火焰图渲染失败',
          )}: ${error}</div>`;
        }
      }
    }
  }, [flameData, activeTab, t]);

  return (
    <Modal
      title={t('智能分析结果')}
      open={isVisible}
      onCancel={onClose}
      width={1000} // 适当增大宽度以容纳图表
      footer={[
        <Button key='close' onClick={onClose}>
          {t('关闭')}
        </Button>,
      ]}
      // 使用 styles.body 替代 bodyStyle
      styles={{
        body: { maxHeight: '70vh', overflowY: 'auto', position: 'relative' }, // 添加 position: relative 以便 Spin 正确定位
      }}
    >
      {/* 将 Spin 包裹住内容区域，以修复 tip 警告 */}
      <Spin spinning={loading} tip={t('加载链路数据中...')}>
        {error && <Alert message={error} type='error' showIcon style={{ marginBottom: '16px' }} />}

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'flame',
              label: t('火焰图'),
              children: (
                <div>
                  <div className='flame-graph-info' style={{ marginBottom: '10px' }}>
                    {t('火焰图说明')}:{' '}
                    {t('每个块代表一个调用，宽度表示耗时，颜色深浅表示调用层级。点击可查看详情。')}
                  </div>
                  <div
                    ref={flameGraphRef}
                    className='flame-graph-container'
                    style={{ width: '100%', height: '400px' }}
                  >
                    {/* 火焰图将在这里渲染 */}
                  </div>
                </div>
              ),
            },
            {
              key: 'sequence',
              label: t('序列图'),
              children: (
                <div ref={mermaidRef} className='mermaid-container'>
                  {/* Mermaid 序列图将在这里渲染 */}
                </div>
              ),
            },
          ]}
        />

        {/* 添加内联样式，以防 CSS 文件导入问题 */}
        <style>{`
           .mermaid-container svg {
             max-width: 100%;
             height: auto;
             display: block; /* 防止 SVG 底部产生额外空白 */
             margin: 0 auto; /* 居中显示 */
           }
           .mermaid-container pre { /* 错误时显示代码的样式 */
             white-space: pre-wrap;
             word-break: break-all;
             background-color: #f5f5f5;
             padding: 10px;
             border-radius: 4px;
           }
           .flame-graph-container {
             border: 1px solid #e8e8e8;
             border-radius: 4px;
             padding: 10px;
             background-color: #fff;
           }
           .d3-flame-graph rect {
             stroke: #EEEEEE;
             fill-opacity: .8;
           }
           .d3-flame-graph rect:hover {
             stroke: #474747;
             stroke-width: 1.5;
             cursor: pointer;
             fill-opacity: 1;
           }
           .d3-flame-graph-label {
             pointer-events: none;
             white-space: nowrap;
             text-overflow: ellipsis;
             overflow: hidden;
             font-size: 12px;
             font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
             margin-left: 4px;
             margin-right: 4px;
             line-height: 1.5;
             padding: 0 0 0;
             font-weight: 400;
             color: #333;
             text-align: left;
           }
           .d3-flame-graph .fade {
             opacity: 0.6 !important;
           }
           .d3-flame-graph .title {
             font-size: 20px;
             font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
             margin-left: 30px;
             margin-right: 30px;
             line-height: 1.5;
             padding: 0 0 0;
             font-weight: 400;
             color: black;
             text-align: left;
           }
           .flame-graph-container {
             border: 1px solid #e8e8e8;
             border-radius: 4px;
             padding: 16px;
             background-color: #fff;
             box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
           }
           .flame-graph-info {
             color: #666;
             font-size: 13px;
             margin-bottom: 12px;
             padding: 8px 12px;
             background-color: #f5f5f5;
             border-radius: 4px;
           }
         `}</style>
      </Spin>
    </Modal>
  );
};

export default ParamsAnalysisModal;
