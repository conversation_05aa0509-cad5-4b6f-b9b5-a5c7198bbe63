import { ExclamationOutlined, RobotOutlined, SendOutlined } from '@ant-design/icons';
import { css, getLocalStorage, Label, RequestMethod, useTranslation } from '@arextest/arex-core';
import { Button, Checkbox, Select, Tooltip } from 'antd';
import PM from 'postman-collection';
import React, { FC, useEffect, useRef, useState } from 'react';

import { addUserAction, sendRequest, UserActionEnum } from '../../helpers';
import { useArexRequestProps, useArexRequestStore } from '../../hooks';
import { ArexEnvironment, ArexRESTParam, ArexRESTRequest, ArexRESTResponse } from '../../types';
import { EnvironmentSelectProps } from '../NavigationBar/EnvironmentSelect';
import { InfoSummaryProps } from '../NavigationBar/InfoSummary';
import EnvInput from './EnvInput';
import { useRequestHandler } from './hooks/useRequestHandler';
import ParamsAnalysisModal from './ParamsAnalysisModal';
import HttpRequestOptions from './RequestOptions';
import { HeaderWrapper, SmartIconWrapper } from './styles';

const greyConfig = [
  'qinsisheng',
  'lihongna01',
  'xutingting08',
  'sunxiaoming01',
  'wangyongli',
  'liulingfeng01',
  'shaoxiang01',
  'cuidemin',
  'weijingjing02',
  'mamengmeng01',
  'chenghuan02',
  'liwenfeng01',
  'shenyalan',
  'liuwenjing04',
  'jiangyuqi01',
  'fujiaxin',
  'lichong13',
  'GUEST_BpHTc_mTarnfHrH',
];

export type RequestProps = {
  disableSave?: boolean;
  onBeforeRequest?: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest;
  onRequest?: (
    reqData: { request: ArexRESTRequest; environment?: ArexEnvironment },
    resData: Awaited<ReturnType<typeof sendRequest>>,
  ) => void;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
  onSaveAs?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
} & InfoSummaryProps & {
    environmentProps?: EnvironmentSelectProps;
  };

const Request: FC<RequestProps> = (props) => {
  const {
    onBeforeRequest = (request: ArexRESTRequest) => request,
    onRequest,
    onSave,
  } = useArexRequestProps();
  const { store, dispatch } = useArexRequestStore();
  const { t } = useTranslation();

  const [endpointStatus, setEndpointStatus] = useState<'error'>();
  const [endpointUrl, setEndpointUrl] = useState<string>('');
  const [showSmartIcon, setShowSmartIcon] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const iconTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 使用自定义hook处理请求相关逻辑
  const { handleRequest, handleUrl } = useRequestHandler({
    store,
    dispatch,
    onBeforeRequest,
    onRequest,
    setEndpointStatus,
    setShowSmartIcon,
    iconTimerRef,
  });

  // 处理图标点击事件
  const handleIconClick = () => {
    // 清除自动隐藏的定时器
    if (iconTimerRef.current) {
      clearTimeout(iconTimerRef.current);
      iconTimerRef.current = null;
    }

    // 直接使用请求体，不再需要在这里提取参数
    setIsModalVisible(true);
    setShowSmartIcon(false);
  };

  // 处理模态框关闭
  const handleModalClose = () => {
    setIsModalVisible(false);

    // 确保模态框关闭后，如果有参数分析数据，更新到store中
    if (store.request.contract && store.request.contract.length > 0) {
      dispatch((state) => {
        state.request.contract = [...(store.request.contract || [])];
      });
    }
  };

  useEffect(() => {
    if (store.request.needUpdate) {
      const url = handleUrl(store.request.endpoint, store.request.params);
      setEndpointUrl(url);
    }
    dispatch((state) => {
      state.request.needUpdate = true;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [store.request.params]);

  return (
    <div style={{ height: '100%' }}>
      <HeaderWrapper>
        <Select
          disabled={store?.request?.inherited}
          css={css`
            width: 80px;
            transform: translateX(1px);
          `}
          value={store?.request?.inherited ? store.request.inheritedMethod : store?.request?.method}
          options={RequestMethod.map((i: string) => ({ value: i, label: i }))}
          onChange={(value) => {
            dispatch((state) => {
              state.request.method = value;
            });
          }}
        />

        <EnvInput
          disabled={store?.request?.inherited}
          status={endpointStatus}
          value={
            store?.request?.inherited ? store.request.inheritedEndpoint : store.request.endpoint
          }
          onChange={(v) => {
            dispatch((state) => {
              state.request.endpoint = v || '';
            });
          }}
        />

        {store.request?.inherited && (
          <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
            <Label type='secondary'>{t('request.inherit')}</Label>
            <Checkbox
              checked={store.request.inherited}
              onChange={(val) => {
                dispatch((state) => {
                  state.request.inherited = val.target.checked;
                });
              }}
            />
          </div>
        )}

        <div style={{ marginLeft: '8px' }}>
          <Button
            id='arex-request-send-btn'
            type='primary'
            loading={store.response?.type === 'loading' || (store.response?.type === 'streaming' && !store.response?.isComplete)}
            disabled={store.response?.type === 'extensionNotInstalled'}
            icon={
              store.response?.type === 'extensionNotInstalled' ? (
                <ExclamationOutlined />
              ) : (
                <SendOutlined />
              )
            }
            onClick={handleRequest}
          >
            {store.response?.type === 'streaming' && !store.response?.isComplete
              ? t('Streaming...')
              : t('action.send')
            }
          </Button>
        </div>
      </HeaderWrapper>

      <HttpRequestOptions onSave={props.onSave} />

      {/* 智能图标 - 使用导入的样式组件 */}
      {showSmartIcon && greyConfig.includes(getLocalStorage('email') || '') && (
        <Tooltip title={t('智能分析')}>
          <SmartIconWrapper onClick={handleIconClick}>
            <RobotOutlined style={{ fontSize: 20 }} />
          </SmartIconWrapper>
        </Tooltip>
      )}

      {/* 添加参数分析模态框 */}
      <ParamsAnalysisModal
        isVisible={isModalVisible}
        onClose={handleModalClose}
        requestBody={store.request.body}
        responseBody={store.response}
      />
    </div>
  );
};

export default Request;
