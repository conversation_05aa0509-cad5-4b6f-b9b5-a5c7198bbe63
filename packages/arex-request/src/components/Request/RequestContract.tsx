import { HelpTooltip, useTranslation } from '@arextest/arex-core';
import { Button, Input, Select, Spin, Table } from 'antd';
import React, { FC, useEffect, useState } from 'react';

import { useArexRequestProps, useArexRequestStore } from '../../hooks';
import { ArexRESTRequest, ArexRESTResponse } from '../../types';
import { ParamContract } from '../../types';
import TestCasesModal from './components/TestCasesModal';
import { useParamsExtractor } from './hooks/useParamsExtractor';
import { useTestCaseGenerator } from './hooks/useTestCaseGenerator';

export const restoreDescriptions = (params: ParamContract[]): ParamContract[] => {
  // 直接返回参数，因为desc字段已经包含在contract数据中
  return params;
};

interface RequestContractProps {
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
}

const RequestContract: FC<RequestContractProps> = ({ onSave }) => {
  const { t } = useTranslation();
  const { store, dispatch } = useArexRequestStore();
  const { getCollections } = useArexRequestProps(); // 从props中获取getCollections
  const [searchText, setSearchText] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [typeFilter, setTypeFilter] = useState<string[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  // 添加选中行的状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 添加参数提取器
  const { extractParamsFromBody } = useParamsExtractor();

  // 使用测试用例生成器钩子，传入选中的行，并接收 streamingData
  const {
    isModalVisible,
    generatedCases,
    handleGenCases: originalHandleGenCases,
    closeModal,
    isLoadingApi,
    streamingData, // <--- 接收 streamingData
  } = useTestCaseGenerator(selectedRowKeys);

  // 修改这个方法，检查是否需要先保存
  const handleGenCases = () => {
    // 检查请求ID是否有效（长度为24的ID表示已保存的请求）
    if (!store.request.id || store.request.id.length !== 24) {
      // 如果请求未保存，显示提示并调用保存
      window.message.warning(t('请先保存用例，再生成测试用例'));
      onSave?.(store.request, store.response); // 调用保存方法
      return; // 不继续生成测试用例
    }

    // 如果请求已保存，则直接生成测试用例
    originalHandleGenCases();
  };

  // 处理描述更新
  const handleDescChange = (value: string, record: ParamContract) => {
    const updateParams = (params: ParamContract[]): ParamContract[] => {
      return params.map((param) => {
        if (param.key === record.key) {
          return { ...param, desc: value };
        }
        if (param.children) {
          return {
            ...param,
            children: updateParams(param.children),
          };
        }
        return param;
      });
    };

    dispatch((state) => {
      state.request.contract = updateParams(state.request.contract || []);
      state.edited = true;
    });
  };

  // 添加表格变化处理函数
  const handleTableChange = (pagination: any, filters: any) => {
    if (filters.type) {
      setTypeFilter(filters.type);
    }
  };

  // 添加递归搜索函数
  const filterBySearchText = (record: ParamContract, searchText: string): boolean => {
    // 检查当前参数名是否匹配
    if (record.name.toLowerCase().includes(searchText.toLowerCase())) {
      return true;
    }

    // 检查完整路径是否匹配
    const fullPath = record.parentKey ? `${record.parentKey}.${record.name}` : record.name;
    if (fullPath.toLowerCase().includes(searchText.toLowerCase())) {
      return true;
    }

    // 递归检查子参数
    if (record.children && record.children.length > 0) {
      return record.children.some((child) => filterBySearchText(child, searchText));
    }

    return false;
  };

  // 添加展开所有父节点的函数
  const getExpandedRowKeys = (list: ParamContract[], searchText: string): string[] => {
    const keys: string[] = [];

    const traverse = (items: ParamContract[]) => {
      items.forEach((item) => {
        if (item.children && item.children.length > 0) {
          // 如果子节点中有匹配的，或者当前节点匹配，则展开当前节点
          const hasMatch = item.children.some(
            (child) =>
              filterBySearchText(child, searchText) ||
              child.name.toLowerCase().includes(searchText.toLowerCase()),
          );

          if (hasMatch) {
            keys.push(item.key);
          }

          traverse(item.children);
        }
      });
    };

    traverse(list);
    return keys;
  };

  // 修改递归搜索函数，增加对数据的过滤功能
  const filterDataBySearchText = (list: ParamContract[], searchText: string): ParamContract[] => {
    if (!searchText) return list;

    return list.reduce<ParamContract[]>((filtered, item) => {
      // 检查当前项是否匹配
      const fullPath = item.parentKey ? `${item.parentKey}.${item.name}` : item.name;
      const currentMatches = fullPath.toLowerCase().includes(searchText.toLowerCase());

      // 递归处理子项
      let filteredChildren: ParamContract[] | undefined = undefined;
      if (item.children && item.children.length > 0) {
        filteredChildren = filterDataBySearchText(item.children, searchText);
      }

      // 如果当前项匹配或者有匹配的子项，则保留
      if (currentMatches || (filteredChildren && filteredChildren.length > 0)) {
        return [
          ...filtered,
          {
            ...item,
            children: filteredChildren,
          },
        ];
      }

      return filtered;
    }, []);
  };

  // 处理展开/收起行的变化
  const handleExpandedRowsChange = (expandedRows: readonly React.Key[]) => {
    setExpandedRowKeys(expandedRows as React.Key[]);
  };

  // 添加自动提取参数的函数
  const extractParams = () => {
    if (!store.request.contract || store.request.contract.length === 0) {
      try {
        let requestBody = store.request.body;

        // 检测SCF请求：requestType为'SCF'或者requestType未定义但存在scfRequest数据
        const isSCFRequest =
          store.request.requestType === 'SCF' ||
          (!store.request.requestType && requestBody?.scfRequest);

        if (
          isSCFRequest &&
          requestBody?.scf &&
          (!requestBody.scfRequest ||
            !requestBody.scfRequest.params ||
            requestBody.scfRequest.params.length === 0)
        ) {
          try {
            const scfInfo = JSON.parse(requestBody.scf);
            // 创建一个新的requestBody副本，包含解析后的scfRequest
            requestBody = {
              ...requestBody,
              scfRequest: scfInfo,
            };
          } catch (parseError) {
            console.error('解析SCF数据失败:', parseError);
          }
        }

        // 传入URL查询参数，使用检测到的请求类型
        const finalRequestType = isSCFRequest ? 'SCF' : store.request.requestType;
        const params = extractParamsFromBody(requestBody, finalRequestType, store.request.params);

        if (params && params.length > 0) {
          dispatch((state) => {
            state.request.contract = params;
            state.edited = true;
          });
        }
      } catch (error) {
        console.error('自动提取参数失败:', error);
      }
    }
  };

  // 组件挂载和请求体变化时自动提取参数
  useEffect(() => {
    extractParams();
  }, [store.request.body]);

  // 当搜索文本变化时，更新展开的行
  useEffect(() => {
    if (searchText && store.request.contract) {
      setExpandedRowKeys(getExpandedRowKeys(store.request.contract, searchText));
    }
  }, [searchText, store.request.contract]);

  // 修改组件挂载和请求体变化时自动提取参数的逻辑
  useEffect(() => {
    if (store.request.body && (!store.request.contract || store.request.contract.length === 0)) {
      try {
        let requestBody = store.request.body;

        // 检测SCF请求：requestType为'SCF'或者requestType未定义但存在scfRequest数据
        const isSCFRequest =
          store.request.requestType === 'SCF' ||
          (!store.request.requestType && requestBody?.scfRequest);

        if (
          isSCFRequest &&
          requestBody?.scf &&
          (!requestBody.scfRequest ||
            !requestBody.scfRequest.params ||
            requestBody.scfRequest.params.length === 0)
        ) {
          try {
            const scfInfo = JSON.parse(requestBody.scf);
            // 创建一个新的requestBody副本，包含解析后的scfRequest
            requestBody = {
              ...requestBody,
              scfRequest: scfInfo,
            };
          } catch (parseError) {
            console.error('解析SCF数据失败:', parseError);
          }
        }

        // 使用检测到的请求类型
        const finalRequestType = isSCFRequest ? 'SCF' : store.request.requestType;
        const params = extractParamsFromBody(requestBody, finalRequestType);
        if (params && params.length > 0) {
          // 恢复保存的描述信息
          const paramsWithDesc = restoreDescriptions(params);
          dispatch((state) => {
            state.request.contract = paramsWithDesc;
            state.edited = true;
          });
        }
      } catch (error) {
        console.error('自动提取参数失败:', error);
      }
    }
  }, [store.request.body, store.request.id]);

  // 根据搜索文本过滤数据
  const filteredData =
    searchText && store.request.contract
      ? filterDataBySearchText(store.request.contract, searchText)
      : store.request.contract || [];

  // 修改handleAnalyze函数
  const handleAnalyze = () => {
    console.log('handleAnalyze开始 - requestType:', store.request.requestType);
    console.log('handleAnalyze开始 - body:', store.request.body);
    console.log('handleAnalyze开始 - params:', store.request.params);

    try {
      let requestBody = store.request.body;

      // 检测SCF请求：requestType为'SCF'或者requestType未定义但存在scfRequest数据
      const isSCFRequest =
        store.request.requestType === 'SCF' ||
        (!store.request.requestType && requestBody?.scfRequest);

      if (isSCFRequest) {
        console.log('SCF请求检测 - scf存在:', !!requestBody?.scf);
        console.log('SCF请求检测 - scfRequest存在:', !!requestBody?.scfRequest);
        console.log('SCF请求检测 - scfRequest.params:', requestBody?.scfRequest?.params);

        // 如果requestType未定义但检测到SCF数据，设置requestType
        if (!store.request.requestType && requestBody?.scfRequest) {
          console.log('检测到SCF数据，设置requestType为SCF');
          dispatch((state) => {
            state.request.requestType = 'SCF';
          });
        }

        if (
          requestBody?.scf &&
          (!requestBody.scfRequest ||
            !requestBody.scfRequest.params ||
            requestBody.scfRequest.params.length === 0)
        ) {
          try {
            console.log('开始解析SCF数据:', requestBody.scf);
            const scfInfo = JSON.parse(requestBody.scf);
            console.log('SCF解析结果:', scfInfo);

            // 创建一个新的requestBody副本，包含解析后的scfRequest
            requestBody = {
              ...requestBody,
              scfRequest: scfInfo,
            };
            console.log('更新后的requestBody.scfRequest:', requestBody.scfRequest);
          } catch (parseError) {
            console.error('解析SCF数据失败:', parseError);
          }
        }
      }

      console.log('调用extractParamsFromBody前 - requestBody:', requestBody);
      console.log('调用extractParamsFromBody前 - requestType:', store.request.requestType);
      console.log('调用extractParamsFromBody前 - queryParams:', store.request.params);

      // 传入URL查询参数，使用检测到的请求类型
      const finalRequestType = isSCFRequest ? 'SCF' : store.request.requestType;
      console.log('最终使用的requestType:', finalRequestType);
      const params = extractParamsFromBody(requestBody, finalRequestType, store.request.params);

      console.log('extractParamsFromBody返回结果:', params);

      if (params && params.length > 0) {
        // 恢复保存的描述信息
        const paramsWithDesc = restoreDescriptions(params);
        dispatch((state) => {
          state.request.contract = paramsWithDesc;
          state.edited = true;
        });
        window.message.success(t('参数分析完成'));
      } else {
        console.log('未检测到有效参数 - params为空或长度为0');
        window.message.warning(t('未检测到有效的参数'));
      }
    } catch (error) {
      console.error('自动提取参数失败:', error);
      window.message.warning(t('参数分析失败'));
    }
  };

  // 添加行选择变化的处理函数
  const handleRowSelectionChange = (selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column', padding: '4px 0' }}>
      {store.request.contract && store.request.contract.length > 0 ? (
        <>
          <div
            style={{
              marginBottom: 4,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Input.Search
              placeholder={'搜索参数名或路径'}
              allowClear
              style={{ width: 280 }}
              onSearch={(value) => setSearchText(value)}
              onChange={(e) => setSearchText(e.target.value)}
              size='small'
            />
            <div>
              <Button
                type='primary'
                size='small'
                onClick={handleAnalyze}
                style={{ marginRight: '8px' }}
              >
                {t('刷新参数')}
              </Button>
              <Button
                type='primary'
                size='small'
                onClick={handleGenCases}
                disabled={selectedRowKeys.length === 0 || isLoadingApi}
                loading={isLoadingApi}
              >
                {isLoadingApi ? t('生成中...') : t('生成用例')}
              </Button>
            </div>
          </div>
          <div style={{ flex: 1, overflow: 'auto', marginTop: 2 }}>
            <Table
              dataSource={filteredData}
              pagination={{
                pageSize: pageSize,
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                showQuickJumper: true,
                showTotal: (total) => `${total}${t('项')}`,
                position: ['bottomRight'],
                onShowSizeChange: (current, size) => setPageSize(size),
                size: 'small',
              }}
              expandable={{
                expandedRowKeys: expandedRowKeys,
                onExpandedRowsChange: handleExpandedRowsChange,
              }}
              rowClassName={(record) =>
                record.children && record.children.length > 0 ? 'parent-row' : ''
              }
              rowSelection={{
                selectedRowKeys,
                onChange: handleRowSelectionChange,
                checkStrictly: true,
              }}
              size='small'
              bordered
              scroll={{ x: 'max-content', y: 'calc(100vh - 200px)' }}
              onChange={handleTableChange}
              className='compact-table'
              columns={[
                {
                  title: t('参数名'),
                  dataIndex: 'name',
                  key: 'name',
                  width: '30%',
                  ellipsis: true,
                  sorter: (a, b) => a.name.localeCompare(b.name),
                  render: (text, record) => {
                    // 显示完整路径作为tooltip，但显示原始字段名
                    const fullPath = record.parentKey ? `${record.parentKey}.${text}` : text;
                    return (
                      <span title={fullPath}>
                        {searchText && fullPath.toLowerCase().includes(searchText.toLowerCase()) ? (
                          <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{text}</span>
                        ) : (
                          record.key
                        )}
                      </span>
                    );
                  },
                },
                {
                  title: t('类型'),
                  dataIndex: 'type',
                  key: 'type',
                  width: '15%',
                  filters: [
                    { text: 'string', value: 'string' },
                    { text: 'number', value: 'number' },
                    { text: 'boolean', value: 'boolean' },
                    { text: 'object', value: 'object' },
                    { text: 'array', value: 'array' },
                    { text: 'null', value: 'null' },
                  ],
                  filteredValue: typeFilter,
                  filterMode: 'menu',
                  onFilter: (value, record) => {
                    return record.type === value;
                  },
                  render: (text) => (
                    <span
                      style={{
                        color:
                          text === 'object'
                            ? '#1890ff'
                            : text === 'array'
                            ? '#52c41a'
                            : text === 'number'
                            ? '#fa8c16'
                            : text === 'boolean'
                            ? '#722ed1'
                            : text === 'null'
                            ? '#f5222d'
                            : '#000000',
                        fontWeight: 'bold',
                        padding: '2px 8px',
                        borderRadius: '4px',
                        backgroundColor:
                          text === 'object'
                            ? 'rgba(24, 144, 255, 0.1)'
                            : text === 'array'
                            ? 'rgba(82, 196, 26, 0.1)'
                            : text === 'number'
                            ? 'rgba(250, 140, 22, 0.1)'
                            : text === 'boolean'
                            ? 'rgba(114, 46, 209, 0.1)'
                            : text === 'null'
                            ? 'rgba(245, 34, 45, 0.1)'
                            : 'transparent',
                      }}
                    >
                      {text}
                    </span>
                  ),
                },
                {
                  title: (
                    <HelpTooltip
                      title={
                        <div>
                          <p>
                            【示例】大家可以根据自己的方式调整描述用语，可以利用接口界面&quot;微信&quot;图标的智能助手帮忙修正，下面初步给出不同类型场景的基础案例，仅供参考。
                          </p>
                          <pre
                            style={{
                              // background: '#f5f5f5',
                              padding: '8px',
                              borderRadius: '4px',
                              maxHeight: '300px',
                              overflow: 'auto',
                            }}
                          >
                            {`
{
  source: 'number',
  title: 'string',
  price: 'number',
  content: 'string',
  cateId: 'string',
  addr: {
    detail: 'string',
    standard: {
      country: 'string',
      countryCode: 'number',
    },
  },
  pics: ['http://easypost.58v5.cn/1746520539629_178603.jpg'],
}`}
                          </pre>
                          <p>
                            <b>场景1:</b> source字段为枚举类型的值
                          </p>
                          <p>描述示例：枚举值0和1，其他值均无业务含义</p>
                          <p>
                            <b>场景2:</b> cateId字段有很多业务值，可以给出要校验的分类集合
                          </p>
                          <p>
                            描述示例：有5种取值，47表示租车，116表示家政，86表示二手书，12510表示找工作，7表示租房
                          </p>
                          <p>
                            <b>场景3:</b> content字段，与cateId字段有关联关系
                          </p>
                          <p>
                            描述示例：按照cate值与业务的映射关系，填写相关业务英文描述，例如cateId值为47，则赋值租车相关的英文描述，上限是200字符
                          </p>
                          <p>
                            <b>场景4:</b>{' '}
                            addr为object类型，业务场景可在子维度描述，侧重类型维度严重描述，或者不写描述
                          </p>
                          <p>描述示例：该字段为object类型，给出该类型异常场景</p>
                          <p>
                            <b>场景5:</b>{' '}
                            pics为array类型，业务场景可在子维度描述，侧重类型维度严重描述，或者不写描述
                          </p>
                          <p>描述示例：该字段为array类型，给出该类型异常、极限等测试点</p>
                        </div>
                      }
                      maxWidth='500px'
                    >
                      {t('描述')}
                    </HelpTooltip>
                  ),
                  dataIndex: 'desc',
                  key: 'desc',
                  width: '55%',
                  render: (text, record) => {
                    let placeholder = t('请输入描述');
                    switch (record.type) {
                      case 'object':
                        placeholder = '例1:为空，不填写任何内容；例2:该字段协议的默认值';
                        break;
                      case 'array':
                        placeholder =
                          '例1:为空，不填写任何内容；例2:该字段为array类型，数组元素上限小于等于200';
                        break;
                      case 'number':
                        placeholder =
                          '例1-枚举：有2种取值，0表示成功，1表示失败；例2-范围：大于0的正整数，分类规则为小于0，等于0，大于0';
                        break;
                      case 'string':
                        placeholder =
                          '例如：字段长度小于200，该字段为相关业务内容，与cate存在映射关系，假设cateId值为47，则该值为租车相关的内容';
                        break;
                      case 'boolean':
                      case 'null':
                      default:
                        placeholder = t('请输入描述');
                    }
                    return (
                      <Input
                        value={text}
                        onChange={(e) => handleDescChange(e.target.value, record)}
                        placeholder={placeholder}
                        allowClear
                      />
                    );
                  },
                },
              ]}
            />
          </div>

          {/* 使用测试用例模态框组件，传递 streamingData */}
          <TestCasesModal
            isVisible={isModalVisible}
            onClose={closeModal}
            testCases={generatedCases}
            isLoadingApi={isLoadingApi}
            streamingData={streamingData}
            onSuccess={getCollections}
          />
        </>
      ) : (
        <div style={{ textAlign: 'center', padding: '5px' }}>
          <p style={{ margin: '2px 0' }}>{t('未检测到请求参数或请求体不是有效的JSON格式')}</p>
          <Button type='primary' onClick={handleAnalyze} style={{ marginTop: '3px' }} size='small'>
            {t('立即分析参数')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default RequestContract;
