import './InterfaceDetail.css'; //导入CSS模块,组件样式

import { Drawer, FloatButton, message, Select } from 'antd'; //导入Ant Design的UI组件
import type { DrawerProps } from 'antd/es/drawer';
import { marked } from 'marked';
import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export interface ChildComponentProps {
  //ChildComponentProps接口,任何使用这个接口作为props类型的组件都需要接收一个名为scfID的字符串参数。
  scfID: string;
  openModal?: boolean;
}

type BranchInfo = {
  clusterName: string;
  key: number;
  value: string;
};

interface IData {
  data: string;
}

//这个React组件InterfaceDetail主要用于展示接口详情，并通过抽屉（Drawer）组件显示相关数据
const InterfaceDetail: React.FC<ChildComponentProps> = ({ scfID, openModal }) => {
  const [open, setOpen] = useState(false); //控制抽屉（Drawer）是否打开,c初始值关闭
  const [size, setSize] = useState<DrawerProps['size']>(); //抽屉的大小，可以根据需要动态设置
  const [selectedOption, setSelectedOption] = useState(''); //初始化，下拉选择框当前选中的项

  const [branchInfo, setBranchInfo] = useState<BranchInfo[]>([]);
  const [iData, setIData] = useState<IData | any>('');

  const [loading, setLoading] = useState(true); //表示数据加载状态。
  const [defaultValue, setDefaultValue] = useState('');

  const [scfid, setScfid] = useState<string>('');

  // // 获取当前URL的查询参数
  // const [searchParams] = useSearchParams();
  // // 更严格的检查：scf 参数存在且其值明确为 'true'
  // const scfParam = searchParams.get('scf');
  // const isScf = scfParam !== null && scfParam === 'true';

  useEffect(() => {
    // useEffect钩子监听，selectedOption 变化时执行，使用异步函数发送POST请求获取数据
    const fetchData = async () => {
      try {
        setScfid(scfid);
        setLoading(true);
        let response = undefined;
        let setSelectValue = false;
        if (scfid == undefined || scfid === '') {
          setLoading(false);
          return;
        }

        if (selectedOption !== '' && selectedOption !== undefined) {
          response = await fetch(
            'https://iapi.58corp.com/iapi/scence/getMethodDoc?scfId=' +
            scfid +
            '&branchId=' +
            selectedOption,
            {
              method: 'GET',
            },
          );
        } else {
          setSelectValue = true;
          response = await fetch('/iapi/iapi/scence/getBranchList?scfId=' + scfid, {
            method: 'GET',
          });
        }
        // 等待服务器响应，并通过 .json() 方法解析返回的JSON数据。
        const result = await response.json();

        if (result !== '' && result.data !== '' && result.data !== undefined) {
          if (setSelectValue) {
            setBranchInfo(result.data);
            setSelectedOption(result.data[0].key);
            setSelectValue = false;
          } else {
            const markdownValue = marked.lexer(result.data);
            setIData(markdownValue);
          }
        } else {
          const markdownValue = marked.lexer('暂无数据');
          setIData(markdownValue);
        }
      } catch (error) {
        console.error('Error fetchData data:', error);
      } finally {
        // 在 finally 块中，无论请求成功还是失败，都会将加载状态 loading 设置为 false，表示数据加载已完成。
        setLoading(false); // 设置加载状态为false，表示加载完成
      }
    };

    fetchData(); // 调异步函数获取数据
  }, [scfid, selectedOption]);

  const handleChange = (value: any) => {
    //处理选择框元素的值变化事件。
    setSelectedOption(value);
  };
  // showDrawer 函数的作用就是：当调用此函数时，不仅会把抽屉的尺寸调整为 'large'，还会打开抽屉使其在用户界面上可见
  const showDrawer = () => {
    if (scfID === '' || scfID === '0') {
      message.error('请先选择接口！');
    } else {
      //tSize('large');
      setOpen(true);
      setScfid(scfID);
    }
  };
  // onClose 函数的作用是在触发关闭动作时执行，使得与之关联的抽屉或模态窗口隐藏起来。
  const onClose = () => {
    setOpen(false);
    setScfid('');
    setSelectedOption('');
  };

  useEffect(() => {
    setOpen(openModal || false);
    setScfid(scfID);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openModal]);

  return (
    <>
      {/* 这是一个用于打开抽屉的浮动按钮 */}
      <FloatButton onClick={() => showDrawer()} className='detail' />

      <Drawer
        title='接口详情'
        placement='right'
        width={1200}
        onClose={onClose}
        open={open}
        // size={size}
        extra={
          <Select
            value={selectedOption}
            defaultValue={defaultValue}
            style={{ width: 150 }}
            onChange={handleChange}
            options={branchInfo.map((option) => ({
              value: option.key,
              label: option.value,
              clusterName: option.clusterName,
            }))}
          />
        }
      >
        <div
          className='markdown'
          dangerouslySetInnerHTML={{ __html: marked.parser(iData) as string }}
        />
      </Drawer>
    </>
  );
};

export default InterfaceDetail;
