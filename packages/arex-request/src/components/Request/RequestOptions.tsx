import { css, getLocalStorage, SmallBadge, styled } from '@arextest/arex-core';
import { useTranslation } from '@arextest/arex-core';
import { Tabs } from 'antd';
import { FC, useMemo, useState } from 'react';
import React from 'react';

import { Tab, TabConfig } from '../../ArexRequest';
import { useArexRequestProps, useArexRequestStore } from '../../hooks';
import { ArexRESTRequest, ArexRESTResponse } from '../../types'; // Import types
import PreRequestScript from './PreRequestScript';
import RequestBody from './RequestBody';
import RequestContract from './RequestContract';
import RequestHeaders from './RequestHeaders';
import RequestParameters from './RequestParameters';
import RequestTests from './RequestTests';

const greyConfig = [
  'qinsisheng',
  'lihongna01',
  'xutingting08',
  'sunxiaoming01',
  'wangyongli',
  'liulingfeng01',
  'shaoxiang01',
  'cuidemin',
  'weijingjing02',
  'mamengmeng01',
  'chenghuan02',
  'liwenfeng01',
  'shenyalan',
  'liuwenjing04',
  'jiangyuqi01',
  'fujiaxin',
  'lichong13',
  'GUEST_BpHTc_mTarnfHrH',
];

const HttpRequestOptionsWrapper = styled.div`
  height: 100%;
  padding: 0 16px 40px;
  flex: 1;
  display: flex;
  flex-direction: column;
  .ant-tabs-content-holder {
    height: 100px;
  }
`;

export interface HttpRequestOptionsProps {
  config?: TabConfig;
  onSave?: (request?: ArexRESTRequest, response?: ArexRESTResponse) => void;
}

const HttpRequestOptions: FC<HttpRequestOptionsProps> = (props) => {
  const { config } = useArexRequestProps();
  const { store } = useArexRequestStore();

  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState('body');

  const items = useMemo(() => {
    const _items: Tab[] = [
      {
        label: <SmallBadge count={store.request.params?.length}>{t('tab.parameters')}</SmallBadge>,
        key: 'parameters',
        children: <RequestParameters />,
        forceRender: true,
      },
      {
        label: <SmallBadge count={store.request.headers?.length}>{t('tab.headers')}</SmallBadge>,
        key: 'headers',
        children: <RequestHeaders />,
        // forceRender: true,
      },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!store.request?.body?.body?.length}>
            {t('tab.body')}
          </SmallBadge>
        ),
        key: 'body',
        children: <RequestBody />,
        forceRender: true,
      },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!store.request?.preRequestScript?.length}>
            {t('tab.pre_request_script')}
          </SmallBadge>
        ),
        key: 'pre_request_script',
        children: <PreRequestScript />,
        forceRender: true,
      },
      {
        label: (
          <SmallBadge offset={[4, 2]} dot={!!store.request.testScript?.length}>
            {t('tab.tests')}
          </SmallBadge>
        ),
        key: 'tests',
        children: <RequestTests />,
        forceRender: true,
      },
      // 添加参数分析标签页
      {
        label: (
          <SmallBadge
            offset={[4, 2]}
            dot={!!(store.request.contract && store.request.contract.length > 0)}
          >
            {t('参数分析')}
          </SmallBadge>
        ),
        key: 'contract',
        children: <RequestContract onSave={props.onSave} />,
        forceRender: true,
        // hidden: !greyConfig.includes(getLocalStorage('email') || ''),
      },
    ];

    return _items
      .filter((item) => !item.hidden)
      .concat(config?.requestTabs?.extra?.filter((tab) => !tab.hidden) || []);
  }, [store.request, t, props.onSave]);

  return (
    <HttpRequestOptionsWrapper>
      <Tabs
        activeKey={activeKey}
        items={items}
        onChange={setActiveKey}
        css={css`
          height: 100%;
          .ant-tabs-nav {
            margin-bottom: 0;
          }
        `}
      />
    </HttpRequestOptionsWrapper>
  );
};

export default HttpRequestOptions;
