import { styled } from '@arextest/arex-core';
import React from 'react';

export const HeaderWrapper: React.FC<React.HTMLAttributes<HTMLDivElement>> = styled.div`
  padding: 0 8px;
  display: flex;
  .ant-select-selector {
    border-radius: 6px 0 0 6px;
  }
`;

// 添加智能图标的样式
export const SmartIconWrapper: React.FC<React.HTMLAttributes<HTMLDivElement>> = styled.div`
  position: fixed;
  top: 10%;
  left: 90%;
  transform: translate(-50%, -50%);
  background-color: #1890ff;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.6);
  animation: fadeIn 0.5s ease-in-out, blink 1.2s infinite, pulse 2s infinite;
  z-index: 1000;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  @keyframes blink {
    0% {
      box-shadow: 0 0 5px rgba(24, 144, 255, 0.6);
    }
    50% {
      box-shadow: 0 0 20px 5px rgba(24, 144, 255, 1);
    }
    100% {
      box-shadow: 0 0 5px rgba(24, 144, 255, 0.6);
    }
  }

  @keyframes pulse {
    0% {
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
    }
  }

  &:hover {
    transform: translate(-50%, -50%) scale(1.2);
    transition: transform 0.3s;
    box-shadow: 0 0 25px 8px rgba(24, 144, 255, 1);
  }
`;
