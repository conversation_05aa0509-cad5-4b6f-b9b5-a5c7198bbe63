import { getLocalStorage, useTranslation } from '@arextest/arex-core';
import { Alert, Button, Checkbox, message, Modal, Spin, Tabs, Tooltip } from 'antd';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import ReactJson from 'react-json-view';

import { useArexRequestStore } from '../../../hooks';

interface TestCase {
  caseDesc: string;
  caseBody: any;
  caseParams?: any; // 新增：用于存储URL参数的测试用例
  isAIGenerated?: boolean; // 标记是否为AI生成的测试用例
}

interface TestCasesModalProps {
  isVisible: boolean;
  onClose: () => void;
  testCases: TestCase[];
  isLoadingApi: boolean;
  streamingData?: string;
  onSuccess?: () => void;
}

const TestCasesModal: FC<TestCasesModalProps> = ({
  isVisible,
  onClose,
  testCases,
  isLoadingApi,
  streamingData = '',
  onSuccess,
}) => {
  const { t } = useTranslation();
  const { store } = useArexRequestStore();
  const [loading, setLoading] = useState(false);
  const loadingRef = useRef(isLoadingApi); // 使用 useRef 替代之前的 useEffect
  // 添加状态管理选中的测试用例
  const [selectedCases, setSelectedCases] = useState<number[]>([]);

  // 区分基础测试用例和AI生成的测试用例
  const basicCaseIndices = useMemo(() =>
    testCases.map((testCase, index) => (!testCase.isAIGenerated ? index : -1)).filter(index => index !== -1),
    [testCases]
  );

  const aiCaseIndices = useMemo(() =>
    testCases.map((testCase, index) => (testCase.isAIGenerated ? index : -1)).filter(index => index !== -1),
    [testCases]
  );

  // 初始化时全选所有测试用例
  useEffect(() => {
    if (isVisible && testCases.length > 0) {
      setSelectedCases(testCases.map((_, index) => index));
    }
  }, [isVisible, testCases]);

  // 基础测试用例全选/取消全选
  const handleSelectAllBasic = (checked: boolean) => {
    if (checked) {
      setSelectedCases(prev => {
        const filtered = prev.filter(idx => !basicCaseIndices.includes(idx));
        return [...filtered, ...basicCaseIndices];
      });
    } else {
      setSelectedCases(prev => prev.filter(idx => !basicCaseIndices.includes(idx)));
    }
  };

  // AI测试用例全选/取消全选
  const handleSelectAllAI = (checked: boolean) => {
    if (checked) {
      setSelectedCases(prev => {
        const filtered = prev.filter(idx => !aiCaseIndices.includes(idx));
        return [...filtered, ...aiCaseIndices];
      });
    } else {
      setSelectedCases(prev => prev.filter(idx => !aiCaseIndices.includes(idx)));
    }
  };

  // 单个测试用例选择处理函数
  const handleCaseSelect = (index: number, checked: boolean) => {
    if (checked) {
      setSelectedCases([...selectedCases, index]);
    } else {
      setSelectedCases(selectedCases.filter(i => i !== index));
    }
  };

  // 检查是否所有基础测试用例都被选中
  const isAllBasicSelected = useMemo(() => {
    return basicCaseIndices.length > 0 &&
           basicCaseIndices.every(index => selectedCases.includes(index));
  }, [selectedCases, basicCaseIndices]);

  // 检查是否所有AI测试用例都被选中
  const isAllAISelected = useMemo(() => {
    return aiCaseIndices.length > 0 &&
           aiCaseIndices.every(index => selectedCases.includes(index));
  }, [selectedCases, aiCaseIndices]);

  // 检查是否有任何测试用例被选中
  const hasSelectedCase = useMemo(() => {
    return selectedCases.length > 0;
  }, [selectedCases]);

  const getActiveWorkspaceId = () => {
    const workspaceId: { state: { activeWorkspaceId: string } } | undefined =
      getLocalStorage('workspaces-storage');
    return workspaceId?.state.activeWorkspaceId;
  };

  const getActivePane = () => {
    const workspaceId: { state: { activePane: { id: string } } } | undefined =
      getLocalStorage('menus-panes-storage');
    const paneId = workspaceId?.state.activePane.id;

    if (paneId) {
      const parts = paneId.split('-');
      if (parts.length === 3) {
        return `${parts[0]}-${parts[2]}`;
      }
    }

    return paneId;
  };

  // 提取测试字段路径数组
  const getTestFieldPaths = (caseDesc: string): string[] => {
    // 从描述中提取字段路径，通常是描述中 " - " 前面的部分
    const match = caseDesc.match(/^(?:【AI】)?(.*?)\s*-\s/);
    const rawPath = match ? match[1].trim() : '';

    // 检查是否包含 '+'，如果包含则分割，否则返回包含单个路径的数组
    if (rawPath.includes('+')) {
      return rawPath.split('+').map((p) => p.trim());
    } else if (rawPath) {
      return [rawPath];
    } else {
      return []; // 如果没有匹配到路径，返回空数组
    }
  };

  // 为每个测试用例创建自定义主题
  const getCustomTheme = (caseDesc: string) => {
    // 注意：这里调用了新的函数，但返回值未在当前主题逻辑中使用
    const testFieldPaths = getTestFieldPaths(caseDesc);

    return {
      base00: '#ffffff', // 背景色
      base01: '#f5f5f5', // 行背景色
      base02: '#e0e0e0', // 边框色
      base03: '#aaaaaa', // 注释色
      base04: '#999999', // 次要文本色
      base05: '#333333', // 主要文本色
      base06: '#222222', // 强调文本色
      base07: '#111111', // 标题文本色
      base08: '#e91e63', // 变量名色
      base09: '#ff5722', // 数字、布尔值色
      base0A: '#ff9800', // 属性名色
      base0B: '#4caf50', // 字符串色
      base0C: '#009688', // 日期、正则色
      base0D: '#2196f3', // 函数色
      base0E: '#9c27b0', // 关键字色
      base0F: '#795548', // 弃用色
    };
  };

  // 自定义渲染器，用于高亮测试字段 (更新以处理路径数组)
  const getCustomRenderer = (caseDesc: string) => {
    const testFieldPaths = getTestFieldPaths(caseDesc);
    // 将每个路径分割成部分，用于后续匹配
    const pathPartsArray = testFieldPaths.map((p) => p.split('.'));

    return {
      // 自定义属性名渲染 (保持不变)
      getItemString: (type: string, data: any, itemType: string, itemString: string) => {
        return (
          <span>
            {itemType} {itemString}
          </span>
        );
      },

      // 自定义属性渲染 (更新以检查多个路径)
      renderLabel: (keyPath: (string | number)[], keyValue: any) => {
        let isExactTestField = false;
        let isTestFieldPart = false;

        // 遍历所有测试路径进行检查
        for (const pathParts of pathPartsArray) {
          const currentIsExact =
            pathParts.length > 0 && pathParts[pathParts.length - 1] === keyPath[0];
          const currentIsPart = pathParts.some(
            (part) => keyPath.includes(part) && keyPath.length <= pathParts.length,
          );

          if (currentIsExact) {
            isExactTestField = true;
            break; // 找到精确匹配，无需继续检查
          }
          if (currentIsPart) {
            isTestFieldPart = true;
            // 不中断，继续检查是否有精确匹配
          }
        }

        // 如果是测试字段本身，使用更醒目的高亮样式
        if (isExactTestField) {
          return (
            <span
              style={{
                fontWeight: 'bold',
                color: '#ffffff',
                backgroundColor: '#e91e63',
                padding: '2px 6px',
                borderRadius: '3px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.2)',
              }}
            >
              {keyPath[0]}
            </span>
          );
        }
        // 如果是测试字段的路径部分，使用普通高亮样式
        else if (isTestFieldPart) {
          return (
            <span
              style={{
                fontWeight: 'bold',
                color: '#e91e63',
                backgroundColor: 'rgba(233, 30, 99, 0.1)',
                padding: '2px 4px',
                borderRadius: '3px',
              }}
            >
              {keyPath[0]}
            </span>
          );
        }

        return <span>{keyPath[0]}</span>;
      },

      // 自定义值渲染 (更新以检查多个路径)
      renderValue: (value: any, keyPath: (string | number)[]) => {
        let isTestField = false;

        // 遍历所有测试路径进行检查
        for (const pathParts of pathPartsArray) {
          if (
            pathParts.length > 0 &&
            pathParts[pathParts.length - 1] === keyPath[0] &&
            keyPath.length === 1
          ) {
            isTestField = true;
            break; // 找到匹配，无需继续检查
          }
        }

        // 如果是测试字段，使用高亮样式
        if (isTestField) {
          return (
            <span
              style={{
                fontWeight: 'bold',
                backgroundColor: 'rgba(233, 30, 99, 0.2)',
                padding: '2px 4px',
                borderRadius: '3px',
              }}
            >
              {String(value)}
            </span>
          );
        }

        return null;
      },
    };
  };

  // 监听 isLoadingApi 变化，用于显示完成提示
  useEffect(() => {
    // 仅当 Modal 可见且 isLoadingApi 从 true 变为 false 时提示
    if (isVisible && !isLoadingApi && loadingRef.current) {
      message.success(t('AI测试用例生成成功！'));
    }
    // 更新 ref 以跟踪当前的状态，为下一次比较做准备
    loadingRef.current = isLoadingApi;
  }, [isVisible, isLoadingApi, t]);

  // 批量生成测试用例方法 (修改以只处理选中的测试用例)
  const autoGenCase = async () => {
    try {
      setLoading(true);
      const workspaceId = getActiveWorkspaceId();
      const userName = getLocalStorage('email');
      const parentPath = getActivePane();

      // 检查是否为SCF请求
      const isSCFRequest = store.request.requestType === 'SCF' ||
        (!store.request.requestType && store.request.body?.scfRequest);

      // 只处理被选中的测试用例
      const selectedTestCases = testCases.filter((_, index) => selectedCases.includes(index));

      // 处理testCases，确保URL参数被正确包含
      const processedTestCases = selectedTestCases.map((testCase) => {
        // 检查是否有参数和请求体
        const hasParams = testCase.caseParams && Object.keys(testCase.caseParams).length > 0;
        const hasBody = testCase.caseBody && Object.keys(testCase.caseBody).length > 0;

        // 统一处理：确保caseBody是字符串格式
        let processedCaseBody = '';
        if (typeof testCase.caseBody === 'string') {
          // 如果已经是字符串，直接使用
          processedCaseBody = testCase.caseBody;
        } else if (hasBody) {
          // SCF请求特殊处理：将数组元素包装成 {exampleValue: value} 格式
          if (isSCFRequest && Array.isArray(testCase.caseBody)) {
            // const wrappedArray = testCase.caseBody.map((value) => ({
            //   exampleValue: JSON.stringify(value),
            // }));
            const wrappedArray = testCase.caseBody.map((value) => JSON.stringify(value));
            processedCaseBody = JSON.stringify(wrappedArray);
            // processedCaseBody = JSON.stringify(testCase.caseBody);
          } else {
            // 如果是对象，转为字符串
            processedCaseBody = JSON.stringify(testCase.caseBody);
          }
        } else {
          // 如果没有body，使用空对象
          processedCaseBody = '';
        }

        // 构建统一格式的测试用例
        const processedCase: {
          caseDesc: string;
          caseBody: string;
          caseParams?: string;
        } = {
          caseDesc: testCase.caseDesc,
          caseBody: processedCaseBody,
        };

        // 如果有URL参数，将其添加到测试用例中
        if (hasParams) {
          // 确保caseParams是对象格式，不需要序列化
          processedCase.caseParams = JSON.stringify(testCase.caseParams);

          // 只有在描述中没有URL参数信息时才添加
          if (!processedCase.caseDesc.includes('[URL参数:')) {
            processedCase.caseDesc = `${testCase.caseDesc} [URL参数: ${Object.keys(testCase.caseParams).join(', ')}]`;
          }
        }

        return processedCase;
      });

      // 如果没有选中任何测试用例，提示用户并返回
      if (processedTestCases.length === 0) {
        message.warning(t('请至少选择一个测试用例'));
        return;
      }

      const response = await fetch('/report/filesystem/batchAddCase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Token': getLocalStorage('accessToken') || '',
        },
        body: JSON.stringify({ workspaceId, userName, cases: processedTestCases, parentPath }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.body) {
        message.success(t('批量生成测试用例成功'));
        onSuccess?.();
        onClose?.();
      } else {
        message.error(t('批量生成测试用例失败') + ': ' + (result.message || ''));
      }
    } catch (error) {
      console.error('批量生成测试用例错误:', error);
      message.error(t('批量生成测试用例失败，请稍后重试'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={`${t('生成的测试用例')} (${testCases.length}${t('条')})`}
      open={isVisible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button
          key='autoGen'
          type='primary'
          onClick={autoGenCase}
          loading={loading}
          disabled={isLoadingApi || !hasSelectedCase}
        >
          {t('批量生成测试用例')}
        </Button>,
        <Button key='close' onClick={onClose} disabled={loading || isLoadingApi}>
          {t('关闭')}
        </Button>,
      ]}
    >
      {/* API 加载提示 */}
      {isLoadingApi && (
        <div
          style={{
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Spin />
          <span style={{ marginLeft: '8px', marginRight: '8px' }}>
            {t('AI 生成更多测试用例中...')}
          </span>
          {/* 添加 Tooltip 包裹的查看按钮 */}
          <Tooltip
            title={
              <pre
                style={{
                  maxHeight: '200px',
                  overflowY: 'auto',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-all',
                }}
              >
                {streamingData || '暂无数据'}
              </pre>
            }
            mouseEnterDelay={0.1} // 悬停0.1秒后显示
            placement='bottom'
          >
            <Button type='link' size='small' style={{ padding: 0 }}>
              {t('查看')}
            </Button>
          </Tooltip>
        </div>
      )}

      {/* 当没有用例且不在加载时显示提示 */}
      {!isLoadingApi && testCases.length === 0 && (
        <Alert message={t('没有生成任何测试用例，请检查请求体或参数选择。')} type='info' showIcon />
      )}

      {/* 分组全选复选框 */}
      {testCases.length > 0 && !isLoadingApi && (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={isAllBasicSelected}
              onChange={(e) => handleSelectAllBasic(e.target.checked)}
              disabled={isLoadingApi || basicCaseIndices.length === 0}
              style={{ marginRight: '16px' }}
            >
              {t('全选基础测试用例')} ({basicCaseIndices.length}{t('条')})
            </Checkbox>
            <Checkbox
              checked={isAllAISelected}
              onChange={(e) => handleSelectAllAI(e.target.checked)}
              disabled={isLoadingApi || aiCaseIndices.length === 0}
            >
              {t('全选AI测试用例')} ({aiCaseIndices.length}{t('条')})
            </Checkbox>
          </div>
          <div>
            {t('已选择')} {selectedCases.length}/{testCases.length} {t('个测试用例')}
          </div>
        </div>
      )}

      {/* 仅当有测试用例时显示 Tabs */}
      {testCases.length > 0 && (
        <Tabs
          type='card'
          items={testCases.map((testCase, index) => {
            // 调用更新后的函数
            const customTheme = getCustomTheme(testCase.caseDesc);
            const customRenderer = getCustomRenderer(testCase.caseDesc);
            const testPaths = getTestFieldPaths(testCase.caseDesc); // 获取路径数组
            const isSelected = selectedCases.includes(index);
            const isAI = testCase.isAIGenerated;

            return {
              label: (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Checkbox
                    checked={isSelected}
                    onChange={(e) => handleCaseSelect(index, e.target.checked)}
                    onClick={(e) => e.stopPropagation()}
                    style={{ marginRight: '8px' }}
                    disabled={isLoadingApi}
                  />
                  <span style={{
                    opacity: isSelected ? 1 : 0.5,
                    color: isAI ? '#1890ff' : 'inherit' // AI用例使用蓝色标记
                  }}>
                    {isAI && '【AI】'}{testCase.caseDesc}
                  </span>
                </div>
              ),
              key: String(index),
              children: (
                <div style={{ maxHeight: '400px', overflow: 'auto' }}>
                  <div
                    style={{
                      padding: '8px',
                      backgroundColor: '#f0f0f0',
                      borderRadius: '4px',
                      marginBottom: '8px',
                      fontWeight: 'bold',
                      color: '#e91e63',
                    }}
                  >
                    {/* 更新显示逻辑，将路径数组用逗号连接 */}
                    {t('测试字段')}: {testPaths.join(', ')}
                  </div>
                  {/* 根据测试用例类型显示不同的数据 */}
                  {(() => {
                    const hasParams =
                      testCase.caseParams && Object.keys(testCase.caseParams).length > 0;
                    const hasBody = testCase.caseBody && Object.keys(testCase.caseBody).length > 0;
                    const displayData =
                      hasParams && !hasBody ? testCase.caseParams : testCase.caseBody;
                    const dataType = hasParams && !hasBody ? 'URL参数' : '请求体';

                    return (
                      <React.Fragment>
                        {hasParams && !hasBody && (
                          <div
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#e6f7ff',
                              borderRadius: '4px',
                              marginBottom: '8px',
                              fontSize: '12px',
                              color: '#1890ff',
                            }}
                          >
                            {t('数据类型')}: {dataType}
                          </div>
                        )}
                        <ReactJson
                          src={displayData}
                          name={false}
                          theme={customTheme}
                          displayDataTypes={false}
                          enableClipboard={true}
                          collapsed={false}
                          collapseStringsAfterLength={50}
                          style={{
                            padding: '10px',
                            borderRadius: '4px',
                            backgroundColor: '#f5f5f5',
                          }}
                          shouldCollapse={(field) => {
                            // 修复类型错误，使用正确的属性判断
                            if (!field || field.namespace === undefined) return false;

                            // 获取当前字段的深度
                            const depth = field.namespace ? field.namespace.length + 1 : 1;

                            // 获取所有测试路径的部分
                            const allPathParts = testPaths.flatMap((p) => p.split('.'));

                            // 构建当前字段的完整路径
                            let fullPath = '';
                            if (field.namespace && field.namespace.length > 0) {
                              fullPath = [...field.namespace, field.name].join('.');
                            } else {
                              fullPath = String(field.name);
                            }

                            // 检查是否是根节点或任何一个测试字段的路径
                            const isRootNode = depth === 1;
                            // 检查当前字段路径是否是任何一个测试路径的前缀，或者当前字段名是否是任何测试路径的一部分
                            const isTestFieldPath = testPaths.some((testPath) => {
                              const parts = testPath.split('.');
                              // 检查 fullPath 是否是 testPath 的前缀
                              if (testPath.startsWith(fullPath)) return true;
                              // 检查当前字段名是否在测试路径的某一部分
                              return parts.includes(String(field.name));
                            });

                            // 如果是根节点或测试字段的路径，不折叠
                            if (isRootNode || isTestFieldPath) return false;

                            // 默认折叠深度大于2的节点
                            return depth > 2;
                          }}
                          iconStyle='triangle'
                          indentWidth={6}
                          {...customRenderer}
                        />
                      </React.Fragment>
                    );
                  })()}
                </div>
              ),
            };
          })}
        />
      )}
    </Modal>
  );
};

export default TestCasesModal;
