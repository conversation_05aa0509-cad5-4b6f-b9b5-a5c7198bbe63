import { getLocalStorage, useTranslation } from '@arextest/arex-core';
import { cloneDeep, get, set } from 'lodash';
// 引入 useState
import { useState } from 'react';

import { useArexRequestStore } from '../../../hooks';
import { ParamContract } from '../../../types';
import { generateTestCases, parseRequestBody } from '../utils/caseGenerator';

export interface TestCase {
  caseDesc: string;
  caseBody: any;
  caseParams?: any; // 新增：用于存储URL参数的测试用例
  isAIGenerated?: boolean; // 标记是否为AI生成的测试用例
}

interface ApiCaseItem {
  key: string; // e.g., "age", "l1.l2.l3"
  caseValue: any;
  caseDesc: string;
}

interface ModificationItem {
  key: string; // e.g., "jsonBody.age", "urlQuery.l1.l2.l3"
  caseValue: any;
}

interface NewApiCaseItem {
  caseDesc: string;
  modifications: ModificationItem[];
}

const fetchAdditionalCases = async (
  originalBody: any,
  originalParams: any, // 新增：原始URL参数
  selectedParamsWithDesc: ParamContract[],
  // 新增回调函数用于更新流式数据
  onStreamUpdate: (data: string) => void,
  // 新增store参数
  store: any,
): Promise<TestCase[]> => {
  console.log('原始请求体:', originalBody);
  console.log('选中且有描述的参数:', selectedParamsWithDesc);

  // --- 实际 API 调用 ---
  // 判断数据源类型
  const hasBody = originalBody && Object.keys(originalBody).length > 0;
  const hasParams = originalParams && Object.keys(originalParams).length > 0;

  // 检查是否为SCF请求
  const isSCFRequest = store.request.requestType === 'SCF' ||
    (!store.request.requestType && store.request.body?.scfRequest);

  // 为key添加前缀以区分数据源
  const paramsForAI = selectedParamsWithDesc.map(({ type, desc, key }) => {
    let prefixedKey = key;

    // SCF请求使用特殊处理，保持原始的数组下标格式
    if (isSCFRequest) {
      // SCF请求的key已经是[0], [1]等格式，直接使用
      prefixedKey = key;
    } else if (hasBody && !hasParams) {
      // 只有body数据
      prefixedKey = `jsonBody.${key}`;
    } else if (!hasBody && hasParams) {
      // 只有URL参数数据
      prefixedKey = `urlQuery.${key}`;
    } else if (hasBody && hasParams) {
      // 两者都有，根据字段是否存在于body中来判断
      const bodyHasField = get(originalBody, key) !== undefined;
      if (bodyHasField) {
        prefixedKey = `jsonBody.${key}`;
      } else {
        prefixedKey = `urlQuery.${key}`;
      }
    }

    return {
      type,
      desc,
      key: prefixedKey,
    };
  });

  const aiMessage = JSON.stringify(paramsForAI); // 只序列化提取后的字段

  const params = {
    oa: getLocalStorage('email') as string,
    message: aiMessage,
    moduleName: 'CASE_GENERATOR',
    serviceName: 'wreplay-copilot',
    triggerUrl: window.location.href,
    isHidden: false,
  };

  let apiResponseData: NewApiCaseItem[] = [];
  let completeResponse = ''; // 用于累积流式响应

  try {
    const response = await fetch('/ai/ai-assistant/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream',
      },
      body: JSON.stringify(params),
    });

    if (!response || !response.ok || !response.body) {
      throw new Error('AI 接口调用失败: 无效响应');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    let isReading = true;
    while (isReading) {
      const { done, value } = await reader.read();

      if (done) {
        isReading = false;
        // 处理缓冲区剩余数据
        if (buffer.trim()) {
          try {
            if (buffer.includes('data:')) {
              const dataContent = buffer.substring(buffer.indexOf('data:') + 5).trim();
              if (dataContent) {
                const jsonData = JSON.parse(dataContent);
                if (jsonData.content) {
                  completeResponse += jsonData.content;
                } else if (jsonData.data?.streamResp) {
                  completeResponse += jsonData.data.streamResp.replace(/\\n/g, '\n');
                } else if (jsonData.data?.content) {
                  completeResponse += jsonData.data.content;
                }
              }
            }
          } catch (e) {
            console.warn('解析最终缓冲区JSON失败:', e);
          }
        }
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;

      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // 保留不完整的行到下一次处理

      for (const line of lines) {
        if (line.trim().startsWith('data:')) {
          try {
            const dataContent = line.substring(line.indexOf('data:') + 5).trim();
            if (dataContent) {
              const jsonData = JSON.parse(dataContent);
              let chunkContent = ''; // 当前块的内容
              if (jsonData.content) {
                chunkContent = jsonData.content;
              } else if (jsonData.data?.streamResp) {
                chunkContent = jsonData.data.streamResp.replace(/\\n/g, '\n');
              } else if (jsonData.data?.content) {
                chunkContent = jsonData.data.content;
              }
              if (chunkContent) {
                completeResponse += chunkContent;
                onStreamUpdate(completeResponse); // 调用回调更新外部状态
              }
            }
          } catch (e) {
            console.warn('解析流式数据行失败:', e, '行内容:', line);
            // 尝试直接累加非JSON字符串内容
            if (!line.includes('{') && !line.includes('}')) {
              const textContent = line.substring(line.indexOf('data:') + 5).trim();
              if (textContent.startsWith('"') && textContent.endsWith('"')) {
                try {
                  const parsedText = JSON.parse(textContent);
                  completeResponse += parsedText;
                  onStreamUpdate(completeResponse); // 调用回调更新外部状态
                } catch {
                  /* ignore */
                }
              }
            }
          }
        }
      }
    }

    console.log('最终完整响应:', completeResponse);

    // 添加一个函数来解析和执行 ${} 表达式
    const evaluateTemplateExpressions = (jsonString: string): string => {
      // 使用正则表达式匹配所有的 ${...} 表达式
      return jsonString.replace(/"\$\{(.+?)\}"/g, (match, expression) => {
        try {
          // 使用 Function 构造函数安全地执行表达式
          const result = new Function(`return ${expression}`)();
          // 将结果转换为字符串并确保正确转义 JSON 字符
          return JSON.stringify(result);
        } catch (error) {
          console.warn(`执行表达式 "${expression}" 失败:`, error);
          // 如果执行失败，保留原始表达式
          return match;
        }
      });
    };

    // 流结束后，尝试解析累积的完整响应
    try {
      // 查找 JSON 数组的开始和结束位置
      const jsonStart = completeResponse.indexOf('[');
      const jsonEnd = completeResponse.lastIndexOf(']');
      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        const jsonString = completeResponse.substring(jsonStart, jsonEnd + 1);
        // 在解析 JSON 之前，先处理模板表达式
        const processedJsonString = evaluateTemplateExpressions(jsonString);
        apiResponseData = JSON.parse(processedJsonString);
        if (!Array.isArray(apiResponseData)) {
          throw new Error('AI 返回的不是有效的 JSON 数组');
        }
        // 可选：添加更严格的类型检查，确保每个元素符合 NewApiCaseItem 结构
      } else {
        throw new Error('AI 返回的内容中未找到有效的 JSON 数组');
      }
    } catch (parseError) {
      console.error('解析AI最终响应失败:', parseError, '完整响应:', completeResponse);
      throw new Error(
        `解析AI响应失败: ${parseError instanceof Error ? parseError.message : parseError}`,
      );
    }
  } catch (error) {
    console.error('调用AI接口获取测试用例失败:', error);
    // 抛出错误，让调用者处理
    throw error;
  }
  // --- API 调用结束 ---

  // --- 使用 API 返回的数据生成用例 ---
  const generatedCases: TestCase[] = apiResponseData
    .map((item) => {
      // 对原始 body 和 params 进行深拷贝，避免互相影响
      const caseBody = cloneDeep(originalBody);
      const caseParams = cloneDeep(originalParams);

      try {
        // 遍历 modifications 数组，应用所有修改
        item.modifications.forEach((modification) => {
          // 解析key中的前缀来判断修改目标
          let targetKey = modification.key;
          let isBodyModification = false;
          let isParamsModification = false;

          if (modification.key.startsWith('jsonBody.')) {
            // 移除前缀，获取实际的字段路径
            targetKey = modification.key.substring('jsonBody.'.length);
            isBodyModification = true;
          } else if (modification.key.startsWith('urlQuery.')) {
            // 移除前缀，获取实际的字段路径
            targetKey = modification.key.substring('urlQuery.'.length);
            isParamsModification = true;
          } else {
            // 兜底逻辑：如果没有前缀，使用原有的判断逻辑
            const isBodyField = originalBody && Object.keys(originalBody).length > 0;
            const isParamsField = originalParams && Object.keys(originalParams).length > 0;

            if (isBodyField && !isParamsField) {
              isBodyModification = true;
            } else if (!isBodyField && isParamsField) {
              isParamsModification = true;
            } else if (isBodyField && isParamsField) {
              // 两者都有，根据字段路径判断
              const bodyHasField = get(originalBody, targetKey) !== undefined;
              if (bodyHasField) {
                isBodyModification = true;
              } else {
                isParamsModification = true;
              }
            }
          }

          // 根据判断结果应用修改
          if (isBodyModification) {
            set(caseBody, targetKey, modification.caseValue);
          } else if (isParamsModification) {
            set(caseParams, targetKey, modification.caseValue);
          }
        });
      } catch (setErr) {
        console.error(
          `使用 lodash.set 设置值失败: modifications=${JSON.stringify(item.modifications)}`,
          setErr,
        );
        // 可以选择跳过这个用例或返回一个表示错误的用例
        return {
          caseDesc: `错误: 无法应用修改 ${item.caseDesc}`,
          caseBody: cloneDeep(originalBody),
          caseParams: cloneDeep(originalParams),
          isAIGenerated: true, // 标记为AI生成
        };
      }

      return {
        caseDesc: `${item.caseDesc}`,
        caseBody: caseBody,
        caseParams: caseParams,
        isAIGenerated: true, // 标记为AI生成
      };
    })
    .filter(Boolean); // 过滤掉可能产生的错误用例

  return generatedCases;
};

// 辅助函数：根据路径查找 ParamContract
const findParamByPath = (
  params: ParamContract[],
  path: string,
  parentPath = '',
): ParamContract | null => {
  for (const param of params) {
    // 直接使用param.key进行比较（已包含正确的数组索引）
    if (param.key === path) {
      return param;
    }
    if (param.children && param.children.length > 0) {
      const found = findParamByPath(param.children, path, param.key);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// 辅助函数：获取选中路径中 desc 不为空的 ParamContract
const getSelectedParamsWithDesc = (
  allParams: ParamContract[],
  selectedPaths: string[],
): ParamContract[] => {
  const paramsWithDesc: ParamContract[] = [];
  selectedPaths.forEach((path) => {
    const param = findParamByPath(allParams, path);
    if (param && param.desc) {
      paramsWithDesc.push(param);
    }
  });
  return paramsWithDesc;
};

export const useTestCaseGenerator = (selectedRowKeys: React.Key[] = []) => {
  const { t } = useTranslation();
  const { store } = useArexRequestStore();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [generatedCases, setGeneratedCases] = useState<TestCase[]>([]);
  const [isLoadingApi, setIsLoadingApi] = useState(false); // API加载状态
  const [streamingData, setStreamingData] = useState(''); // 追踪流式数据状态

  // 获取选中的参数路径
  const getSelectedParamPaths = (
    params: ParamContract[],
    selectedKeys: React.Key[],
    parentPath = '',
  ): string[] => {
    let paths: string[] = [];

    params.forEach((param) => {
      // 如果当前参数被选中，直接使用param.key作为路径（已包含正确的数组索引）
      if (selectedKeys.includes(param.key)) {
        paths.push(param.key);
      }

      // 递归处理子参数，但不考虑父参数是否被选中
      if (param.children && param.children.length > 0) {
        const childPaths = getSelectedParamPaths(param.children, selectedKeys, param.key);
        paths = [...paths, ...childPaths];
      }
    });

    return paths;
  };

  // 实现异步加载和更新
  const handleGenCases = async () => {
    if (!store.request.contract || store.request.contract.length === 0) {
      window.message.warning(t('无法生成测试用例：缺少参数定义'));
      return;
    }

    try {
      // 解析当前请求体
      const originalBody = parseRequestBody(store.request.body);

      // 解析URL参数
      const originalParams: Record<string, any> = {};
      if (store.request.params && store.request.params.length > 0) {
        store.request.params.forEach((param: any) => {
          if (param.active && param.key && param.value !== undefined) {
            try {
              // 尝试解析为JSON，如果失败则保持字符串
              originalParams[param.key] = JSON.parse(param.value);
            } catch {
              originalParams[param.key] = param.value;
            }
          }
        });
      }

      // 检查 SCF 请求的数据源
      const isSCFRequest =
        store.request.requestType === 'SCF' ||
        (!store.request.requestType && store.request.body?.scfRequest);

      let hasSCFParams = false;
      if (isSCFRequest && store.request.body?.scfRequest?.params) {
        hasSCFParams = store.request.body.scfRequest.params.length > 0;
      }

      // 检查是否有有效的数据源（body、params 或 SCF params）
      const hasBody = originalBody && Object.keys(originalBody).length > 0;
      const hasParams = Object.keys(originalParams).length > 0;

      if (!hasBody && !hasParams && !hasSCFParams) {
        window.message.warning(t('无法生成测试用例：缺少请求体、URL参数或SCF参数'));
        return;
      }

      // 获取选中参数的路径
      const selectedPaths = getSelectedParamPaths(store.request.contract, selectedRowKeys);

      if (selectedRowKeys.length > 0 && selectedPaths.length === 0) {
        window.message.warning(t('未找到选中的参数'));
        return;
      }

      // 获取选中路径中 desc 不为空的 ParamContract (用于 API 调用)
      const selectedParamsWithDesc = getSelectedParamsWithDesc(
        store.request.contract,
        selectedPaths,
      );

      // 1. 生成基础测试用例，并标记为非AI生成
      const initialCases = generateTestCases(originalBody, store.request.contract, selectedPaths, originalParams).map(testCase => ({
        ...testCase,
        isAIGenerated: false, // 标记为基础测试用例
      }));

      // 2. 立即设置基础用例并显示模态框，开始加载状态
      setGeneratedCases(initialCases);
      setIsModalVisible(true);
      setIsLoadingApi(true); // 开始加载 API
      setStreamingData(''); // 重置流式数据

      // 3. 异步调用额外接口获取数据 (不阻塞 UI)
      fetchAdditionalCases(originalBody, originalParams, selectedParamsWithDesc, (data) => {
        // 通过回调更新流式数据状态
        setStreamingData(data);
      }, store)
        .then((additionalCases) => {
          // 4. 成功获取后，合并用例并更新状态
          // AI生成的用例已在fetchAdditionalCases中标记为isAIGenerated: true
          setGeneratedCases((prevCases) => [...prevCases, ...additionalCases]);
        })
        .catch((apiError) => {
          // 5. 处理 API 调用错误
          console.error('调用额外接口失败:', apiError);
          window.message.error(t('获取额外测试用例失败'));
          setStreamingData((prev) => prev + `\n\n--- Error: ${apiError.message} ---`); // 在流数据中显示错误
        })
        .finally(() => {
          // 6. 无论成功或失败，结束加载状态
          setIsLoadingApi(false);
        });
    } catch (error) {
      console.error('生成测试用例失败:', error);
      window.message.error(t('生成测试用例失败'));
      setIsLoadingApi(false); // 确保同步错误时也关闭加载状态
      setStreamingData(`--- Error: ${error instanceof Error ? error.message : String(error)} ---`); // 显示同步错误
    }
  };

  const closeModal = () => {
    setIsModalVisible(false);
    // 关闭时可以考虑重置状态
    // setGeneratedCases([]);
    // setIsLoadingApi(false);
    // setStreamingData('');
  };

  return {
    isModalVisible,
    generatedCases,
    handleGenCases,
    closeModal,
    isLoadingApi,
    streamingData, // 返回流式数据状态
  };
};
