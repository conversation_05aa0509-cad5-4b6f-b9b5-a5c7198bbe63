import { useCallback } from 'react';

import { ArexRESTParam, ArexRESTReqBody } from '../../../types';

interface ParamItem {
  name: string;
  type: string;
  desc: string;
  key: string;
  children?: ParamItem[];
  parentKey?: string;
}

export const useParamsExtractor = () => {
  // 递归提取参数
  const extractParams = useCallback((obj: any, parentKey = ''): ParamItem[] => {
    const params: ParamItem[] = [];

    if (typeof obj === 'object' && obj !== null) {
      Object.entries(obj).forEach(([key, value]) => {
        const currentKey = parentKey ? `${parentKey}.${key}` : key;
        // 确保正确设置类型
        let type: string;
        let children = undefined;

        if (value === null) {
          type = 'null';
        } else if (Array.isArray(value)) {
          type = 'array';
          if (value.length > 0 && typeof value[0] === 'object') {
            // 为数组中的对象元素生成正确的路径，包含索引
            children = extractParams(value[0], `${currentKey}[0]`);
          }
        } else if (typeof value === 'number') {
          type = 'number';
        } else if (typeof value === 'boolean') {
          type = 'boolean';
        } else if (typeof value === 'object') {
          type = 'object';
          children = extractParams(value, currentKey);
        } else {
          type = 'string';
        }

        const param = {
          name: key,
          type,
          desc: '',
          key: currentKey,
          parentKey,
          children,
        };

        params.push(param);
      });
    }

    return params;
  }, []);

  // 从URL查询参数中提取参数
  const extractParamsFromQuery = useCallback(
    (params?: ArexRESTParam[]): ParamItem[] => {
      if (!params || params.length === 0) {
        return [];
      }

      try {
        // 将查询参数转换为对象格式
        const queryObj: Record<string, any> = {};
        params.forEach(param => {
          if (param.active) {
            try {
              // 尝试解析value为JSON对象
              const value = JSON.parse(param.value);
              queryObj[param.key] = value;
            } catch (e) {
              // 如果解析失败，则保留原始字符串
              queryObj[param.key] = param.value;
            }
          }
        });

        if (Object.keys(queryObj).length === 0) {
          return [];
        }

        return extractParams(queryObj);
      } catch (error) {
        console.error('提取URL查询参数失败:', error);
        return [];
      }
    },
    [extractParams],
  );

  // 从表单数据中提取参数
  const extractParamsFromFormData = useCallback(
    (formData?: { key: string; value: string }[]): ParamItem[] => {
      if (!formData || formData.length === 0) {
        return [];
      }

      try {
        // 将表单数据转换为对象格式
        const formDataObj: Record<string, any> = {};
        formData.forEach(item => {
          try {
            // 尝试解析value为JSON对象
            const value = JSON.parse(item.value);
            formDataObj[item.key] = value;
          } catch (e) {
            // 如果解析失败，则保留原始字符串
            formDataObj[item.key] = item.value;
          }
        });

        if (Object.keys(formDataObj).length === 0) {
          return [];
        }

        return extractParams(formDataObj);
      } catch (error) {
        console.error('提取表单数据失败:', error);
        return [];
      }
    },
    [extractParams],
  );

  // 从请求体中提取参数
  const extractParamsFromBody = useCallback(
    (body?: ArexRESTReqBody, requestType?: string, queryParams?: ArexRESTParam[]): ParamItem[] => {
      if (!body && (!queryParams || queryParams.length === 0)) {
        return [];
      }

      try {
        // 处理SCF类型请求
        if (requestType === 'SCF' && body?.scfRequest && body.scfRequest.params) {
          // 从SCF请求的params中提取数据
          const scfParams = body.scfRequest.params;
          const params: ParamItem[] = [];

          // 为每个SCF参数生成基于数组下标的key
          scfParams.forEach((param, index) => {
            try {
              // 尝试解析exampleValue为JSON对象
              const value = JSON.parse(param.exampleValue as string);
              
              // 使用数组下标作为第一层key
              const baseKey = `[${index}]`;
              
              // 如果值是对象或数组，递归提取子参数
              if (typeof value === 'object' && value !== null) {
                const children = extractParams(value, baseKey);
                params.push({
                   name: param.paramKey,
                   type: Array.isArray(value) ? 'array' : 'object',
                   desc: '',
                   key: baseKey,
                   children,
                 });
              } else {
                // 基本类型参数
                params.push({
                   name: param.paramKey,
                   type: typeof value,
                   desc: '',
                   key: baseKey,
                 });
              }
            } catch (e) {
              // 如果解析失败，则作为字符串处理
              params.push({
                 name: param.paramKey,
                 type: 'string',
                 desc: '',
                 key: `[${index}]`,
               });
            }
          });

          return params;
        }

        // 合并所有参数来源
        let allParams: ParamItem[] = [];

        // 处理URL查询参数
        if (queryParams && queryParams.length > 0) {
          const queryParamItems = extractParamsFromQuery(queryParams);
          allParams = [...allParams, ...queryParamItems];
        }

        // 处理表单数据
        if (body?.formData && body.formData.length > 0) {
          const formDataItems = extractParamsFromFormData(body.formData);
          allParams = [...allParams, ...formDataItems];
        }

        // 以下是原有逻辑，处理其他类型请求的body
        let bodyContent = '';

        if (body?.contentType && body.body) {
          // 处理有 contentType 和 body 的情况
          bodyContent = typeof body.body === 'string' ? body.body : JSON.stringify(body.body);
        } else if (body?.wmb_mock) {
          // 处理 wmb_mock 的情况
          bodyContent =
            typeof body.wmb_mock === 'string' ? body.wmb_mock : JSON.stringify(body.wmb_mock);
        } else if (body?.wmb_Consumer && body.wmb_Consumer.msgBody) {
          // 处理 wmb_Consumer 的情况
          bodyContent =
            typeof body.wmb_Consumer.msgBody === 'string'
              ? body.wmb_Consumer.msgBody
              : JSON.stringify(body.wmb_Consumer.msgBody);
        }

        if (bodyContent) {
          try {
            // 尝试解析JSON
            const parsedBody = JSON.parse(bodyContent);
            const bodyParams = extractParams(parsedBody);
            allParams = [...allParams, ...bodyParams];
          } catch (error) {
            console.error('解析JSON失败:', error);
          }
        }

        return allParams;
      } catch (error) {
        console.error('提取参数失败:', error);
        return [];
      }
    },
    [extractParams, extractParamsFromQuery, extractParamsFromFormData],
  );

  return {
    extractParamsFromBody,
    extractParamsFromQuery,
    extractParamsFromFormData,
  };
};
