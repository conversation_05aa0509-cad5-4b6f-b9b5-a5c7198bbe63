import { useTranslation } from '@arextest/arex-core';
import PM from 'postman-collection';
import { useCallback } from 'react';

import { addUserAction, sendRequest, UserActionEnum } from '../../../helpers';
import { ArexEnvironment, ArexRESTParam, ArexRESTRequest } from '../../../types';
import { useParamsExtractor } from './useParamsExtractor';

// 处理流式请求的函数
const handleStreamingRequest = async (
  request: ArexRESTRequest,
  environment: ArexEnvironment | undefined,
  onBeforeRequest: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest,
  onRequest: ((reqData: { request: ArexRESTRequest; environment?: ArexEnvironment }, resData: any) => void) | undefined,
  dispatch: any
) => {
  try {
    // 初始化流式响应状态
    dispatch((state: any) => {
      state.response = {
        type: 'streaming',
        headers: [],
        statusCode: 0,
        body: [],
        isComplete: false,
        meta: {
          responseSize: 0,
          responseDuration: 0,
        },
      };
    });

    // 发送流式请求
    const res = await sendRequest(onBeforeRequest(request, environment), environment);

    // 如果是流式响应，设置实时更新逻辑
    if (res.response.type === 'streaming') {
      // 立即更新初始状态
      dispatch((state: any) => {
        state.response = { ...res.response };
        state.consoles = res.consoles;
        state.visualizer = res.visualizer;
        state.testResult = res.testResult;
      });

      // 创建一个定时器来检查流式数据更新
      const updateInterval = setInterval(() => {
        dispatch((state: any) => {
          if (state.response.type === 'streaming') {
            // 更新响应数据（创建新的引用以触发重新渲染）
            state.response = {
              ...res.response,
              body: Array.isArray(res.response.body) ? [...res.response.body] : res.response.body, // 创建新的数组引用
            };

            // 如果流式传输完成，清除定时器
            if (res.response.type === 'streaming' && res.response.isComplete) {
              clearInterval(updateInterval);
            }
          }
        });
      }, 100); // 每100ms检查一次更新

      // 设置超时清理
      setTimeout(() => {
        clearInterval(updateInterval);
      }, 300000); // 5分钟超时
    } else {
      // 普通响应直接更新
      dispatch((state: any) => {
        state.response = res.response;
        state.consoles = res.consoles;
        state.visualizer = res.visualizer;
        state.testResult = res.testResult;
      });
    }

    onRequest?.({ request, environment }, res);
  } catch (error) {
    console.error('Streaming request error:', error);
    dispatch((state: any) => {
      state.response = {
        type: 'fail',
        headers: [],
        statusCode: 500,
        body: 'Streaming request failed: ' + (error as Error).message,
        meta: {
          responseSize: 0,
          responseDuration: 0,
        },
      };
    });
  }
};

export interface UseRequestHandlerProps {
  store: any;
  dispatch: any;
  onBeforeRequest: (request: ArexRESTRequest, environment?: ArexEnvironment) => ArexRESTRequest;
  onRequest?: (
    reqData: { request: ArexRESTRequest; environment?: ArexEnvironment },
    resData: Awaited<ReturnType<typeof sendRequest>>,
  ) => void;
  setEndpointStatus: (status?: 'error') => void;
  setShowSmartIcon: (show: boolean) => void;
  iconTimerRef: React.MutableRefObject<NodeJS.Timeout | null>;
}

export const useRequestHandler = ({
  store,
  dispatch,
  onBeforeRequest,
  onRequest,
  setEndpointStatus,
  setShowSmartIcon,
  iconTimerRef,
}: UseRequestHandlerProps) => {
  const { t } = useTranslation();
  // 添加参数提取器
  const { extractParamsFromBody } = useParamsExtractor();

  const handleUrl = useCallback(
    (endpoint: string, params: ArexRESTParam[]) => {
      const newParams: ArexRESTParam[] = [];
      store.request.params.forEach((i: ArexRESTParam) => {
        i?.active && newParams.push(i);
      });

      const url = new PM.Url({
        ...PM.Url.parse(store.request.endpoint),
        query: newParams,
      }).toString();
      return url;
    },
    [store.request.params, store.request.endpoint]
  );

  const handleRequest = async () => {
    void addUserAction(UserActionEnum.SEND_HTTP);
    if (!store.request?.endpoint) {
      setEndpointStatus('error');
      setTimeout(() => {
        setEndpointStatus(undefined);
      }, 3000);
      window.message.error(t('error.emptyEndpoint'));
      return;
    }

    // 在发送请求前重新解析contract并保持desc值
    if (store.request.body) {
      try {
        // 保存当前的contract数据，以便后续恢复描述信息
        const currentContract = store.request.contract ? [...store.request.contract] : [];
        const currentDescriptions: Record<string, string> = {};

        // 提取当前的描述信息
        if (currentContract && currentContract.length > 0) {
          const extractDescriptions = (params: any[]) => {
            params.forEach((param) => {
              if (param.desc) {
                currentDescriptions[param.key] = param.desc;
              }
              if (param.children && param.children.length > 0) {
                extractDescriptions(param.children);
              }
            });
          };

          extractDescriptions(currentContract);
        }

        // 提取新的参数
        const params = extractParamsFromBody(
          store.request.body,
          store.request.requestType,
          store.request.params,
        );
        if (params && params.length > 0) {
          // 恢复描述信息到新提取的参数
          const restoreDescriptions = (paramList: any[]): any[] => {
            return paramList.map((param) => {
              const savedDesc = currentDescriptions[param.key];

              const updatedParam = { ...param };
              if (savedDesc) {
                updatedParam.desc = savedDesc;
              }

              if (param.children && param.children.length > 0) {
                updatedParam.children = restoreDescriptions(param.children);
              }

              return updatedParam;
            });
          };

          const paramsWithDesc = restoreDescriptions(params);

          dispatch((state: any) => {
            state.request.contract = paramsWithDesc;
          });
        }
      } catch (error) {
        console.error('重新解析contract失败:', error);
      }
    }

    dispatch((state: any) => {
      state.response = {
        type: 'loading',
        headers: undefined,
      };
    });

    const requestTmp = { ...store.request };
    requestTmp.params = requestTmp.params.filter((param: ArexRESTParam) => param.active);

    requestTmp.endpoint = handleUrl(store.request.endpoint, store.request.params);

    // 检查是否是流式请求
    const isStreamingRequest = requestTmp.headers?.some(
      (header: any) => header.key.toLowerCase() === 'accept' && header.value.includes('text/event-stream') && header.active
    );

    if (isStreamingRequest) {
      // 处理流式请求
      handleStreamingRequest(requestTmp, store.environment, onBeforeRequest, onRequest, dispatch);
    } else {
      // 处理普通请求
      const res = await sendRequest(onBeforeRequest(requestTmp), store.environment);
      onRequest?.({ request: store.request, environment: store.environment }, res);
      dispatch((state: any) => {
        state.response = res.response;
        state.consoles = res.consoles;
        state.visualizer = res.visualizer;
        state.testResult = res.testResult;
      });
    }

    // 请求成功后的处理已移至发送前，此处不再重复处理contract

    // 请求完成后显示智能图标
    setShowSmartIcon(true);

    // 设置3秒后自动隐藏
    if (iconTimerRef.current) {
      clearTimeout(iconTimerRef.current);
    }

    iconTimerRef.current = setTimeout(() => {
      setShowSmartIcon(false);
    }, 3000);
  };

  return {
    handleRequest,
    handleUrl,
  };
};
