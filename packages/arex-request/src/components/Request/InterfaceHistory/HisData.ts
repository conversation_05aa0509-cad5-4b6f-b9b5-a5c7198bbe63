import { EnvType, ExecuteResult } from './constrants';

export interface ScfRunHistoryListItem {
  historyID: number;
  scfID: number;
  caseName: string;
  operator: string;
  branchName: string;
  status: ExecuteResult;
  costTime: number; // ms
  createTime: string;
  params: string; // json
  responseBody: string; // json
  branchEnv: EnvType;
  scfIP: string;
  scfPort: number;
  scfKey: string;
  expandDetail: {
    // 这个结构真垃圾
    title: string;
    info: string; // json Str
  }[][];
}
