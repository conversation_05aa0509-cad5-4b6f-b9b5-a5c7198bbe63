{"name": "@arextest/arex-request", "version": "0.3.8", "type": "module", "main": "dist/arex-request.js", "module": "dist/arex-request.js", "homepage": "https://github.com/arextest/arex-request", "scripts": {"build:decl": "tsc --project tsconfig.decl.json", "do-build": "vite build && pnpm run build:decl", "do-test": "vitest", "coverage": "vitest run --coverage"}, "types": "./dist/index.d.ts", "files": ["dist/*"], "dependencies": {"@ant-design/icons": "^5.2.6", "@arextest/arex-core": "*", "@arextest/monaco-react": "^4.5.1", "allotment": "^1.18.1", "arex-request-runtime": "^1.0.2", "axios": "^1.3.6", "d3": "^7.9.0", "d3-flame-graph": "^4.1.3", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "immer": "^9.0.21", "json-bigint": "^1.0.0", "lodash": "^4.17.15", "marked": "^12.0.0", "mermaid": "^11.6.0", "postman-collection": "^4.1.7", "react-json-view": "^1.21.3", "use-immer": "^0.9.0"}, "devDependencies": {"@babel/core": "^7.21.4", "@emotion/babel-plugin": "^11.10.6", "@svgr/core": "^8.0.0", "@svgr/plugin-jsx": "^8.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/d3": "^7.4.3", "@types/json-bigint": "^1.0.4", "@types/lodash": "^4.14.198", "@types/postman-collection": "^3.5.7", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/testing-library__jest-dom": "^5.14.5", "@vitejs/plugin-react-swc": "^3.3.1", "@vitest/coverage-c8": "^0.31.0", "jsdom": "^22.0.0", "typescript": "^5.3.0", "vite": "^4.2.0", "vite-plugin-dts": "^3.4.0", "vitest": "^0.31.0"}, "peerDependencies": {"@arextest/arex-request-runtime": "^1.0.2", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@monaco-editor/react": "^4.6.0", "antd": "^5.11.0", "i18next": "^22.4.11", "monaco-editor": "^0.39.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.2.0"}}