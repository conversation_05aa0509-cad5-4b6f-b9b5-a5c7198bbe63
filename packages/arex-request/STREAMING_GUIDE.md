# 流式接口（Event-Stream）使用指南

本指南介绍如何在 AREX Request 中使用新添加的流式接口功能。

## 功能概述

流式接口功能允许您发送支持 Server-Sent Events (SSE) 的 HTTP 请求，并实时接收和显示流式数据。这对于聊天机器人、实时数据推送等场景非常有用。

## 如何使用

### 1. 设置请求头

要启用流式功能，您需要在请求头中添加以下配置：

```
Accept: text/event-stream
```

### 2. 请求配置

在 AREX Request 界面中：

1. 选择 HTTP 方法（通常是 POST）
2. 输入接口地址
3. 在 Headers 标签页中添加：
   - Key: `Accept`
   - Value: `text/event-stream`
   - 确保该头部处于激活状态
4. 在 Body 标签页中配置请求体（如果需要）

### 3. 发送请求

点击 "Send" 按钮发送请求。如果检测到流式请求头，系统会：

- 将请求转发到流式代理接口：`http://arex-api.58dns.org/api/sse/proxy`
- 实时显示接收到的数据块
- 在响应区域显示 "Streaming in progress..." 提示

### 4. 查看响应

流式响应会在 Response 区域实时更新：

- 每个数据块会单独显示
- 页面会自动滚动到最新内容
- 传输完成后会显示完整的响应数据

## 技术实现

### 核心组件修改

1. **ArexRESTResponse 类型扩展**
   - 添加了 `streaming` 类型
   - 支持 `body` 为字符串数组
   - 添加了 `isComplete` 状态标识

2. **ResponseBody 组件增强**
   - 支持流式数据的实时渲染
   - 自动滚动到最新内容
   - 显示传输状态提示

3. **请求代理逻辑**
   - 检测 `Accept: text/event-stream` 头部
   - 转发到专用的流式代理接口
   - 保持与原有 API 调用格式的兼容性

4. **流式数据处理**
   - 解析 SSE 格式数据
   - 提取 `data:` 开头的事件流
   - 支持 JSON 和纯文本数据

### 数据流程

```
用户请求 → 检测流式头部 → 转发到代理接口 → 接收流式数据 → 实时更新UI
```

## 示例代码

### 基本流式请求

```typescript
const streamingRequest: ArexRESTRequest = {
  method: 'POST',
  endpoint: 'https://api.example.com/chat',
  headers: [
    {
      key: 'Content-Type',
      value: 'application/json',
      active: true
    },
    {
      key: 'Accept',
      value: 'text/event-stream',
      active: true
    }
  ],
  body: {
    contentType: 'application/json',
    body: JSON.stringify({
      message: 'Hello, world!',
      stream: true
    })
  }
};
```

### 响应数据格式

流式响应的数据格式：

```typescript
interface StreamingResponse {
  type: 'streaming';
  headers: Array<{key: string, value: string}>;
  statusCode: number;
  body: string[]; // 数据块数组
  isComplete: boolean;
  meta: {
    responseSize: number;
    responseDuration: number;
  };
}
```

## 注意事项

1. **代理接口要求**
   - 流式请求会被转发到 `http://arex-api.58dns.org/api/sse/proxy`
   - 请求体格式与调用 `do_quick_run` 接口时保持一致

2. **性能考虑**
   - 流式数据会实时更新，频繁的 DOM 操作可能影响性能
   - 建议合理控制数据更新频率

3. **错误处理**
   - 网络中断会自动停止流式传输
   - 超时设置为 5 分钟

4. **兼容性**
   - 普通 HTTP 请求不受影响
   - 向后兼容现有功能

## 故障排除

### 常见问题

1. **流式请求没有被识别**
   - 检查 `Accept` 头部是否正确设置为 `text/event-stream`
   - 确保该头部处于激活状态

2. **数据没有实时更新**
   - 检查浏览器控制台是否有错误信息
   - 确认代理接口是否可访问

3. **请求超时**
   - 检查网络连接
   - 确认目标接口是否支持流式响应

### 调试建议

1. 打开浏览器开发者工具
2. 查看 Network 标签页中的请求详情
3. 检查 Console 中的错误日志
4. 使用示例代码进行测试

## 更新日志

- **v1.0.0**: 初始版本，支持基本的流式接口功能
  - 添加流式响应类型定义
  - 实现流式数据实时显示
  - 集成流式请求代理逻辑
  - 提供完整的用户界面支持