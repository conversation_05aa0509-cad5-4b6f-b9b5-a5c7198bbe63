import { createProxyMiddleware } from 'http-proxy-middleware';
import history from 'connect-history-api-fallback';
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);

const __dirname = path.dirname(__filename);
const app = express();

//http://10.249.146.115:8090
//http://10.249.146.115:8092
//http://10.249.146.115:8093
// const SERVICE_REPORT_URL = process.env.SERVICE_REPORT_URL || 'http://10.249.146.115:8090';
// const SERVICE_SCHEDULE_URL = process.env.SERVICE_SCHEDULE_URL || 'http://10.249.146.115:8092';
// const SERVICE_STORAGE_URL = process.env.SERVICE_STORAGE_URL || 'http://10.249.146.115:8093';

const SERVICE_REPORT_URL = process.env.SERVICE_REPORT_URL || 'http://arex-api.58dns.org';
const SERVICE_SCHEDULE_URL = process.env.SERVICE_SCHEDULE_URL || 'http://arex-schedule.58dns.org';
const SERVICE_STORAGE_URL = process.env.SERVICE_STORAGE_URL || 'http://arex-storage.58dns.org';
const SERVICE_IAPI_URL = process.env.SERVICE_IAPI_URL || 'http://iapi.58corp.com';
const SERVICE_API_URL = process.env.SERVICE_API_URL || 'http://arex-api.58dns.org';
const SERVICE_BAIZE_URL = process.env.SERVICE_BAIZE_URL || 'http://baizeapi.58corp.com';
const SERVICE_AI_URL = process.env.SERVICE_AI_URL || 'http://arex-api.58dns.org';
const SERVICE_WTRACE_URL = process.env.SERVICE_WTRACE_URL || 'http://wtrace.58corp.com';

app.use(
  '/webApi',
  createProxyMiddleware({
    target: SERVICE_API_URL,
    changeOrigin: true,
    pathRewrite: { '/webApi': '/api' },
  }),
);

app.use(
  '/report',
  createProxyMiddleware({
    target: SERVICE_REPORT_URL,
    changeOrigin: true,
    pathRewrite: { '/report': '/api' },
  }),
);

app.use(
  '/ai',
  createProxyMiddleware({
    target: SERVICE_AI_URL,
    changeOrigin: true,
    pathRewrite: { '/ai': '/api' },
    // 流式响应配置
    buffering: false,
    // 增加超时时间
    proxyTimeout: 180000,
    // 确保代理不会缓冲响应
    onProxyRes: (proxyRes, req, res) => {
      // 禁用压缩
      proxyRes.headers['content-encoding'] = 'identity';
      // 确保不缓存响应
      proxyRes.headers['cache-control'] = 'no-cache, no-store, must-revalidate';
      proxyRes.headers['pragma'] = 'no-cache';
      proxyRes.headers['expires'] = '0';
      // 确保正确传递 Transfer-Encoding 头
      if (proxyRes.headers['transfer-encoding'] === 'chunked') {
        // 保留 chunked 编码
        res.setHeader('X-Accel-Buffering', 'no'); // 特殊头，告诉 Nginx 不要缓冲
      }
      // proxyRes.headers['content-type'] = 'text/event-stream';
      // proxyRes.headers['x-accel-buffering'] = 'no';
      // proxyRes.headers['transfer-encoding'] = 'chunked';
    },
  }),
);

app.use(
  '/schedule',
  createProxyMiddleware({
    target: SERVICE_SCHEDULE_URL,
    changeOrigin: true,
    pathRewrite: { '/schedule': '/api' },
  }),
);

app.use(
  '/storage',
  createProxyMiddleware({
    target: SERVICE_STORAGE_URL,
    changeOrigin: true,
    pathRewrite: { '/storage': '/api' },
  }),
);

app.use(
  '/iapi',
  createProxyMiddleware({
    target: SERVICE_IAPI_URL,
    changeOrigin: true,
    pathRewrite: { '/iapi': '' },
  }),
);

app.use(
  '/baize',
  createProxyMiddleware({
    target: SERVICE_BAIZE_URL,
    changeOrigin: true,
    pathRewrite: { '/baize': '' },
  }),
);

app.use(
  '/wtrace',
  createProxyMiddleware({
    target: SERVICE_WTRACE_URL,
    changeOrigin: true,
    pathRewrite: { '/wtrace': '' },
  }),
);

// version check
app.use(
  '/version/webApi',
  createProxyMiddleware({
    target: SERVICE_API_URL,
    changeOrigin: true,
    pathRewrite: () => SERVICE_API_URL + '/vi/health',
  }),
);
app.use(
  '/version/schedule',
  createProxyMiddleware({
    target: SERVICE_SCHEDULE_URL,
    changeOrigin: true,
    pathRewrite: () => SERVICE_SCHEDULE_URL + '/vi/health',
  }),
);
app.use(
  '/version/storage',
  createProxyMiddleware({
    target: SERVICE_STORAGE_URL,
    changeOrigin: true,
    pathRewrite: () => SERVICE_STORAGE_URL + '/vi/health',
  }),
);

// 健康检查
app.get('/vi/health', (req, res) => {
  res.end(`365ms`);
});
// storage
app.get('/env', (req, res) => {
  res.send({
    SERVICE_API_URL,
    SERVICE_SCHEDULE_URL,
    SERVICE_STORAGE_URL,
  });
});

app.use(history()); // 这里千万要注意，要在static静态资源上面
// 托管静态文件
app.use(express.static(__dirname + '/dist'));

// 监听8001端口-->云平台配置8001
app.listen(8001, function () {
  console.log('hi');
});
