#!/bin/bash
set -e

compile_path=$1
work_space=$2
env_version=$3
curpath=$(pwd)
# 通过Package 获取集群名
Cluster_From_Package_Json=$(cat ./package.json | awk -F "[name]" '/cluster-name/{print $0}' | sed -n "1p" | awk -F'\"' '{print $4}')
echo 'Cluster_From_Package_Json='$Cluster_From_Package_Json
# 静态资源分平台 上传cdn使用
PLATFORM_SITES=('ajk' 'wb')

init() {
    ls -al
    echo "current node version: $1"
   source /root/.bash_profile
    #nvm use $1
	export PATH
    export NODE_HOME=/usr/local/nodejs/v16.16.0
    export PATH=$NODE_HOME/bin:$PATH
    node -v
    npm -v

    # find . -type d -name ".svn" | xargs -t -i rm -rf {}
    # if [[ -d "dist" ]]; then
    #     rm -rf dist
    # fi
    ##
    pnpm run rm
    rm -rf dist
    mkdir dist
    rm -rf all
}

cp_file() {
    if [[ -f $1 ]]; then
        cp $1 $2
    fi
    if [[ -d $1 ]]; then
        cp -r $1 $2
    fi
}

npm_install() {
    source './bin/global.sh'
    #打印调试查看项目目录
    ls -al
    dist1=$(ls | grep -v node_modules | grep -v dist | grep -v start.sh | grep -v stop.sh | grep -v restart.sh | grep -v config_offline | grep -v config_online | grep -v config_stable | grep -v config_sandbox | grep -v bin | grep -v docs | grep -v all)
    echo "显示的文件:"
    echo $dist1
    cp -r $dist1 dist/
    #复制源码文件夹下的隐藏文件
    cp -r $(ls -al | awk -F' ' '{print $9}' | grep -E '^\.[a-zA-Z]') dist/
    # if [[ -f webpack.config.js ]]; then
    #     cp webpack.config.js dist/
    # fi
    if [[ -d "ftp" ]]; then
        rm -rf ftp
    fi

    mkdir ftp
    
    cd dist
    echo "npm_install/dist"
    ls -al

    pnpm install

    #  dist目录包发送到cdn服务器
    pwd
    branchName=$(git branch | awk '$1=="*"{print $2}')
    echo branchName
    pnpm run build-core
    pnpm run build
    # cp_file node_modules dist
    
}

preonline() {
    cd $curpath
    mkdir preonline
    arr=(dist start.sh stop.sh restart.sh global.sh config_online bin)
    for data in ${arr[@]}; do
        cp_file $data preonline
    done

    #cp -r  dist start.sh stop.sh restart.sh config_online bin preonline/
    cd preonline
    echo "preonline/dist"
    ls -al
    mv config_online config
    tar -zcf preonline.tar.gz *
    md5sum preonline.tar.gz >preonline.md5
    cd $curpath && mv preonline/preonline.tar.gz preonline/preonline.md5 ftp/ && rm -rf preonline
}

all() {
    cd $curpath
    ls -al
    mkdir all
    arr=(dist start.sh stop.sh global.sh config_* bin)
    for data in ${arr[@]}; do
        cp_file $data all
    done

    #cp -r  dist start.sh stop.sh restart.sh  config_* bin all/
    cd all
    mkdir config
    # ls -l --color=aut | grep "^d" | awk '{print $NF}' > tmp
    ls -l --color=auto | grep "^d" | awk '{print $NF}' > tmp
    cat tmp | sed -e "/^config$/d" | grep -E "^config_*" > temp1
    echo "begin for line in $(cat temp1); do\n"
    for line in $(cat temp1); do
        envi=$(echo $line | awk -F '_' '{print $NF}')
        mkdir -p $envi
        \cp -r $line/* $envi/
    done
    echo "begin tar -zcf all.tar.gz *"
    #rm -rf tmp temp1 config_*
    tar -zcf all.tar.gz *
    md5sum all.tar.gz >all.md5
    cd $curpath && mv all/all.tar.gz all/all.md5 ftp/ && rm -rf all dist
}

init $env_version
npm_install
# preonline
all
