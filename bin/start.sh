#!/bin/bash

#source /opt/web/ershoufang_house_nuxt/bin/global.sh
source "/opt/web/${WCloud_Cluster}/bin/global.sh"
cd $PROJECT_PATH

init() {
	#判断当前shell环境是否有pm2 对usp后安装pm2的情况 要重新读取profile
	if pm2 -v ;then
		:
	else
		source /etc/profile
	fi

	#获取当前工作目录
	Pwd=$(cd $(dirname $0);pwd)

	#根据当前环境定义 云环境的端口必须是8001，pwd为上一层目录
	if [[ $WCloud_Cluster ]];then
		Pwd=${Pwd}"/.."
		Port='8001'
    	Name=$PM2_APP_NAME
	fi
}

Start() {	

	#获取pm2运行信息
	PMPID=$(cat $HOME/.pm2/pm2.pid 2>/dev/null)
	if ps ax | awk '{print $1}' | grep -E "^$PMPID$" 2>&1 >/dev/null;then
		NodeProcessInfo=$(pm2 status -m |grep "^+---"|awk '{print $2}' 2>/dev/null)
	fi
	for i in $NodeProcessInfo;do
		if [[ $i == $Name ]];then
			echo "当前工程 $i 已运行"
			if pm2 reload $i ;then
					:
			else
				echo "reload 启动失败!"
				exit 1
			fi
			echo "等待2.1秒，系统bind端口"
			sleep 2
			#不仅200，4xx/5xx 其实也是能证明程序已经能响应http请求
			#000是端口没有被监听返回的http code
			HttpCode=$(curl -m5 127.0.0.1:$Port -o /dev/null -s -w %{http_code})
			if [[ $HttpCode == 000 ]];then
				echo "启动失败，指定端口不存在！"
				exit 1
			fi
			#_help
			exit 0
		fi
	done

	#-o -e 配置日志地址不生效，暂时使用默认位置
	# ./dist 目录是约定好的默认目录 删掉-- port $Port参数 没用
	if pm2 start $PM2_PATH --env production   -- WCloud_Env=${WCloud_Env} -- ENV_SITE=${ENV_SITE};then
		:
	else
		echo "启动失败!"
		exit 1
	fi
	echo "等待2秒，系统bind端口"
	sleep 2
	#不仅200，4xx/5xx 其实也是能证明程序已经能响应http请求
	#000是端口没有被监听返回的http code
	HttpCode=$(curl -m5 127.0.0.1:$Port -o /dev/null -s -w %{http_code})
	if [[ $HttpCode == 000 ]];then
		echo "启动失败，指定端口不存在！"
		exit 1
	else
		echo "$Port 端口已存活"
		pm2 show $Name
	fi
}

_help() {
 echo '提示: 可以使用 --port 手工指定启动端口 '
        echo '注意: 如果想在终端管理pm2内的工程，必须在终端先设置 $HOME 变量'
        echo '先 cd 进入工程所在目录，然后 '
        echo 'HOME=$(pwd)'
        echo "NODE_PATH=\"$Pwd/node_modules;$Node/node_modules;$Pwd/dist/node_modules;\" "
        echo "PATH=\"$PATH:$Node/bin:$Node/node_modules/pm2/bin\" "
}

main() {
	if [[ $(whoami) == root ]];then
        	echo "不能在root用户下运行"
        	exit 1
	fi
	init $@
	#sh /home/<USER>/wlog_install.sh install /opt/log/app-*.log ${WCloud_Cluster}
	Start
	#_help
}

main $@
