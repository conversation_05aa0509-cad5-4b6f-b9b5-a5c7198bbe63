#!/bin/bash
# 编译阶段
# 编译阶段没有环境变量可以依赖,只能手动定义
# cdn服务器地址
#CDN_URL='http://pages.corp.anjuke.com/yun/fileupload'
# cdn服务器存放的项目目录
# CDN_SITE='hbg_web_hbgseedh5'

# 启动阶段
# 可以依赖环境变量实现部分自动化
# pm2 启动实例的名称，必须和 ecosystem.config 中的name一致，否则可能会导致stop失败
#if [ !$WCloud_Cluster ]
#then
#  WCloud_Cluster='app_name'
#fi
PM2_APP_NAME=$WCloud_Cluster
# 项目目录
PROJECT_PATH="/opt/web/${PM2_APP_NAME}/"
# pm2 配置路径
PM2_PATH='/opt/web/'${PM2_APP_NAME}'/dist/server.js'

