#!/bin/bash
set -e
source /root/.bash_profile
export PATH
export NODE_HOME=/usr/local/nodejs/v16.16.0
export PATH=$NODE_HOME/bin:$PATH

compile_path=$1
work_space=$2
env_version=$3
curpath=$(pwd)

# 通过Package 获取集群名
Cluster_From_Package_Json=$(cat ./package.json | awk -F "[name]" '/cluster-name/{print $0}' | sed -n "1p" | awk -F'\"' '{print $4}')
echo 'Cluster_From_Package_Json='$Cluster_From_Package_Json

# 静态资源分平台 上传cdn使用
PLATFORM_SITES=('ajk' 'wb')

init() {
    ls -al
    echo "current node version: $1"

    node -v
    npm -v

    echo "---安装开始(pnpm)---"
    pnpm config set _mirror "https://npm.taobao.org/mirrors//"
    pnpm install
    pnpm run build
    ls -all
    echo "---安装结束(pnpm)---"

    rm -rf dist
    mkdir dist
    rm -rf all
}

cp_file() {
    if [[ -f $1 ]]; then
        cp $1 $2
    fi
    if [[ -d $1 ]]; then
        cp -r $1 $2
    fi
}

npm_install() {
    source './bin/global.sh'
    cp -r arex-server/. dist/
    cp -r packages/arex/dist/ dist/dist/

    if [[ -d "ftp" ]]; then
        rm -rf ftp
    fi

    mkdir ftp
    # 删除外层依赖
    rm -rf package.json
    rm -rf yarn.lock
    cd dist

    node -v
    npm -v
    echo "---安装开始(yarn)---"
    pwd
    yarn install --ignore-engines --production
    ls -all
    echo "---安装结束(yarn)---"

    # dist目录包发送到cdn服务器
    # pwd
    # branchName=$(git branch | awk '$1=="*"{print $2}')
    # echo branchName
}

all() {
    cd $curpath
    ls -al
    mkdir all
    arr=(dist start.sh stop.sh global.sh config_* bin)
    for data in ${arr[@]}; do
        cp_file $data all
    done

    #cp -r  dist start.sh stop.sh restart.sh  config_* bin all/
    cd all
    mkdir config
    # ls -l --color=aut | grep "^d" | awk '{print $NF}' > tmp
    ls -l --color=auto | grep "^d" | awk '{print $NF}' > tmp
    cat tmp | sed -e "/^config$/d" | grep -E "^config_*" > temp1
    echo "begin for line in $(cat temp1); do\n"
    for line in $(cat temp1); do
        envi=$(echo $line | awk -F '_' '{print $NF}')
        mkdir -p $envi
        \cp -r $line/* $envi/
    done
    echo "begin tar -zcf all.tar.gz *"
    #rm -rf tmp temp1 config_*
    tar -zcf all.tar.gz *
    md5sum all.tar.gz >all.md5
    cd $curpath && mv all/all.tar.gz all/all.md5 ftp/ #&& rm -rf all dist
}

init $env_version
npm_install
all
