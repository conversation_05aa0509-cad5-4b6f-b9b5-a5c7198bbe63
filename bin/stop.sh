#!/bin/bash

source /opt/web/${WCloud_Cluster}/bin/global.sh

init() {
	Pwd=$(cd $(dirname $0);pwd)
	HOME="$Pwd"
	Node='/opt/web/nodejs/bin/nodejs'
	if [[ -x $Node/bin/node ]];then
		:
	else
		echo "未找到 nodejs 运行环境"
		exit 1
	fi
	export PATH=$PATH:$Node/bin:$Node/node_modules/pm2/bin
	export NODE_PATH="$Pwd/node_modules;$Node/node_modules"
}

Stop() {
	NodeProcessInfo=$(pm2 status -m |grep "^+---"|awk '{print $2}' 2>/dev/null)


	#这里使用了 delete ，而不是 stop 。
	#注意！ 如何想在终端管理pm2里的工程，必须在终端先设置 $HOME 变量！
        for i in $NodeProcessInfo;do
                if [[ $i == $PM2_APP_NAME ]];then
                        if pm2 delete $i ;then
				echo "关闭成功"
				exit 0
			else
				echo "关闭失败"
				exit 1
			fi
                fi
        done
	echo "未找到指定的工程"
}

main() {
	init
	Stop
}

main
