{"extends": ["eslint:recommended", "plugin:react/recommended", "plugin:jsx-a11y/recommended", "plugin:@typescript-eslint/recommended", "plugin:import/typescript", "plugin:react/jsx-runtime", "plugin:prettier/recommended", "prettier"], "env": {"browser": true, "node": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2021, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "import", "simple-import-sort", "jsx-a11y", "react-hooks", "prettier", "@emotion"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "linebreak-style": "off", "comma-dangle": [0, "always-multiline"], "quotes": [0, "double"], "semi": [0, "always"], "space-before-function-paren": [0, "never"], "multiline-ternary": "off", "camelcase": "off", "prettier/prettier": ["warn", {"endOfLine": "auto", "singleQuote": true}], "react/prop-types": "off", "react/display-name": "off", "react/jsx-uses-react": "error", "react/jsx-uses-vars": "error", "react/react-in-jsx-scope": "warn", "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "react/no-unknown-property": ["error", {"ignore": ["css"]}], "jsx-a11y/no-autofocus": "off"}, "ignorePatterns": ["**/*.css", "**/*.md", "**/electron/*.*"]}