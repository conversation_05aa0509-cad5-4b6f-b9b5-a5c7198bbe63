name: Node.js Package

on:
  push:
    branches: [ main,npm-publish ]

jobs:
  install:
    runs-on: ubuntu-latest
    steps:
      - uses: pnpm/action-setup@v2
        with:
          version: 8

  publish-npm:
    needs: [install]
    runs-on: ubuntu-latest
    steps:
      - uses: pnpm/action-setup@v2
        with:
          version: 8
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 18
          registry-url: https://registry.npmjs.org/
      - run: pnpm install
      - run: pnpm run build-core
      - run: pnpm --filter=@arextest/arex-core publish -f --no-git-checks --access=public --filter
        env:
          NODE_AUTH_TOKEN: ${{secrets.NODE_AUTH_TOKEN}}
